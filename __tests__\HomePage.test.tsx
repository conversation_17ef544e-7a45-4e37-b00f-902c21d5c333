import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import HomePage from '../homepage/HomePage'

// Mock the router
const mockPush = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

describe('HomePage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the homepage with hero section', () => {
    render(<HomePage />)
    
    // Check for main heading
    expect(screen.getByText(/The Future of/)).toBeInTheDocument()
    expect(screen.getByText(/Urban Mobility/)).toBeInTheDocument()
    
    // Check for description
    expect(screen.getByText(/One platform for rides, deliveries, micro-mobility/)).toBeInTheDocument()
    
    // Check for CTA buttons
    expect(screen.getByText('Download App')).toBeInTheDocument()
    expect(screen.getByText('Watch Demo')).toBeInTheDocument()
  })

  it('displays animated statistics', async () => {
    render(<HomePage />)
    
    // Wait for stats to be visible
    await waitFor(() => {
      expect(screen.getByText(/Total Rides/)).toBeInTheDocument()
      expect(screen.getByText(/Cities/)).toBeInTheDocument()
      expect(screen.getByText(/Happy Users/)).toBeInTheDocument()
      expect(screen.getByText(/CO₂ Saved/)).toBeInTheDocument()
    })
  })

  it('shows service features correctly', () => {
    render(<HomePage />)
    
    // Check for service cards
    expect(screen.getByText('Smart Ride Sharing')).toBeInTheDocument()
    expect(screen.getByText('Micro-Mobility')).toBeInTheDocument()
    expect(screen.getByText('Delivery Ecosystem')).toBeInTheDocument()
    expect(screen.getByText('Corporate Solutions')).toBeInTheDocument()
    
    // Check for feature descriptions
    expect(screen.getByText(/AI-powered matching with real-time tracking/)).toBeInTheDocument()
    expect(screen.getByText(/Bikes and scooters with IoT tracking/)).toBeInTheDocument()
  })

  it('displays customer testimonials', () => {
    render(<HomePage />)
    
    // Check for testimonial section
    expect(screen.getByText('Trusted by Millions')).toBeInTheDocument()
    
    // Check for specific testimonials
    expect(screen.getByText('Priya Sharma')).toBeInTheDocument()
    expect(screen.getByText('Rajesh Kumar')).toBeInTheDocument()
    expect(screen.getByText('Sarah Chen')).toBeInTheDocument()
    
    // Check for testimonial content
    expect(screen.getByText(/This platform has revolutionized our corporate travel/)).toBeInTheDocument()
  })

  it('shows pricing plans with features', () => {
    render(<HomePage />)
    
    // Check for pricing section
    expect(screen.getByText('Choose Your Plan')).toBeInTheDocument()
    
    // Check for pricing plans
    expect(screen.getByText('Personal')).toBeInTheDocument()
    expect(screen.getByText('Premium')).toBeInTheDocument()
    expect(screen.getByText('Corporate')).toBeInTheDocument()
    
    // Check for pricing
    expect(screen.getByText('Free')).toBeInTheDocument()
    expect(screen.getByText('₹299/month')).toBeInTheDocument()
    expect(screen.getByText('Custom')).toBeInTheDocument()
    
    // Check for popular badge
    expect(screen.getByText('Most Popular')).toBeInTheDocument()
  })

  it('handles feature card interactions', () => {
    render(<HomePage />)
    
    // Find and click on a feature card
    const rideCard = screen.getByText('Smart Ride Sharing').closest('div')
    expect(rideCard).toBeInTheDocument()
    
    if (rideCard) {
      fireEvent.click(rideCard)
      // Feature card should be clickable (no specific action expected in this test)
    }
  })

  it('displays eco-friendly benefits', () => {
    render(<HomePage />)
    
    // Check for eco-friendly section
    expect(screen.getByText('Eco-Friendly')).toBeInTheDocument()
    expect(screen.getByText(/Reduce your carbon footprint/)).toBeInTheDocument()
    
    // Check for AI-powered section
    expect(screen.getByText('AI-Powered')).toBeInTheDocument()
    expect(screen.getByText(/Advanced algorithms optimize routes/)).toBeInTheDocument()
    
    // Check for safety section
    expect(screen.getByText('Safe & Secure')).toBeInTheDocument()
    expect(screen.getByText(/End-to-end encryption/)).toBeInTheDocument()
  })

  it('has working CTA buttons', () => {
    render(<HomePage />)
    
    // Check for main CTA buttons
    const downloadButtons = screen.getAllByText('Download App')
    const getStartedButtons = screen.getAllByText(/Get Started/)
    
    expect(downloadButtons.length).toBeGreaterThan(0)
    expect(getStartedButtons.length).toBeGreaterThan(0)
    
    // Click on a download button
    fireEvent.click(downloadButtons[0])
    // No specific navigation expected in this test
  })

  it('displays trust indicators', () => {
    render(<HomePage />)
    
    // Check for trust indicators in hero section
    expect(screen.getByText(/2,500,000\+/)).toBeInTheDocument() // Total rides
    expect(screen.getByText(/50\+/)).toBeInTheDocument() // Cities
    expect(screen.getByText(/150,000\+/)).toBeInTheDocument() // Happy users
    expect(screen.getByText(/45,000kg/)).toBeInTheDocument() // CO2 saved
  })

  it('shows newsletter signup section', () => {
    render(<HomePage />)
    
    // Check for newsletter section
    expect(screen.getByText('Ready to Transform Your Mobility?')).toBeInTheDocument()
    expect(screen.getByText(/Join millions of users who have already made the switch/)).toBeInTheDocument()
    
    // Check for final CTA buttons
    expect(screen.getByText('Get Started Today')).toBeInTheDocument()
    expect(screen.getByText('Contact Sales')).toBeInTheDocument()
  })

  it('is responsive and mobile-friendly', () => {
    // Test mobile viewport
    global.innerWidth = 375
    global.innerHeight = 667
    global.dispatchEvent(new Event('resize'))
    
    render(<HomePage />)
    
    // Key elements should still be present on mobile
    expect(screen.getByText(/The Future of/)).toBeInTheDocument()
    expect(screen.getByText('Download App')).toBeInTheDocument()
  })

  it('handles error states gracefully', () => {
    // Mock console.error to avoid noise in tests
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    
    // Test with missing image
    render(<HomePage />)
    
    // Component should still render even if images fail to load
    expect(screen.getByText(/The Future of/)).toBeInTheDocument()
    
    consoleSpy.mockRestore()
  })
})
