// Corporate Mobility Solutions & B2B Services Management
import { apmService } from '../monitoring/apm';
import { Location } from '../ai/routeOptimization';

export interface CorporateClient {
  id: string;
  companyName: string;
  industry: string;
  size: 'startup' | 'sme' | 'enterprise' | 'multinational';
  contactInfo: {
    primaryContact: string;
    email: string;
    phone: string;
    address: string;
  };
  billingInfo: {
    billingAddress: string;
    paymentTerms: number; // days
    creditLimit: number;
    taxId: string;
  };
  subscription: {
    plan: 'basic' | 'premium' | 'enterprise' | 'custom';
    startDate: Date;
    endDate: Date;
    monthlyCredits: number;
    usedCredits: number;
    features: string[];
  };
  locations: {
    id: string;
    name: string;
    address: string;
    location: Location;
    type: 'office' | 'warehouse' | 'branch' | 'event_venue';
    isActive: boolean;
  }[];
  employees: {
    id: string;
    name: string;
    email: string;
    department: string;
    role: string;
    approvalLevel: number;
    isActive: boolean;
  }[];
  policies: {
    maxRideValue: number;
    allowedVehicleTypes: string[];
    approvalRequired: boolean;
    approvalThreshold: number;
    allowedHours: { start: string; end: string };
    allowedDays: string[];
    costCenters: string[];
  };
  analytics: {
    totalSpend: number;
    totalRides: number;
    avgRideValue: number;
    topRoutes: { from: string; to: string; count: number }[];
    departmentSpend: { department: string; amount: number }[];
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface CorporateBooking {
  id: string;
  clientId: string;
  employeeId: string;
  type: 'business_travel' | 'employee_transport' | 'event_logistics' | 'airport_transfer' | 'client_pickup';
  priority: 'standard' | 'high' | 'urgent';
  
  // Trip details
  pickupLocation: Location & {
    address: string;
    landmark?: string;
    instructions?: string;
  };
  dropoffLocation: Location & {
    address: string;
    landmark?: string;
    instructions?: string;
  };
  
  // Scheduling
  scheduledTime: Date;
  isRecurring: boolean;
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    days: string[];
    endDate: Date;
  };
  
  // Passenger details
  passengers: {
    name: string;
    phone: string;
    isVip: boolean;
    specialRequirements?: string;
  }[];
  
  // Vehicle requirements
  vehiclePreferences: {
    type: 'economy' | 'premium' | 'luxury' | 'suv' | 'van';
    features: string[]; // 'wifi', 'charging', 'refreshments', 'privacy_screen'
    accessibility: boolean;
  };
  
  // Business context
  purpose: string;
  costCenter: string;
  projectCode?: string;
  clientMeeting?: {
    clientName: string;
    meetingType: string;
    importance: 'low' | 'medium' | 'high' | 'critical';
  };
  
  // Approval workflow
  approvalStatus: 'pending' | 'approved' | 'rejected' | 'auto_approved';
  approvedBy?: string;
  approvalDate?: Date;
  rejectionReason?: string;
  
  // Booking status
  status: 'scheduled' | 'confirmed' | 'driver_assigned' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
  driverId?: string;
  vehicleId?: string;
  
  // Pricing
  pricing: {
    baseFare: number;
    distanceFare: number;
    timeFare: number;
    premiumCharges: number;
    corporateDiscount: number;
    total: number;
  };
  
  // Tracking and feedback
  actualPickupTime?: Date;
  actualDropoffTime?: Date;
  rating?: number;
  feedback?: string;
  
  createdAt: Date;
  updatedAt: Date;
}

export interface EventLogistics {
  id: string;
  clientId: string;
  eventName: string;
  eventType: 'conference' | 'meeting' | 'training' | 'team_building' | 'client_event' | 'product_launch';
  
  // Event details
  venue: Location & {
    name: string;
    address: string;
    capacity: number;
  };
  startDate: Date;
  endDate: Date;
  expectedAttendees: number;
  
  // Transportation requirements
  transportationNeeds: {
    airportPickups: number;
    hotelTransfers: number;
    venueShuttles: number;
    vipTransports: number;
  };
  
  // Logistics plan
  shuttleRoutes: {
    id: string;
    name: string;
    stops: (Location & { name: string; estimatedTime: Date })[];
    frequency: number; // minutes
    capacity: number;
    vehicleType: string;
  }[];
  
  // VIP arrangements
  vipGuests: {
    name: string;
    title: string;
    company: string;
    arrivalDetails: {
      flightNumber?: string;
      arrivalTime: Date;
      pickupLocation: string;
    };
    accommodations: string;
    specialRequirements: string[];
  }[];
  
  // Budget and pricing
  budget: {
    allocated: number;
    estimated: number;
    actual: number;
    breakdown: {
      shuttles: number;
      vipTransports: number;
      airportTransfers: number;
      miscellaneous: number;
    };
  };
  
  status: 'planning' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  coordinatorId: string;
  
  createdAt: Date;
  updatedAt: Date;
}

class CorporateMobilityService {
  private corporateClients: Map<string, CorporateClient> = new Map();
  private corporateBookings: Map<string, CorporateBooking> = new Map();
  private eventLogistics: Map<string, EventLogistics> = new Map();

  constructor() {
    this.initializeCorporateSystem();
  }

  /**
   * Create a new corporate booking
   */
  async createCorporateBooking(bookingData: Partial<CorporateBooking>): Promise<CorporateBooking> {
    const timer = apmService.startTimer('create_corporate_booking');
    
    try {
      const client = this.corporateClients.get(bookingData.clientId!);
      if (!client) {
        throw new Error('Corporate client not found');
      }

      // Check if approval is required
      const estimatedCost = await this.estimateBookingCost(bookingData);
      const requiresApproval = client.policies.approvalRequired && 
                              estimatedCost > client.policies.approvalThreshold;

      const booking: CorporateBooking = {
        id: `corp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        clientId: bookingData.clientId!,
        employeeId: bookingData.employeeId!,
        type: bookingData.type || 'business_travel',
        priority: bookingData.priority || 'standard',
        pickupLocation: bookingData.pickupLocation!,
        dropoffLocation: bookingData.dropoffLocation!,
        scheduledTime: bookingData.scheduledTime || new Date(),
        isRecurring: bookingData.isRecurring || false,
        recurringPattern: bookingData.recurringPattern,
        passengers: bookingData.passengers || [],
        vehiclePreferences: bookingData.vehiclePreferences || {
          type: 'economy',
          features: [],
          accessibility: false,
        },
        purpose: bookingData.purpose || 'Business travel',
        costCenter: bookingData.costCenter || 'General',
        projectCode: bookingData.projectCode,
        clientMeeting: bookingData.clientMeeting,
        approvalStatus: requiresApproval ? 'pending' : 'auto_approved',
        status: 'scheduled',
        pricing: await this.calculateCorporatePricing(bookingData, client),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      this.corporateBookings.set(booking.id, booking);

      // If approval required, send notification
      if (requiresApproval) {
        await this.sendApprovalRequest(booking, client);
      }

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'corporate_booking_created',
        value: booking.pricing.total,
        timestamp: Date.now(),
        properties: {
          clientId: booking.clientId,
          type: booking.type,
          priority: booking.priority,
          requiresApproval,
        },
      });

      timer.end(true);
      return booking;

    } catch (error) {
      timer.end(false);
      console.error('Create corporate booking failed:', error);
      throw error;
    }
  }

  /**
   * Approve or reject a corporate booking
   */
  async processBookingApproval(
    bookingId: string, 
    decision: 'approved' | 'rejected', 
    approverId: string,
    reason?: string
  ): Promise<CorporateBooking> {
    const booking = this.corporateBookings.get(bookingId);
    if (!booking) {
      throw new Error('Booking not found');
    }

    booking.approvalStatus = decision;
    booking.approvedBy = approverId;
    booking.approvalDate = new Date();
    booking.updatedAt = new Date();

    if (decision === 'rejected') {
      booking.status = 'cancelled';
      booking.rejectionReason = reason;
    } else {
      booking.status = 'confirmed';
    }

    // Record metrics
    apmService.recordBusinessMetric({
      event: 'corporate_booking_approval',
      value: decision === 'approved' ? 1 : 0,
      timestamp: Date.now(),
      properties: {
        bookingId,
        decision,
        approverId,
      },
    });

    return booking;
  }

  /**
   * Create event logistics plan
   */
  async createEventLogistics(eventData: Partial<EventLogistics>): Promise<EventLogistics> {
    const timer = apmService.startTimer('create_event_logistics');
    
    try {
      const event: EventLogistics = {
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        clientId: eventData.clientId!,
        eventName: eventData.eventName!,
        eventType: eventData.eventType || 'conference',
        venue: eventData.venue!,
        startDate: eventData.startDate!,
        endDate: eventData.endDate!,
        expectedAttendees: eventData.expectedAttendees || 0,
        transportationNeeds: eventData.transportationNeeds || {
          airportPickups: 0,
          hotelTransfers: 0,
          venueShuttles: 0,
          vipTransports: 0,
        },
        shuttleRoutes: eventData.shuttleRoutes || [],
        vipGuests: eventData.vipGuests || [],
        budget: eventData.budget || {
          allocated: 0,
          estimated: 0,
          actual: 0,
          breakdown: {
            shuttles: 0,
            vipTransports: 0,
            airportTransfers: 0,
            miscellaneous: 0,
          },
        },
        status: 'planning',
        coordinatorId: eventData.coordinatorId!,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Generate shuttle routes if not provided
      if (event.shuttleRoutes.length === 0) {
        event.shuttleRoutes = await this.generateShuttleRoutes(event);
      }

      // Estimate budget
      event.budget.estimated = await this.estimateEventBudget(event);

      this.eventLogistics.set(event.id, event);

      timer.end(true);
      return event;

    } catch (error) {
      timer.end(false);
      console.error('Create event logistics failed:', error);
      throw error;
    }
  }

  /**
   * Get corporate analytics
   */
  async getCorporateAnalytics(clientId?: string): Promise<{
    overview: any;
    bookings: any;
    financial: any;
    efficiency: any;
  }> {
    const bookings = Array.from(this.corporateBookings.values());
    const clients = Array.from(this.corporateClients.values());
    
    // Filter by client if specified
    const filteredBookings = clientId 
      ? bookings.filter(b => b.clientId === clientId)
      : bookings;

    const completedBookings = filteredBookings.filter(b => b.status === 'completed');
    const totalRevenue = completedBookings.reduce((sum, b) => sum + b.pricing.total, 0);
    
    return {
      overview: {
        totalClients: clientId ? 1 : clients.length,
        totalBookings: filteredBookings.length,
        completedBookings: completedBookings.length,
        totalRevenue,
        averageBookingValue: completedBookings.length > 0 ? totalRevenue / completedBookings.length : 0,
      },
      bookings: {
        byType: this.getBookingsByType(filteredBookings),
        byStatus: this.getBookingsByStatus(filteredBookings),
        byPriority: this.getBookingsByPriority(filteredBookings),
        monthlyTrend: this.getMonthlyBookingTrend(filteredBookings),
      },
      financial: {
        revenueByClient: this.getRevenueByClient(filteredBookings),
        costCenterAnalysis: this.getCostCenterAnalysis(filteredBookings),
        savingsFromCorporateRates: this.calculateCorporateSavings(filteredBookings),
      },
      efficiency: {
        approvalTime: this.calculateAverageApprovalTime(filteredBookings),
        utilizationRate: this.calculateUtilizationRate(filteredBookings),
        customerSatisfaction: this.calculateCustomerSatisfaction(filteredBookings),
        onTimePerformance: this.calculateOnTimePerformance(filteredBookings),
      },
    };
  }

  // Private helper methods
  private initializeCorporateSystem(): void {
    // Initialize sample corporate clients
    const sampleClients: CorporateClient[] = [
      {
        id: 'corp_001',
        companyName: 'TechCorp Solutions',
        industry: 'Technology',
        size: 'enterprise',
        contactInfo: {
          primaryContact: 'John Smith',
          email: '<EMAIL>',
          phone: '+91-9876543210',
          address: 'Tech Park, Bangalore',
        },
        billingInfo: {
          billingAddress: 'Tech Park, Bangalore',
          paymentTerms: 30,
          creditLimit: 500000,
          taxId: 'GST123456789',
        },
        subscription: {
          plan: 'enterprise',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          monthlyCredits: 100000,
          usedCredits: 45000,
          features: ['priority_booking', 'dedicated_support', 'analytics', 'bulk_booking'],
        },
        locations: [
          {
            id: 'loc_001',
            name: 'Headquarters',
            address: 'Tech Park, Bangalore',
            location: { latitude: 12.9716, longitude: 77.5946 },
            type: 'office',
            isActive: true,
          },
        ],
        employees: [
          {
            id: 'emp_001',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            department: 'Sales',
            role: 'Manager',
            approvalLevel: 2,
            isActive: true,
          },
        ],
        policies: {
          maxRideValue: 2000,
          allowedVehicleTypes: ['economy', 'premium', 'luxury'],
          approvalRequired: true,
          approvalThreshold: 1000,
          allowedHours: { start: '06:00', end: '22:00' },
          allowedDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
          costCenters: ['Sales', 'Marketing', 'Engineering', 'HR'],
        },
        analytics: {
          totalSpend: 125000,
          totalRides: 450,
          avgRideValue: 278,
          topRoutes: [
            { from: 'Office', to: 'Airport', count: 45 },
            { from: 'Hotel', to: 'Client Office', count: 32 },
          ],
          departmentSpend: [
            { department: 'Sales', amount: 65000 },
            { department: 'Marketing', amount: 35000 },
            { department: 'Engineering', amount: 25000 },
          ],
        },
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date(),
      },
    ];

    sampleClients.forEach(client => {
      this.corporateClients.set(client.id, client);
    });
  }

  private async estimateBookingCost(bookingData: Partial<CorporateBooking>): Promise<number> {
    // Simple cost estimation based on distance and vehicle type
    const distance = this.calculateDistance(
      bookingData.pickupLocation!,
      bookingData.dropoffLocation!
    );
    
    const baseRates = {
      economy: 15,
      premium: 25,
      luxury: 40,
      suv: 30,
      van: 35,
    };
    
    const vehicleType = bookingData.vehiclePreferences?.type || 'economy';
    const ratePerKm = baseRates[vehicleType] || 15;
    
    return Math.round(50 + (distance * ratePerKm)); // Base fare + distance fare
  }

  private async calculateCorporatePricing(
    bookingData: Partial<CorporateBooking>,
    client: CorporateClient
  ): Promise<CorporateBooking['pricing']> {
    const distance = this.calculateDistance(
      bookingData.pickupLocation!,
      bookingData.dropoffLocation!
    );
    
    const baseFare = 50;
    const distanceFare = distance * 15;
    const timeFare = 0; // Would calculate based on time of day
    const premiumCharges = bookingData.vehiclePreferences?.type === 'luxury' ? 100 : 0;
    
    const subtotal = baseFare + distanceFare + timeFare + premiumCharges;
    const corporateDiscount = subtotal * 0.15; // 15% corporate discount
    const total = subtotal - corporateDiscount;
    
    return {
      baseFare,
      distanceFare: Math.round(distanceFare),
      timeFare,
      premiumCharges,
      corporateDiscount: Math.round(corporateDiscount),
      total: Math.round(total),
    };
  }

  private calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private async sendApprovalRequest(booking: CorporateBooking, client: CorporateClient): Promise<void> {
    // In real implementation, would send email/notification to approvers
    console.log(`Approval request sent for booking ${booking.id} to ${client.contactInfo.email}`);
  }

  private async generateShuttleRoutes(event: EventLogistics): Promise<EventLogistics['shuttleRoutes']> {
    // Generate sample shuttle routes based on event details
    return [
      {
        id: 'shuttle_001',
        name: 'Airport to Venue',
        stops: [
          { ...event.venue, name: 'Airport', estimatedTime: new Date(event.startDate.getTime() - 2 * 60 * 60 * 1000) },
          { ...event.venue, name: event.venue.name, estimatedTime: new Date(event.startDate.getTime() - 1 * 60 * 60 * 1000) },
        ],
        frequency: 30,
        capacity: 25,
        vehicleType: 'van',
      },
    ];
  }

  private async estimateEventBudget(event: EventLogistics): Promise<number> {
    const { transportationNeeds } = event;
    
    const costs = {
      airportPickup: 800,
      hotelTransfer: 400,
      venueShuttle: 300,
      vipTransport: 1500,
    };
    
    return (
      transportationNeeds.airportPickups * costs.airportPickup +
      transportationNeeds.hotelTransfers * costs.hotelTransfer +
      transportationNeeds.venueShuttles * costs.venueShuttle +
      transportationNeeds.vipTransports * costs.vipTransport
    );
  }

  private getBookingsByType(bookings: CorporateBooking[]): Record<string, number> {
    return bookings.reduce((acc, booking) => {
      acc[booking.type] = (acc[booking.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getBookingsByStatus(bookings: CorporateBooking[]): Record<string, number> {
    return bookings.reduce((acc, booking) => {
      acc[booking.status] = (acc[booking.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getBookingsByPriority(bookings: CorporateBooking[]): Record<string, number> {
    return bookings.reduce((acc, booking) => {
      acc[booking.priority] = (acc[booking.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getMonthlyBookingTrend(bookings: CorporateBooking[]): { month: string; count: number }[] {
    // Simplified monthly trend calculation
    const monthlyData = bookings.reduce((acc, booking) => {
      const month = booking.createdAt.toISOString().slice(0, 7); // YYYY-MM
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(monthlyData).map(([month, count]) => ({ month, count }));
  }

  private getRevenueByClient(bookings: CorporateBooking[]): { clientId: string; revenue: number }[] {
    const revenueData = bookings
      .filter(b => b.status === 'completed')
      .reduce((acc, booking) => {
        acc[booking.clientId] = (acc[booking.clientId] || 0) + booking.pricing.total;
        return acc;
      }, {} as Record<string, number>);

    return Object.entries(revenueData).map(([clientId, revenue]) => ({ clientId, revenue }));
  }

  private getCostCenterAnalysis(bookings: CorporateBooking[]): { costCenter: string; amount: number }[] {
    const costCenterData = bookings
      .filter(b => b.status === 'completed')
      .reduce((acc, booking) => {
        acc[booking.costCenter] = (acc[booking.costCenter] || 0) + booking.pricing.total;
        return acc;
      }, {} as Record<string, number>);

    return Object.entries(costCenterData).map(([costCenter, amount]) => ({ costCenter, amount }));
  }

  private calculateCorporateSavings(bookings: CorporateBooking[]): number {
    return bookings
      .filter(b => b.status === 'completed')
      .reduce((sum, booking) => sum + booking.pricing.corporateDiscount, 0);
  }

  private calculateAverageApprovalTime(bookings: CorporateBooking[]): number {
    const approvedBookings = bookings.filter(b => b.approvalStatus === 'approved' && b.approvalDate);
    if (approvedBookings.length === 0) return 0;

    const totalTime = approvedBookings.reduce((sum, booking) => {
      const approvalTime = booking.approvalDate!.getTime() - booking.createdAt.getTime();
      return sum + (approvalTime / (1000 * 60 * 60)); // Convert to hours
    }, 0);

    return Math.round(totalTime / approvedBookings.length * 100) / 100;
  }

  private calculateUtilizationRate(bookings: CorporateBooking[]): number {
    const totalBookings = bookings.length;
    const completedBookings = bookings.filter(b => b.status === 'completed').length;
    return totalBookings > 0 ? Math.round((completedBookings / totalBookings) * 100) : 0;
  }

  private calculateCustomerSatisfaction(bookings: CorporateBooking[]): number {
    const ratedBookings = bookings.filter(b => b.rating);
    if (ratedBookings.length === 0) return 0;

    const avgRating = ratedBookings.reduce((sum, b) => sum + b.rating!, 0) / ratedBookings.length;
    return Math.round(avgRating * 100) / 100;
  }

  private calculateOnTimePerformance(bookings: CorporateBooking[]): number {
    const completedBookings = bookings.filter(b => b.status === 'completed' && b.actualPickupTime);
    if (completedBookings.length === 0) return 0;

    const onTimeBookings = completedBookings.filter(b => {
      const scheduledTime = b.scheduledTime.getTime();
      const actualTime = b.actualPickupTime!.getTime();
      return Math.abs(actualTime - scheduledTime) <= 5 * 60 * 1000; // Within 5 minutes
    });

    return Math.round((onTimeBookings.length / completedBookings.length) * 100);
  }
}

}

// Export singleton instance
export const corporateMobilityService = new CorporateMobilityService();
export default corporateMobilityService;
