import redis
import json
import logging
from typing import Any, Optional
import os
import pickle

logger = logging.getLogger(__name__)

class CacheManager:
    def __init__(self):
        self.redis_client = None
        self._connect()
    
    def _connect(self):
        """Connect to Redis"""
        try:
            redis_url = os.getenv('REDIS_URL', 'redis://redis:6379')
            self.redis_client = redis.from_url(redis_url, decode_responses=False)
            
            # Test connection
            self.redis_client.ping()
            logger.info("Connected to Redis successfully")
            
        except Exception as e:
            logger.error(f"Redis connection error: {e}")
            # Use in-memory cache as fallback
            self.redis_client = None
            self._memory_cache = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        try:
            if self.redis_client:
                value = self.redis_client.get(key)
                if value:
                    return pickle.loads(value)
                return None
            else:
                # Fallback to memory cache
                return self._memory_cache.get(key)
                
        except Exception as e:
            logger.error(f"Cache get error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, expire: int = 3600):
        """Set value in cache with expiration"""
        try:
            if self.redis_client:
                serialized_value = pickle.dumps(value)
                self.redis_client.setex(key, expire, serialized_value)
            else:
                # Fallback to memory cache (no expiration in this simple implementation)
                self._memory_cache[key] = value
                
        except Exception as e:
            logger.error(f"Cache set error for key {key}: {e}")
    
    async def delete(self, key: str):
        """Delete key from cache"""
        try:
            if self.redis_client:
                self.redis_client.delete(key)
            else:
                self._memory_cache.pop(key, None)
                
        except Exception as e:
            logger.error(f"Cache delete error for key {key}: {e}")
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            if self.redis_client:
                return bool(self.redis_client.exists(key))
            else:
                return key in self._memory_cache
                
        except Exception as e:
            logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def get_keys(self, pattern: str) -> list:
        """Get all keys matching pattern"""
        try:
            if self.redis_client:
                return [key.decode() for key in self.redis_client.keys(pattern)]
            else:
                # Simple pattern matching for memory cache
                import fnmatch
                return [key for key in self._memory_cache.keys() if fnmatch.fnmatch(key, pattern)]
                
        except Exception as e:
            logger.error(f"Cache get_keys error for pattern {pattern}: {e}")
            return []
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment a counter in cache"""
        try:
            if self.redis_client:
                return self.redis_client.incr(key, amount)
            else:
                current = self._memory_cache.get(key, 0)
                new_value = current + amount
                self._memory_cache[key] = new_value
                return new_value
                
        except Exception as e:
            logger.error(f"Cache increment error for key {key}: {e}")
            return 0
    
    async def set_hash(self, key: str, field: str, value: Any):
        """Set field in hash"""
        try:
            if self.redis_client:
                serialized_value = pickle.dumps(value)
                self.redis_client.hset(key, field, serialized_value)
            else:
                if key not in self._memory_cache:
                    self._memory_cache[key] = {}
                self._memory_cache[key][field] = value
                
        except Exception as e:
            logger.error(f"Cache set_hash error for key {key}, field {field}: {e}")
    
    async def get_hash(self, key: str, field: str) -> Optional[Any]:
        """Get field from hash"""
        try:
            if self.redis_client:
                value = self.redis_client.hget(key, field)
                if value:
                    return pickle.loads(value)
                return None
            else:
                hash_data = self._memory_cache.get(key, {})
                return hash_data.get(field)
                
        except Exception as e:
            logger.error(f"Cache get_hash error for key {key}, field {field}: {e}")
            return None
    
    async def get_all_hash(self, key: str) -> dict:
        """Get all fields from hash"""
        try:
            if self.redis_client:
                hash_data = self.redis_client.hgetall(key)
                return {k.decode(): pickle.loads(v) for k, v in hash_data.items()}
            else:
                return self._memory_cache.get(key, {})
                
        except Exception as e:
            logger.error(f"Cache get_all_hash error for key {key}: {e}")
            return {}
    
    async def add_to_list(self, key: str, value: Any):
        """Add value to list"""
        try:
            if self.redis_client:
                serialized_value = pickle.dumps(value)
                self.redis_client.lpush(key, serialized_value)
            else:
                if key not in self._memory_cache:
                    self._memory_cache[key] = []
                self._memory_cache[key].insert(0, value)
                
        except Exception as e:
            logger.error(f"Cache add_to_list error for key {key}: {e}")
    
    async def get_list(self, key: str, start: int = 0, end: int = -1) -> list:
        """Get list values"""
        try:
            if self.redis_client:
                values = self.redis_client.lrange(key, start, end)
                return [pickle.loads(v) for v in values]
            else:
                list_data = self._memory_cache.get(key, [])
                if end == -1:
                    return list_data[start:]
                else:
                    return list_data[start:end+1]
                
        except Exception as e:
            logger.error(f"Cache get_list error for key {key}: {e}")
            return []
    
    async def trim_list(self, key: str, start: int, end: int):
        """Trim list to specified range"""
        try:
            if self.redis_client:
                self.redis_client.ltrim(key, start, end)
            else:
                if key in self._memory_cache:
                    if end == -1:
                        self._memory_cache[key] = self._memory_cache[key][start:]
                    else:
                        self._memory_cache[key] = self._memory_cache[key][start:end+1]
                
        except Exception as e:
            logger.error(f"Cache trim_list error for key {key}: {e}")
    
    async def set_with_ttl(self, key: str, value: Any, ttl_seconds: int):
        """Set value with time-to-live"""
        await self.set(key, value, ttl_seconds)
    
    async def get_ttl(self, key: str) -> int:
        """Get time-to-live for key"""
        try:
            if self.redis_client:
                return self.redis_client.ttl(key)
            else:
                # Memory cache doesn't support TTL in this simple implementation
                return -1
                
        except Exception as e:
            logger.error(f"Cache get_ttl error for key {key}: {e}")
            return -1
    
    async def flush_pattern(self, pattern: str):
        """Delete all keys matching pattern"""
        try:
            keys = await self.get_keys(pattern)
            for key in keys:
                await self.delete(key)
            logger.info(f"Flushed {len(keys)} keys matching pattern {pattern}")
            
        except Exception as e:
            logger.error(f"Cache flush_pattern error for pattern {pattern}: {e}")
    
    async def get_cache_stats(self) -> dict:
        """Get cache statistics"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', '0B'),
                    'keyspace_hits': info.get('keyspace_hits', 0),
                    'keyspace_misses': info.get('keyspace_misses', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0)
                }
            else:
                return {
                    'cache_type': 'memory',
                    'total_keys': len(self._memory_cache),
                    'status': 'fallback'
                }
                
        except Exception as e:
            logger.error(f"Cache stats error: {e}")
            return {'error': str(e)}
    
    def close(self):
        """Close cache connection"""
        if self.redis_client:
            self.redis_client.close()
            logger.info("Cache connection closed")
