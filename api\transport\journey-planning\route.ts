import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { publicTransportService } from '@/lib/transport/publicTransportService';
import { unifiedPaymentService } from '@/lib/transport/unifiedPaymentService';
import { routeOptimizationService } from '@/lib/ai/routeOptimization';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { from, to, preferences = {}, departureTime, arrivalTime } = body;

    // Validate required fields
    if (!from || !to) {
      return NextResponse.json(
        { error: 'Origin and destination locations are required' },
        { status: 400 }
      );
    }

    if (!from.latitude || !from.longitude || !to.latitude || !to.longitude) {
      return NextResponse.json(
        { error: 'Invalid location coordinates' },
        { status: 400 }
      );
    }

    // Set default preferences
    const journeyPreferences = {
      modes: preferences.modes || ['rideshare', 'bus', 'metro', 'walk'],
      maxWalkingDistance: preferences.maxWalkingDistance || 1.0, // 1km
      maxTransfers: preferences.maxTransfers || 2,
      accessibility: preferences.accessibility || false,
      avoidModes: preferences.avoidModes || [],
      prioritize: preferences.prioritize || 'time',
    };

    // Prepare journey planning request
    const journeyRequest = {
      from: {
        latitude: from.latitude,
        longitude: from.longitude,
        address: from.address,
      },
      to: {
        latitude: to.latitude,
        longitude: to.longitude,
        address: to.address,
      },
      departureTime: departureTime ? new Date(departureTime) : undefined,
      arrivalTime: arrivalTime ? new Date(arrivalTime) : undefined,
      preferences: journeyPreferences,
      userId: decoded.userId,
    };

    // Plan multi-modal journey
    const journey = await publicTransportService.planJourney(journeyRequest);

    // Calculate fare for the journey
    const fareSegments = journey.segments.map(segment => ({
      segmentId: segment.mode.id + '_' + Date.now(),
      transportMode: segment.mode.type,
      operator: segment.mode.operator,
      distance: segment.distance,
      duration: segment.duration,
      timeOfDay: segment.startTime,
    }));

    const fareCalculation = await unifiedPaymentService.calculateFare(
      decoded.userId,
      fareSegments
    );

    // Get real-time updates for public transport segments
    const publicTransportSegments = journey.segments.filter(s => s.route);
    const routeIds = publicTransportSegments.map(s => s.route!.id);
    
    let realTimeUpdates = new Map();
    if (routeIds.length > 0) {
      realTimeUpdates = await publicTransportService.getRealTimeUpdates(routeIds);
    }

    // Enhance journey with real-time data
    const enhancedSegments = journey.segments.map(segment => {
      const realTimeData = segment.route ? realTimeUpdates.get(segment.route.id) : undefined;
      
      return {
        ...segment,
        realTimeUpdates: realTimeData,
        pricing: {
          amount: fareCalculation.segments.find(f => 
            f.transportMode === segment.mode.type && 
            f.operator === segment.mode.operator
          )?.amount || 0,
          fareType: segment.mode.type === 'rideshare' ? 'dynamic' : 'fixed',
        },
        sustainability: {
          carbonFootprint: segment.carbonFootprint,
          ecoFriendly: ['bus', 'metro', 'train', 'bike', 'walk'].includes(segment.mode.type),
        },
        accessibility: {
          wheelchairAccessible: segment.mode.accessibility.wheelchairAccessible,
          audioAnnouncements: segment.mode.accessibility.audioAnnouncements,
          visualAnnouncements: segment.mode.accessibility.visualAnnouncements,
        },
      };
    });

    // Generate journey alternatives with different priorities
    const alternatives = await Promise.all([
      // Fastest route
      publicTransportService.planJourney({
        ...journeyRequest,
        preferences: { ...journeyPreferences, prioritize: 'time' },
      }),
      // Cheapest route
      publicTransportService.planJourney({
        ...journeyRequest,
        preferences: { ...journeyPreferences, prioritize: 'cost' },
      }),
      // Most eco-friendly route
      publicTransportService.planJourney({
        ...journeyRequest,
        preferences: { ...journeyPreferences, prioritize: 'environment' },
      }),
    ]);

    // Calculate fare for alternatives
    const alternativesWithFare = await Promise.all(
      alternatives.map(async (alt) => {
        const altFareSegments = alt.segments.map(segment => ({
          segmentId: segment.mode.id + '_alt_' + Date.now(),
          transportMode: segment.mode.type,
          operator: segment.mode.operator,
          distance: segment.distance,
          duration: segment.duration,
          timeOfDay: segment.startTime,
        }));

        const altFare = await unifiedPaymentService.calculateFare(
          decoded.userId,
          altFareSegments
        );

        return {
          ...alt,
          totalCost: altFare.total,
          savings: fareCalculation.total - altFare.total,
        };
      })
    );

    // Prepare response
    const response = {
      success: true,
      data: {
        journey: {
          id: journey.id,
          from: journey.fromLocation,
          to: journey.toLocation,
          segments: enhancedSegments,
          summary: {
            totalDuration: journey.totalDuration,
            totalDistance: journey.totalDistance,
            walkingDistance: journey.walkingDistance,
            totalCost: fareCalculation.total,
            carbonFootprint: journey.totalCarbonFootprint,
            transfers: journey.segments.filter(s => s.mode.type !== 'walk').length - 1,
          },
          accessibility: journey.accessibility,
          confidence: journey.confidence,
          lastUpdated: journey.lastUpdated,
        },
        pricing: {
          breakdown: fareCalculation.breakdown,
          savings: fareCalculation.savings,
          total: fareCalculation.total,
          currency: 'INR',
          paymentMethods: ['wallet', 'card', 'upi'],
        },
        alternatives: alternativesWithFare.map(alt => ({
          id: alt.id,
          type: alt.id.includes('time') ? 'fastest' : 
                alt.id.includes('cost') ? 'cheapest' : 'eco-friendly',
          duration: alt.totalDuration,
          cost: alt.totalCost,
          savings: alt.savings,
          carbonFootprint: alt.totalCarbonFootprint,
          segments: alt.segments.length,
          description: alt.id.includes('time') ? 'Fastest route with minimal delays' :
                      alt.id.includes('cost') ? 'Most economical option' :
                      'Environmentally friendly with lowest emissions',
        })),
        recommendations: {
          bestOption: journey.confidence > 0.8 ? 'recommended' : 'alternative',
          tips: generateJourneyTips(journey, fareCalculation),
          sustainability: {
            carbonSaved: calculateCarbonSavings(journey),
            ecoScore: calculateEcoScore(journey),
            incentives: fareCalculation.savings.carbonIncentive > 0 ? 
              `Earn ₹${fareCalculation.savings.carbonIncentive} for choosing eco-friendly transport` : null,
          },
        },
        realTime: {
          hasUpdates: routeIds.length > 0,
          nextUpdate: new Date(Date.now() + 30000), // 30 seconds
          alerts: extractAlerts(realTimeUpdates),
        },
      },
      metadata: {
        requestId: `journey_${Date.now()}`,
        planningTime: Date.now(),
        preferences: journeyPreferences,
        userId: decoded.userId,
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Journey planning API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Journey planning failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Get available transport modes and their status
    const transportModes = publicTransportService.getAllTransportModes();
    
    // Get real-time system status
    const systemStatus = {
      operational: transportModes.map(mode => ({
        mode: mode.type,
        operator: mode.operator,
        status: 'operational', // Would check real status
        lastUpdate: new Date(),
      })),
      alerts: [
        // Would fetch real alerts
      ],
      coverage: {
        cities: ['Bangalore', 'Mumbai', 'Delhi'], // Supported cities
        modes: transportModes.map(m => m.type),
        features: [
          'Real-time updates',
          'Multi-modal planning',
          'Unified payments',
          'Carbon tracking',
          'Accessibility support',
        ],
      },
    };

    const response = {
      success: true,
      data: {
        transportModes,
        systemStatus,
        capabilities: {
          maxWalkingDistance: 2.0, // km
          maxTransfers: 3,
          supportedModes: transportModes.map(m => m.type),
          realTimeUpdates: true,
          accessibilitySupport: true,
          carbonTracking: true,
          unifiedPayments: true,
        },
        pricing: {
          currency: 'INR',
          fareStructure: {
            bus: 'Distance-based with peak hour multiplier',
            metro: 'Zone-based with subscription discounts',
            rideshare: 'Dynamic pricing with surge multiplier',
          },
          discounts: [
            'Multi-modal journey discount (10%)',
            'Subscription savings (up to 50%)',
            'Eco-friendly incentive (₹5 per segment)',
            'Loyalty rewards (5%)',
          ],
        },
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Transport info API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch transport information',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Helper functions
function generateJourneyTips(journey: any, fareCalculation: any): string[] {
  const tips: string[] = [];

  if (journey.walkingDistance > 1) {
    tips.push('Consider comfortable walking shoes for this journey');
  }

  if (fareCalculation.savings.multiModalDiscount > 0) {
    tips.push(`You're saving ₹${fareCalculation.savings.multiModalDiscount} with multi-modal transport`);
  }

  if (journey.segments.some(s => s.mode.type === 'metro')) {
    tips.push('Metro stations have elevators and wheelchair accessibility');
  }

  if (journey.totalCarbonFootprint < 2) {
    tips.push('This is an eco-friendly journey with low carbon emissions');
  }

  return tips;
}

function calculateCarbonSavings(journey: any): number {
  // Compare with car journey emissions
  const carEmissions = journey.totalDistance * 0.2; // kg CO2 per km for car
  return Math.max(0, carEmissions - journey.totalCarbonFootprint);
}

function calculateEcoScore(journey: any): number {
  const ecoModes = ['bus', 'metro', 'train', 'bike', 'walk'];
  const ecoSegments = journey.segments.filter(s => ecoModes.includes(s.mode.type));
  return Math.round((ecoSegments.length / journey.segments.length) * 100);
}

function extractAlerts(realTimeUpdates: Map<string, any>): string[] {
  const alerts: string[] = [];
  
  for (const updates of realTimeUpdates.values()) {
    for (const update of updates) {
      if (update.status === 'delayed') {
        alerts.push(`${update.routeId} is delayed by ${update.delay} minutes`);
      } else if (update.status === 'cancelled') {
        alerts.push(`${update.routeId} service is cancelled`);
      }
    }
  }
  
  return alerts;
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
