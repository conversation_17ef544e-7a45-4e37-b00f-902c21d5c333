import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { MapPin, BikeIcon as MotorbikeIcon, CreditCard, Calendar } from "lucide-react"

export function HowItWorks() {
  return (
    <section className="py-12">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold">How It Works</h2>
        <p className="mt-4 text-muted-foreground max-w-2xl mx-auto">
          Our platform makes it easy to find and share rides in just a few simple steps
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto bg-primary/10 w-12 h-12 flex items-center justify-center rounded-full mb-4">
              <MapPin className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>1. Enter Your Route</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p>Enter your pickup and drop-off locations to find available rides.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto bg-primary/10 w-12 h-12 flex items-center justify-center rounded-full mb-4">
              <MotorbikeIcon className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>2. Choose a Ride</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p>Browse available two-wheelers and select the one that fits your needs.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto bg-primary/10 w-12 h-12 flex items-center justify-center rounded-full mb-4">
              <CreditCard className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>3. Pay Securely</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p>Pay just ₹5 per kilometer through our secure payment system.</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto bg-primary/10 w-12 h-12 flex items-center justify-center rounded-full mb-4">
              <Calendar className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>4. Schedule Regular Rides</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p>Set up daily commutes and save even more on your regular travel.</p>
          </CardContent>
        </Card>
      </div>
    </section>
  )
}

