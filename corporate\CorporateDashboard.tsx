'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  Building2, 
  Users, 
  TrendingUp, 
  DollarSign, 
  Clock, 
  Star,
  Calendar,
  MapPin,
  BarChart3,
  PieChart,
  Target,
  AlertTriangle,
  CheckCircle,
  Plus,
  Download,
  Filter,
  Settings
} from 'lucide-react';

interface CorporateDashboardProps {
  userRole?: 'corporate_admin' | 'corporate_user' | 'admin';
  clientId?: string;
}

export default function CorporateDashboard({ 
  userRole = 'corporate_user',
  clientId 
}: CorporateDashboardProps) {
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState<any>(null);
  const [bookings, setBookings] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    loadDashboardData();
  }, [timeRange, clientId]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      // Load analytics (admin only)
      if (userRole === 'corporate_admin' || userRole === 'admin') {
        const analyticsResponse = await fetch(`/api/corporate/analytics?timeRange=${timeRange}${clientId ? `&clientId=${clientId}` : ''}`, {
          headers: { 'Authorization': `Bearer ${token}` },
        });
        
        if (analyticsResponse.ok) {
          const analyticsData = await analyticsResponse.json();
          setAnalytics(analyticsData.data);
        }
      }

      // Load bookings
      const bookingsResponse = await fetch(`/api/corporate/bookings${clientId ? `?clientId=${clientId}` : ''}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      });
      
      if (bookingsResponse.ok) {
        const bookingsData = await bookingsResponse.json();
        setBookings(bookingsData.data?.bookings || []);
      }

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string): string => {
    const colors = {
      scheduled: 'bg-blue-100 text-blue-800',
      confirmed: 'bg-green-100 text-green-800',
      driver_assigned: 'bg-purple-100 text-purple-800',
      in_progress: 'bg-orange-100 text-orange-800',
      completed: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getApprovalColor = (status: string): string => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      auto_approved: 'bg-blue-100 text-blue-800',
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner className="mr-2" />
        <span>Loading corporate dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Corporate Mobility Dashboard</h1>
          <p className="text-gray-600">
            {userRole === 'corporate_admin' && 'Manage corporate transportation and analytics'}
            {userRole === 'corporate_user' && 'Book and track your business travel'}
            {userRole === 'admin' && 'Monitor all corporate mobility operations'}
          </p>
        </div>
        <div className="flex space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            New Booking
          </Button>
        </div>
      </div>

      {/* Analytics Cards (Admin only) */}
      {analytics && (userRole === 'corporate_admin' || userRole === 'admin') && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                  <p className="text-2xl font-bold">{analytics.overview.totalBookings}</p>
                </div>
                <Building2 className="h-8 w-8 text-blue-600" />
              </div>
              <div className="mt-2 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+15% from last month</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(analytics.overview.totalRevenue)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="mt-2 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">+12% revenue growth</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Booking Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(analytics.overview.averageBookingValue)}</p>
                </div>
                <Target className="h-8 w-8 text-purple-600" />
              </div>
              <div className="mt-2 flex items-center text-sm">
                <BarChart3 className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-blue-600">Industry benchmark: ₹450</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Customer Satisfaction</p>
                  <p className="text-2xl font-bold">{analytics.efficiency.customerSatisfaction}/5</p>
                </div>
                <Star className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="mt-2 flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-green-600">{analytics.efficiency.onTimePerformance}% on-time</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b">
        <nav className="flex space-x-8">
          {['overview', 'bookings', 'analytics', 'reports'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </nav>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Bookings */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Bookings</CardTitle>
                <CardDescription>Latest corporate travel requests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bookings.slice(0, 5).map((booking) => (
                    <div key={booking.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <MapPin className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{booking.purpose}</p>
                            <Badge className={getStatusColor(booking.status)}>
                              {booking.status.replace('_', ' ')}
                            </Badge>
                            <Badge className={getApprovalColor(booking.approvalStatus)}>
                              {booking.approvalStatus.replace('_', ' ')}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">
                            {booking.pickupLocation.address} → {booking.dropoffLocation.address}
                          </p>
                          <div className="flex items-center mt-1 space-x-4">
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 text-gray-500 mr-1" />
                              <span className="text-xs text-gray-500">
                                {new Date(booking.scheduledTime).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <Building2 className="h-4 w-4 text-gray-500 mr-1" />
                              <span className="text-xs text-gray-500">{booking.costCenter}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(booking.pricing.total)}</p>
                        {booking.pricing.corporateDiscount > 0 && (
                          <p className="text-sm text-green-600">
                            Saved: {formatCurrency(booking.pricing.corporateDiscount)}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions & Alerts */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  Book Business Trip
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Recurring
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  Bulk Booking
                </Button>
                {userRole === 'corporate_admin' && (
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Policies
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Alerts */}
            {analytics?.alerts && analytics.alerts.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
                    Alerts
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.alerts.slice(0, 3).map((alert: any, index: number) => (
                      <div key={index} className={`p-3 rounded-lg border-l-4 ${
                        alert.severity === 'high' ? 'border-red-500 bg-red-50' :
                        alert.severity === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                        'border-blue-500 bg-blue-50'
                      }`}>
                        <p className="text-sm font-medium">{alert.message}</p>
                        <p className="text-xs text-gray-600 mt-1">{alert.action}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {activeTab === 'bookings' && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>All Bookings</CardTitle>
                <CardDescription>Manage corporate travel bookings</CardDescription>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  New Booking
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {bookings.map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <MapPin className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{booking.purpose}</p>
                        <Badge className={getStatusColor(booking.status)}>
                          {booking.status.replace('_', ' ')}
                        </Badge>
                        <Badge className={getApprovalColor(booking.approvalStatus)}>
                          {booking.approvalStatus.replace('_', ' ')}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        {booking.pickupLocation.address} → {booking.dropoffLocation.address}
                      </p>
                      <div className="flex items-center mt-1 space-x-4">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-500 mr-1" />
                          <span className="text-xs text-gray-500">
                            {new Date(booking.scheduledTime).toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Building2 className="h-4 w-4 text-gray-500 mr-1" />
                          <span className="text-xs text-gray-500">{booking.costCenter}</span>
                        </div>
                        {booking.vehiclePreferences && (
                          <div className="flex items-center">
                            <span className="text-xs text-gray-500">
                              {booking.vehiclePreferences.type}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(booking.pricing.total)}</p>
                      {booking.pricing.corporateDiscount > 0 && (
                        <p className="text-sm text-green-600">
                          Saved: {formatCurrency(booking.pricing.corporateDiscount)}
                        </p>
                      )}
                      {booking.rating && (
                        <div className="flex items-center mt-1">
                          <Star className="h-4 w-4 text-yellow-500 mr-1" />
                          <span className="text-sm">{booking.rating}</span>
                        </div>
                      )}
                    </div>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'analytics' && analytics && (userRole === 'corporate_admin' || userRole === 'admin') && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Booking Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                  <p>Booking trends chart would be displayed here</p>
                  <p className="text-sm">Integration with charting library needed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Cost Center Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.financial.costCenterAnalysis?.map((item: any, index: number) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm font-medium">{item.costCenter}</span>
                    <div className="text-right">
                      <span className="font-medium">{formatCurrency(item.amount)}</span>
                      <div className="text-xs text-gray-500">{item.count} bookings</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>On-time Performance</span>
                    <span>{analytics.efficiency.onTimePerformance}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${analytics.efficiency.onTimePerformance}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Customer Satisfaction</span>
                    <span>{analytics.efficiency.customerSatisfaction}/5</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(analytics.efficiency.customerSatisfaction / 5) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Utilization Rate</span>
                    <span>{analytics.efficiency.utilizationRate}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-600 h-2 rounded-full" 
                      style={{ width: `${analytics.efficiency.utilizationRate}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.recommendations?.slice(0, 4).map((rec: string, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <p className="text-sm">{rec}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'reports' && (
        <Card>
          <CardHeader>
            <CardTitle>Reports & Exports</CardTitle>
            <CardDescription>Generate detailed reports for analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="h-24 flex-col">
                <BarChart3 className="h-8 w-8 mb-2" />
                <span>Usage Report</span>
              </Button>
              <Button variant="outline" className="h-24 flex-col">
                <DollarSign className="h-8 w-8 mb-2" />
                <span>Cost Analysis</span>
              </Button>
              <Button variant="outline" className="h-24 flex-col">
                <PieChart className="h-8 w-8 mb-2" />
                <span>Department Breakdown</span>
              </Button>
              <Button variant="outline" className="h-24 flex-col">
                <Clock className="h-8 w-8 mb-2" />
                <span>Time Analysis</span>
              </Button>
              <Button variant="outline" className="h-24 flex-col">
                <Star className="h-8 w-8 mb-2" />
                <span>Satisfaction Report</span>
              </Button>
              <Button variant="outline" className="h-24 flex-col">
                <TrendingUp className="h-8 w-8 mb-2" />
                <span>Trend Analysis</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
