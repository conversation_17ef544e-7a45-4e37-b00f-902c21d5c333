import type React from "react"
import type { Metada<PERSON> } from "next"
import "./globals.css"
import { AuthProvider } from "@/lib/contexts/AuthContext"
import Layout from "@/components/layout/Layout"

export const metadata: Metadata = {
  title: "MobilityHub - Multi-Modal Transportation Ecosystem",
  description: "India's leading platform for rides, delivery, micro-mobility, and corporate travel. Smart, sustainable, and seamless urban mobility solutions.",
  keywords: "ride sharing, food delivery, bike rental, corporate travel, urban mobility, transportation, India",
  generator: 'MobilityHub v2.0',
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#3B82F6',
  openGraph: {
    title: 'MobilityHub - The Future of Urban Mobility',
    description: 'One platform for all your transportation needs. Rides, deliveries, micro-mobility, and corporate solutions.',
    type: 'website',
    locale: 'en_IN',
    siteName: 'MobilityHub',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MobilityHub - Multi-Modal Transportation',
    description: 'Smart, sustainable, and seamless urban mobility solutions.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="MobilityHub" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#3B82F6" />
        <meta name="theme-color" content="#3B82F6" />
      </head>
      <body className="antialiased">
        <AuthProvider>
          <Layout>
            {children}
          </Layout>
        </AuthProvider>
      </body>
    </html>
  )
}