import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { demandForecastingService } from '@/lib/ai/demandForecasting';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { location, timeWindow, options = {} } = body;

    // Validate required fields
    if (!location || !location.latitude || !location.longitude) {
      return NextResponse.json(
        { error: 'Valid location coordinates are required' },
        { status: 400 }
      );
    }

    if (!timeWindow || !timeWindow.start || !timeWindow.end) {
      return NextResponse.json(
        { error: 'Time window with start and end times is required' },
        { status: 400 }
      );
    }

    // Parse time window
    const startTime = new Date(timeWindow.start);
    const endTime = new Date(timeWindow.end);

    if (isNaN(startTime.getTime()) || isNaN(endTime.getTime())) {
      return NextResponse.json(
        { error: 'Invalid time window format' },
        { status: 400 }
      );
    }

    if (startTime >= endTime) {
      return NextResponse.json(
        { error: 'Start time must be before end time' },
        { status: 400 }
      );
    }

    // Limit forecast window to 24 hours
    const maxDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    if (endTime.getTime() - startTime.getTime() > maxDuration) {
      return NextResponse.json(
        { error: 'Forecast window cannot exceed 24 hours' },
        { status: 400 }
      );
    }

    // Get demand forecast
    const forecast = await demandForecastingService.forecastDemand(
      {
        latitude: location.latitude,
        longitude: location.longitude,
        address: location.address,
      },
      {
        start: startTime,
        end: endTime,
      },
      {
        includeRecommendations: options.includeRecommendations !== false,
        granularity: options.granularity || 'hourly',
      }
    );

    // Get real-time demand adjustment
    const realTimeAdjustment = await demandForecastingService.getRealTimeDemandAdjustment({
      latitude: location.latitude,
      longitude: location.longitude,
      address: location.address,
    });

    // Format response
    const response = {
      success: true,
      data: {
        forecast: {
          location: forecast.location,
          timeWindow: forecast.timeWindow,
          predictedDemand: forecast.predictedDemand,
          confidence: forecast.confidence,
          factors: forecast.factors,
        },
        realTime: {
          currentDemand: realTimeAdjustment.currentDemand,
          adjustment: realTimeAdjustment.adjustment,
          trend: realTimeAdjustment.trend,
        },
        recommendations: forecast.recommendations,
        insights: {
          demandLevel: forecast.predictedDemand > 20 ? 'high' : 
                      forecast.predictedDemand > 10 ? 'medium' : 'low',
          suggestedActions: generateActionableInsights(forecast, realTimeAdjustment),
          confidenceLevel: forecast.confidence > 0.8 ? 'high' : 
                          forecast.confidence > 0.6 ? 'medium' : 'low',
        },
        metadata: {
          forecastedAt: new Date(),
          validUntil: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
          modelVersion: '1.0',
        },
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Demand forecast API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Demand forecasting failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    try {
      jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const locationsParam = searchParams.get('locations');
    const hoursAhead = parseInt(searchParams.get('hoursAhead') || '2');

    if (!locationsParam) {
      return NextResponse.json(
        { error: 'Locations parameter is required' },
        { status: 400 }
      );
    }

    // Parse locations
    let locations;
    try {
      locations = JSON.parse(locationsParam);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid locations format' },
        { status: 400 }
      );
    }

    if (!Array.isArray(locations) || locations.length === 0) {
      return NextResponse.json(
        { error: 'Locations must be a non-empty array' },
        { status: 400 }
      );
    }

    // Limit number of locations
    if (locations.length > 10) {
      return NextResponse.json(
        { error: 'Maximum 10 locations allowed per request' },
        { status: 400 }
      );
    }

    // Validate location format
    for (const location of locations) {
      if (!location.latitude || !location.longitude) {
        return NextResponse.json(
          { error: 'Each location must have latitude and longitude' },
          { status: 400 }
        );
      }
    }

    // Set time window
    const now = new Date();
    const timeWindow = {
      start: now,
      end: new Date(now.getTime() + hoursAhead * 60 * 60 * 1000),
    };

    // Get forecasts for all locations
    const forecasts = await demandForecastingService.forecastMultipleLocations(
      locations,
      timeWindow
    );

    // Calculate aggregate insights
    const aggregateInsights = {
      totalPredictedDemand: forecasts.reduce((sum, f) => sum + f.predictedDemand, 0),
      averageConfidence: forecasts.reduce((sum, f) => sum + f.confidence, 0) / forecasts.length,
      highDemandAreas: forecasts.filter(f => f.predictedDemand > 20).length,
      recommendedIncentiveZones: forecasts.reduce((sum, f) => sum + f.recommendations.incentiveZones.length, 0),
      recommendedSurgeAreas: forecasts.reduce((sum, f) => sum + f.recommendations.surgeAreas.length, 0),
    };

    const response = {
      success: true,
      data: {
        forecasts: forecasts.map(forecast => ({
          location: forecast.location,
          predictedDemand: forecast.predictedDemand,
          confidence: forecast.confidence,
          demandLevel: forecast.predictedDemand > 20 ? 'high' : 
                      forecast.predictedDemand > 10 ? 'medium' : 'low',
          recommendations: forecast.recommendations,
        })),
        aggregate: aggregateInsights,
        timeWindow,
        metadata: {
          forecastedAt: new Date(),
          locationsCount: forecasts.length,
          hoursAhead,
        },
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Multiple location demand forecast API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Multiple location demand forecasting failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

function generateActionableInsights(forecast: any, realTime: any): string[] {
  const insights: string[] = [];

  // High demand insights
  if (forecast.predictedDemand > 20) {
    insights.push('High demand expected - consider positioning more drivers in this area');
    
    if (realTime.trend === 'increasing') {
      insights.push('Demand is currently increasing - implement surge pricing');
    }
  }

  // Low demand insights
  if (forecast.predictedDemand < 5) {
    insights.push('Low demand expected - redirect drivers to higher demand areas');
  }

  // Confidence-based insights
  if (forecast.confidence < 0.6) {
    insights.push('Low forecast confidence - monitor real-time conditions closely');
  }

  // Recommendation-based insights
  if (forecast.recommendations.incentiveZones.length > 0) {
    insights.push(`Consider offering incentives in ${forecast.recommendations.incentiveZones.length} zones`);
  }

  if (forecast.recommendations.surgeAreas.length > 0) {
    insights.push(`Surge pricing recommended for ${forecast.recommendations.surgeAreas.length} areas`);
  }

  // Real-time adjustment insights
  if (realTime.adjustment > 1.2) {
    insights.push('Current demand exceeds forecast - increase driver supply');
  } else if (realTime.adjustment < 0.8) {
    insights.push('Current demand below forecast - optimize driver positioning');
  }

  return insights;
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
