import schedule
import time
import threading
import logging
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import asyncio

logger = logging.getLogger(__name__)

class MLScheduler:
    def __init__(self, demand_service, fleet_service, revenue_service, maintenance_service):
        self.demand_service = demand_service
        self.fleet_service = fleet_service
        self.revenue_service = revenue_service
        self.maintenance_service = maintenance_service
        
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        
        # Setup scheduled jobs
        self._setup_jobs()
    
    def _setup_jobs(self):
        """Setup all scheduled jobs"""
        
        # Demand prediction jobs
        self.scheduler.add_job(
            func=self._run_demand_predictions,
            trigger=CronTrigger(minute='*/30'),  # Every 30 minutes
            id='demand_predictions',
            name='Generate demand predictions',
            replace_existing=True
        )
        
        # Fleet positioning optimization
        self.scheduler.add_job(
            func=self._run_fleet_optimization,
            trigger=CronTrigger(minute='*/15'),  # Every 15 minutes
            id='fleet_optimization',
            name='Optimize fleet positioning',
            replace_existing=True
        )
        
        # Revenue analysis
        self.scheduler.add_job(
            func=self._run_revenue_analysis,
            trigger=CronTrigger(hour='*/6'),  # Every 6 hours
            id='revenue_analysis',
            name='Analyze revenue trends',
            replace_existing=True
        )
        
        # Maintenance predictions
        self.scheduler.add_job(
            func=self._run_maintenance_predictions,
            trigger=CronTrigger(hour=2),  # Daily at 2 AM
            id='maintenance_predictions',
            name='Predict maintenance needs',
            replace_existing=True
        )
        
        # Model retraining jobs
        self.scheduler.add_job(
            func=self._retrain_demand_model,
            trigger=CronTrigger(hour=3, minute=0),  # Daily at 3 AM
            id='retrain_demand_model',
            name='Retrain demand forecasting model',
            replace_existing=True
        )
        
        self.scheduler.add_job(
            func=self._retrain_revenue_model,
            trigger=CronTrigger(day_of_week='sun', hour=4),  # Weekly on Sunday at 4 AM
            id='retrain_revenue_model',
            name='Retrain revenue optimization model',
            replace_existing=True
        )
        
        self.scheduler.add_job(
            func=self._retrain_maintenance_model,
            trigger=CronTrigger(day_of_week='mon', hour=5),  # Weekly on Monday at 5 AM
            id='retrain_maintenance_model',
            name='Retrain maintenance prediction model',
            replace_existing=True
        )
        
        # System health checks
        self.scheduler.add_job(
            func=self._system_health_check,
            trigger=CronTrigger(minute='*/5'),  # Every 5 minutes
            id='system_health_check',
            name='System health monitoring',
            replace_existing=True
        )
        
        # Cache cleanup
        self.scheduler.add_job(
            func=self._cleanup_cache,
            trigger=CronTrigger(hour=1),  # Daily at 1 AM
            id='cache_cleanup',
            name='Clean up expired cache entries',
            replace_existing=True
        )
        
        logger.info("Scheduled jobs configured successfully")
    
    def start(self):
        """Start the scheduler"""
        try:
            self.scheduler.start()
            self.is_running = True
            logger.info("ML Scheduler started successfully")
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
    
    def stop(self):
        """Stop the scheduler"""
        try:
            self.scheduler.shutdown()
            self.is_running = False
            logger.info("ML Scheduler stopped")
        except Exception as e:
            logger.error(f"Failed to stop scheduler: {e}")
    
    def _run_async_job(self, coro):
        """Helper to run async functions in scheduler"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(coro)
            loop.close()
        except Exception as e:
            logger.error(f"Async job execution error: {e}")
    
    def _run_demand_predictions(self):
        """Generate demand predictions for all locations"""
        logger.info("Running scheduled demand predictions...")
        try:
            self._run_async_job(self.demand_service.predict_demand_batch())
            logger.info("Demand predictions completed successfully")
        except Exception as e:
            logger.error(f"Demand prediction job failed: {e}")
    
    def _run_fleet_optimization(self):
        """Run fleet positioning optimization"""
        logger.info("Running scheduled fleet optimization...")
        try:
            # Mock current drivers data - in practice, this would come from the database
            mock_drivers = [
                {
                    'id': f'driver_{i}',
                    'latitude': 19.1136 + (i * 0.01),
                    'longitude': 72.8697 + (i * 0.01),
                    'isAvailable': True,
                    'rating': 4.5 + (i * 0.1),
                    'earningsToday': 300 + (i * 50)
                }
                for i in range(20)
            ]
            
            self._run_async_job(
                self.fleet_service.optimize_positioning(mock_drivers, time_horizon=60)
            )
            logger.info("Fleet optimization completed successfully")
        except Exception as e:
            logger.error(f"Fleet optimization job failed: {e}")
    
    def _run_revenue_analysis(self):
        """Run revenue trend analysis"""
        logger.info("Running scheduled revenue analysis...")
        try:
            self._run_async_job(self.revenue_service.analyze_revenue_trends())
            logger.info("Revenue analysis completed successfully")
        except Exception as e:
            logger.error(f"Revenue analysis job failed: {e}")
    
    def _run_maintenance_predictions(self):
        """Run maintenance predictions for all vehicles"""
        logger.info("Running scheduled maintenance predictions...")
        try:
            self._run_async_job(self.maintenance_service.predict_maintenance_needs())
            logger.info("Maintenance predictions completed successfully")
        except Exception as e:
            logger.error(f"Maintenance prediction job failed: {e}")
    
    def _retrain_demand_model(self):
        """Retrain demand forecasting model"""
        logger.info("Starting demand model retraining...")
        try:
            self._run_async_job(self.demand_service.retrain_model())
            logger.info("Demand model retraining completed")
        except Exception as e:
            logger.error(f"Demand model retraining failed: {e}")
    
    def _retrain_revenue_model(self):
        """Retrain revenue optimization model"""
        logger.info("Starting revenue model retraining...")
        try:
            self._run_async_job(self.revenue_service.retrain_model())
            logger.info("Revenue model retraining completed")
        except Exception as e:
            logger.error(f"Revenue model retraining failed: {e}")
    
    def _retrain_maintenance_model(self):
        """Retrain maintenance prediction model"""
        logger.info("Starting maintenance model retraining...")
        try:
            self._run_async_job(self.maintenance_service.retrain_model())
            logger.info("Maintenance model retraining completed")
        except Exception as e:
            logger.error(f"Maintenance model retraining failed: {e}")
    
    def _system_health_check(self):
        """Perform system health checks"""
        try:
            # Check if all services are responsive
            health_status = {
                'timestamp': datetime.now(),
                'demand_service': 'healthy',
                'fleet_service': 'healthy',
                'revenue_service': 'healthy',
                'maintenance_service': 'healthy',
                'scheduler': 'healthy' if self.is_running else 'stopped'
            }
            
            # Log any issues
            for service, status in health_status.items():
                if status != 'healthy':
                    logger.warning(f"Health check: {service} status is {status}")
            
            # Store health status in cache for monitoring
            self._run_async_job(
                self.demand_service.cache_manager.set(
                    'system_health_status', 
                    health_status, 
                    expire=300
                )
            )
            
        except Exception as e:
            logger.error(f"System health check failed: {e}")
    
    def _cleanup_cache(self):
        """Clean up expired cache entries"""
        logger.info("Running cache cleanup...")
        try:
            # Clean up old prediction results
            patterns_to_clean = [
                'demand_prediction:*',
                'fleet_positioning:*',
                'pricing_optimization:*',
                'maintenance_predictions:*'
            ]
            
            for pattern in patterns_to_clean:
                self._run_async_job(
                    self.demand_service.cache_manager.flush_pattern(pattern)
                )
            
            logger.info("Cache cleanup completed")
        except Exception as e:
            logger.error(f"Cache cleanup failed: {e}")
    
    def get_job_status(self):
        """Get status of all scheduled jobs"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'scheduler_running': self.is_running,
            'total_jobs': len(jobs),
            'jobs': jobs
        }
    
    def trigger_job(self, job_id: str):
        """Manually trigger a specific job"""
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.modify(next_run_time=datetime.now())
                logger.info(f"Triggered job: {job_id}")
                return True
            else:
                logger.warning(f"Job not found: {job_id}")
                return False
        except Exception as e:
            logger.error(f"Failed to trigger job {job_id}: {e}")
            return False
    
    def pause_job(self, job_id: str):
        """Pause a specific job"""
        try:
            self.scheduler.pause_job(job_id)
            logger.info(f"Paused job: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to pause job {job_id}: {e}")
            return False
    
    def resume_job(self, job_id: str):
        """Resume a paused job"""
        try:
            self.scheduler.resume_job(job_id)
            logger.info(f"Resumed job: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to resume job {job_id}: {e}")
            return False
    
    def add_custom_job(self, func, trigger, job_id, name):
        """Add a custom job to the scheduler"""
        try:
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name,
                replace_existing=True
            )
            logger.info(f"Added custom job: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to add custom job {job_id}: {e}")
            return False
    
    def remove_job(self, job_id: str):
        """Remove a job from the scheduler"""
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Removed job: {job_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to remove job {job_id}: {e}")
            return False
