import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { RazorpayService } from '@/lib/services/razorpayService';
import { Payment } from '@/lib/models/Payment';
import connectDB from '@/lib/mongodb';

const WEBHOOK_SECRET = process.env.RAZORPAY_WEBHOOK_SECRET || '';

export async function POST(request: NextRequest) {
  try {
    // Get the raw body
    const body = await request.text();
    const signature = request.headers.get('x-razorpay-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing webhook signature' },
        { status: 400 }
      );
    }

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', WEBHOOK_SECRET)
      .update(body)
      .digest('hex');

    if (expectedSignature !== signature) {
      console.error('Invalid webhook signature');
      return NextResponse.json(
        { error: 'Invalid webhook signature' },
        { status: 400 }
      );
    }

    // Parse the webhook payload
    const event = JSON.parse(body);
    const { event: eventType, payload } = event;

    await connectDB();

    // Handle different webhook events
    switch (eventType) {
      case 'payment.captured':
        await handlePaymentCaptured(payload.payment.entity);
        break;

      case 'payment.failed':
        await handlePaymentFailed(payload.payment.entity);
        break;

      case 'order.paid':
        await handleOrderPaid(payload.order.entity, payload.payment.entity);
        break;

      case 'refund.created':
        await handleRefundCreated(payload.refund.entity);
        break;

      case 'refund.processed':
        await handleRefundProcessed(payload.refund.entity);
        break;

      case 'settlement.processed':
        await handleSettlementProcessed(payload.settlement.entity);
        break;

      default:
        console.log(`Unhandled webhook event: ${eventType}`);
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Webhook processing error:', error);
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentCaptured(paymentData: any) {
  try {
    const payment = await Payment.findOne({ 
      razorpayPaymentId: paymentData.id 
    });

    if (payment) {
      payment.status = 'completed';
      payment.completedAt = new Date();
      payment.addWebhookEvent('payment.captured', paymentData);
      await payment.save();

      console.log(`Payment captured: ${paymentData.id}`);
    }
  } catch (error) {
    console.error('Error handling payment captured:', error);
  }
}

async function handlePaymentFailed(paymentData: any) {
  try {
    const payment = await Payment.findOne({ 
      razorpayOrderId: paymentData.order_id 
    });

    if (payment) {
      payment.status = 'failed';
      payment.failureReason = paymentData.error_description || 'Payment failed';
      payment.addWebhookEvent('payment.failed', paymentData);
      await payment.save();

      console.log(`Payment failed: ${paymentData.id}`);
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handleOrderPaid(orderData: any, paymentData: any) {
  try {
    const payment = await Payment.findOne({ 
      razorpayOrderId: orderData.id 
    });

    if (payment) {
      payment.razorpayPaymentId = paymentData.id;
      payment.status = 'completed';
      payment.completedAt = new Date();
      payment.addWebhookEvent('order.paid', { order: orderData, payment: paymentData });
      await payment.save();

      console.log(`Order paid: ${orderData.id}`);
    }
  } catch (error) {
    console.error('Error handling order paid:', error);
  }
}

async function handleRefundCreated(refundData: any) {
  try {
    const payment = await Payment.findOne({ 
      razorpayPaymentId: refundData.payment_id 
    });

    if (payment) {
      payment.refundId = refundData.id;
      payment.refundAmount = refundData.amount / 100; // Convert paise to rupees
      payment.refundStatus = 'pending';
      payment.addWebhookEvent('refund.created', refundData);
      await payment.save();

      console.log(`Refund created: ${refundData.id}`);
    }
  } catch (error) {
    console.error('Error handling refund created:', error);
  }
}

async function handleRefundProcessed(refundData: any) {
  try {
    const payment = await Payment.findOne({ 
      refundId: refundData.id 
    });

    if (payment) {
      payment.refundStatus = 'processed';
      payment.addWebhookEvent('refund.processed', refundData);
      await payment.save();

      console.log(`Refund processed: ${refundData.id}`);
    }
  } catch (error) {
    console.error('Error handling refund processed:', error);
  }
}

async function handleSettlementProcessed(settlementData: any) {
  try {
    // Update payments with settlement information
    const payments = await Payment.find({ 
      razorpayPaymentId: { $in: settlementData.entity_ids } 
    });

    for (const payment of payments) {
      payment.settlement = {
        settlementId: settlementData.id,
        settledAt: new Date(settlementData.settled_at * 1000),
        settledAmount: settlementData.amount / 100, // Convert paise to rupees
      };
      payment.addWebhookEvent('settlement.processed', settlementData);
      await payment.save();
    }

    console.log(`Settlement processed: ${settlementData.id}`);
  } catch (error) {
    console.error('Error handling settlement processed:', error);
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Razorpay-Signature',
    },
  });
}
