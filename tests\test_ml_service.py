#!/usr/bin/env python3
"""
Comprehensive ML Service Testing Suite
Tests all ML endpoints for accuracy, performance, and reliability
"""

import asyncio
import aiohttp
import json
import time
import pytest
from datetime import datetime, timedelta
import numpy as np
from typing import Dict, List, Any

class MLServiceTester:
    def __init__(self, base_url: str = "http://localhost:8081"):
        self.base_url = base_url
        self.session = None
        self.test_results = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_demand_prediction_accuracy(self):
        """Test demand prediction with various scenarios"""
        print("🔍 Testing Demand Prediction Accuracy...")
        
        test_scenarios = [
            {
                "name": "Peak Hour - BKC",
                "data": {
                    "location": "Bandra Kurla Complex",
                    "latitude": 19.0596,
                    "longitude": 72.8656,
                    "hours_ahead": 2,
                    "include_weather": True
                },
                "expected_range": (15, 50),  # Expected demand range
                "confidence_threshold": 0.85
            },
            {
                "name": "Off-Peak - Suburban",
                "data": {
                    "location": "Andheri East",
                    "latitude": 19.1136,
                    "longitude": 72.8697,
                    "hours_ahead": 4,
                    "include_weather": True
                },
                "expected_range": (5, 20),
                "confidence_threshold": 0.80
            },
            {
                "name": "Weekend - Entertainment District",
                "data": {
                    "location": "Lower Parel",
                    "latitude": 19.0144,
                    "longitude": 72.8310,
                    "hours_ahead": 1,
                    "include_weather": True
                },
                "expected_range": (20, 60),
                "confidence_threshold": 0.88
            }
        ]
        
        accuracy_scores = []
        response_times = []
        
        for scenario in test_scenarios:
            start_time = time.time()
            
            try:
                async with self.session.post(
                    f"{self.base_url}/predict/demand",
                    json=scenario["data"]
                ) as response:
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # Validate response structure
                        assert "predicted_demand" in result
                        assert "confidence" in result
                        assert "factors" in result
                        
                        # Check prediction accuracy
                        predicted_demand = result["predicted_demand"]
                        confidence = result["confidence"]
                        
                        # Validate prediction is within expected range
                        min_expected, max_expected = scenario["expected_range"]
                        is_in_range = min_expected <= predicted_demand <= max_expected
                        
                        # Validate confidence threshold
                        meets_confidence = confidence >= scenario["confidence_threshold"]
                        
                        accuracy_score = 1.0 if (is_in_range and meets_confidence) else 0.0
                        accuracy_scores.append(accuracy_score)
                        
                        print(f"  ✅ {scenario['name']}: Demand={predicted_demand:.1f}, Confidence={confidence:.2f}, Time={response_time:.3f}s")
                        
                    else:
                        print(f"  ❌ {scenario['name']}: HTTP {response.status}")
                        accuracy_scores.append(0.0)
                        
            except Exception as e:
                print(f"  ❌ {scenario['name']}: Error - {e}")
                accuracy_scores.append(0.0)
        
        # Calculate overall metrics
        overall_accuracy = np.mean(accuracy_scores) * 100
        avg_response_time = np.mean(response_times)
        
        self.test_results["demand_prediction"] = {
            "accuracy_percentage": overall_accuracy,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_accuracy": 92.0,
            "target_response_time_ms": 100,
            "passed": overall_accuracy >= 92.0 and avg_response_time <= 0.1
        }
        
        print(f"📊 Demand Prediction Results:")
        print(f"   Accuracy: {overall_accuracy:.1f}% (Target: 92%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <100ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['demand_prediction']['passed'] else '❌ FAILED'}")
        
        return self.test_results["demand_prediction"]
    
    async def test_driver_assignment_algorithm(self):
        """Test driver assignment with mock driver pools"""
        print("\n🚗 Testing Driver Assignment Algorithm...")
        
        # Mock driver data for testing
        mock_drivers = [
            {
                "id": "driver_001",
                "latitude": 19.1136,
                "longitude": 72.8697,
                "rating": 4.8,
                "isAvailable": True,
                "earningsToday": 1200,
                "completedRides": 15
            },
            {
                "id": "driver_002", 
                "latitude": 19.1200,
                "longitude": 72.8750,
                "rating": 4.6,
                "isAvailable": True,
                "earningsToday": 800,
                "completedRides": 10
            },
            {
                "id": "driver_003",
                "latitude": 19.1050,
                "longitude": 72.8600,
                "rating": 4.9,
                "isAvailable": True,
                "earningsToday": 600,
                "completedRides": 8
            }
        ]
        
        test_scenarios = [
            {
                "name": "Close Distance Priority",
                "ride_request": {
                    "pickup_latitude": 19.1140,
                    "pickup_longitude": 72.8700,
                    "destination_latitude": 19.0596,
                    "destination_longitude": 72.8656,
                    "user_rating": 4.5
                },
                "expected_driver": "driver_001"  # Closest driver
            },
            {
                "name": "Earnings Balance Priority",
                "ride_request": {
                    "pickup_latitude": 19.1100,
                    "pickup_longitude": 72.8650,
                    "destination_latitude": 19.0800,
                    "destination_longitude": 72.8800,
                    "user_rating": 4.8
                },
                "expected_driver": "driver_003"  # Lowest earnings
            }
        ]
        
        assignment_success_rate = []
        response_times = []
        
        for scenario in test_scenarios:
            start_time = time.time()
            
            try:
                request_data = {
                    "ride_request": scenario["ride_request"],
                    "available_drivers": mock_drivers
                }
                
                async with self.session.post(
                    f"{self.base_url}/assign/driver",
                    json=request_data
                ) as response:
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # Validate response structure
                        assert "assigned_driver_id" in result
                        assert "assignment_score" in result
                        assert "estimated_pickup_time" in result
                        
                        assigned_driver = result["assigned_driver_id"]
                        assignment_score = result["assignment_score"]
                        
                        # Check if assignment is reasonable (not necessarily exact match)
                        is_valid_assignment = assigned_driver in [d["id"] for d in mock_drivers]
                        has_good_score = assignment_score >= 0.7
                        
                        success = is_valid_assignment and has_good_score
                        assignment_success_rate.append(1.0 if success else 0.0)
                        
                        print(f"  ✅ {scenario['name']}: Driver={assigned_driver}, Score={assignment_score:.2f}, Time={response_time:.3f}s")
                        
                    else:
                        print(f"  ❌ {scenario['name']}: HTTP {response.status}")
                        assignment_success_rate.append(0.0)
                        
            except Exception as e:
                print(f"  ❌ {scenario['name']}: Error - {e}")
                assignment_success_rate.append(0.0)
        
        # Calculate metrics
        overall_success_rate = np.mean(assignment_success_rate) * 100
        avg_response_time = np.mean(response_times)
        
        self.test_results["driver_assignment"] = {
            "success_rate_percentage": overall_success_rate,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_success_rate": 88.0,
            "target_response_time_ms": 50,
            "passed": overall_success_rate >= 88.0 and avg_response_time <= 0.05
        }
        
        print(f"📊 Driver Assignment Results:")
        print(f"   Success Rate: {overall_success_rate:.1f}% (Target: 88%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <50ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['driver_assignment']['passed'] else '❌ FAILED'}")
        
        return self.test_results["driver_assignment"]
    
    async def test_pricing_optimization(self):
        """Test pricing optimization with various demand scenarios"""
        print("\n💰 Testing Pricing Optimization...")
        
        test_scenarios = [
            {
                "name": "High Demand Surge",
                "data": {
                    "base_distance": 15,
                    "current_demand": "high",
                    "weather_condition": "rain",
                    "time_of_day": "peak",
                    "driver_availability": 5
                },
                "expected_multiplier_range": (1.5, 2.5)
            },
            {
                "name": "Normal Conditions",
                "data": {
                    "base_distance": 10,
                    "current_demand": "medium",
                    "weather_condition": "clear",
                    "time_of_day": "normal",
                    "driver_availability": 20
                },
                "expected_multiplier_range": (1.0, 1.3)
            },
            {
                "name": "Low Demand Off-Peak",
                "data": {
                    "base_distance": 8,
                    "current_demand": "low",
                    "weather_condition": "clear",
                    "time_of_day": "off_peak",
                    "driver_availability": 30
                },
                "expected_multiplier_range": (0.8, 1.1)
            }
        ]
        
        pricing_accuracy = []
        response_times = []
        
        for scenario in test_scenarios:
            start_time = time.time()
            
            try:
                async with self.session.post(
                    f"{self.base_url}/optimize/pricing",
                    json=scenario["data"]
                ) as response:
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # Validate response structure
                        assert "base_fare" in result
                        assert "surge_multiplier" in result
                        assert "total_fare" in result
                        assert "driver_earnings" in result
                        
                        surge_multiplier = result["surge_multiplier"]
                        min_expected, max_expected = scenario["expected_multiplier_range"]
                        
                        # Check if pricing is within expected range
                        is_in_range = min_expected <= surge_multiplier <= max_expected
                        pricing_accuracy.append(1.0 if is_in_range else 0.0)
                        
                        print(f"  ✅ {scenario['name']}: Surge={surge_multiplier:.2f}x, Fare=₹{result['total_fare']:.0f}, Time={response_time:.3f}s")
                        
                    else:
                        print(f"  ❌ {scenario['name']}: HTTP {response.status}")
                        pricing_accuracy.append(0.0)
                        
            except Exception as e:
                print(f"  ❌ {scenario['name']}: Error - {e}")
                pricing_accuracy.append(0.0)
        
        # Calculate metrics
        overall_accuracy = np.mean(pricing_accuracy) * 100
        avg_response_time = np.mean(response_times)
        
        self.test_results["pricing_optimization"] = {
            "accuracy_percentage": overall_accuracy,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_accuracy": 85.0,
            "target_response_time_ms": 75,
            "passed": overall_accuracy >= 85.0 and avg_response_time <= 0.075
        }
        
        print(f"📊 Pricing Optimization Results:")
        print(f"   Accuracy: {overall_accuracy:.1f}% (Target: 85%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <75ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['pricing_optimization']['passed'] else '❌ FAILED'}")
        
        return self.test_results["pricing_optimization"]
    
    async def test_predictive_maintenance(self):
        """Test predictive maintenance with simulated vehicle data"""
        print("\n🔧 Testing Predictive Maintenance...")
        
        # Mock vehicle data for testing
        mock_vehicles = [
            {
                "vehicle_id": "VH001",
                "mileage": 45000,
                "age_months": 24,
                "last_service_km": 40000,
                "usage_hours_daily": 8,
                "recent_issues": ["brake_wear", "tire_pressure"]
            },
            {
                "vehicle_id": "VH002", 
                "mileage": 15000,
                "age_months": 8,
                "last_service_km": 10000,
                "usage_hours_daily": 6,
                "recent_issues": []
            }
        ]
        
        maintenance_predictions = []
        response_times = []
        
        for vehicle in mock_vehicles:
            start_time = time.time()
            
            try:
                async with self.session.post(
                    f"{self.base_url}/predict/maintenance",
                    json=vehicle
                ) as response:
                    response_time = time.time() - start_time
                    response_times.append(response_time)
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # Validate response structure
                        assert "vehicle_id" in result
                        assert "health_score" in result
                        assert "maintenance_needed" in result
                        assert "predicted_issues" in result
                        
                        health_score = result["health_score"]
                        maintenance_needed = result["maintenance_needed"]
                        
                        # Validate health score is reasonable
                        is_valid_score = 0 <= health_score <= 100
                        has_logical_prediction = isinstance(maintenance_needed, bool)
                        
                        prediction_quality = 1.0 if (is_valid_score and has_logical_prediction) else 0.0
                        maintenance_predictions.append(prediction_quality)
                        
                        print(f"  ✅ {vehicle['vehicle_id']}: Health={health_score:.1f}%, Maintenance={maintenance_needed}, Time={response_time:.3f}s")
                        
                    else:
                        print(f"  ❌ {vehicle['vehicle_id']}: HTTP {response.status}")
                        maintenance_predictions.append(0.0)
                        
            except Exception as e:
                print(f"  ❌ {vehicle['vehicle_id']}: Error - {e}")
                maintenance_predictions.append(0.0)
        
        # Calculate metrics
        overall_accuracy = np.mean(maintenance_predictions) * 100
        avg_response_time = np.mean(response_times)
        
        self.test_results["predictive_maintenance"] = {
            "accuracy_percentage": overall_accuracy,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_accuracy": 90.0,
            "target_response_time_ms": 200,
            "passed": overall_accuracy >= 90.0 and avg_response_time <= 0.2
        }
        
        print(f"📊 Predictive Maintenance Results:")
        print(f"   Accuracy: {overall_accuracy:.1f}% (Target: 90%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <200ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['predictive_maintenance']['passed'] else '❌ FAILED'}")
        
        return self.test_results["predictive_maintenance"]
    
    async def run_all_tests(self):
        """Run all ML service tests"""
        print("🚀 Starting Comprehensive ML Service Testing...\n")
        
        await self.test_demand_prediction_accuracy()
        await self.test_driver_assignment_algorithm()
        await self.test_pricing_optimization()
        await self.test_predictive_maintenance()
        
        # Generate overall summary
        print("\n" + "="*60)
        print("📋 OVERALL ML SERVICE TEST SUMMARY")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["passed"])
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result["passed"] else "❌ FAILED"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall Success Rate: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        return self.test_results

# Test execution function
async def main():
    async with MLServiceTester() as tester:
        results = await tester.run_all_tests()
        return results

if __name__ == "__main__":
    asyncio.run(main())
