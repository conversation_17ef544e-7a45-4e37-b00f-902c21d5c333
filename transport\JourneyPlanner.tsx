'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Leaf, 
  Navigation, 
  Bus, 
  Train, 
  Car, 
  Bike,
  Walking,
  ArrowRight,
  AlertCircle,
  CheckCircle,
  Settings,
  Zap,
  Users
} from 'lucide-react';

interface JourneyPlannerProps {
  onJourneySelected?: (journey: any) => void;
  defaultFrom?: { latitude: number; longitude: number; address?: string };
  defaultTo?: { latitude: number; longitude: number; address?: string };
}

interface TransportMode {
  type: string;
  icon: React.ComponentType<any>;
  color: string;
  name: string;
}

const transportModes: Record<string, TransportMode> = {
  bus: { type: 'bus', icon: Bus, color: 'text-orange-600', name: 'Bus' },
  metro: { type: 'metro', icon: Train, color: 'text-blue-600', name: 'Metro' },
  rideshare: { type: 'rideshare', icon: Car, color: 'text-green-600', name: 'Ride' },
  bike: { type: 'bike', icon: Bike, color: 'text-purple-600', name: 'Bike' },
  walk: { type: 'walk', icon: Walking, color: 'text-gray-600', name: 'Walk' },
};

export default function JourneyPlanner({ 
  onJourneySelected, 
  defaultFrom, 
  defaultTo 
}: JourneyPlannerProps) {
  const [fromLocation, setFromLocation] = useState(defaultFrom?.address || '');
  const [toLocation, setToLocation] = useState(defaultTo?.address || '');
  const [loading, setLoading] = useState(false);
  const [journey, setJourney] = useState<any>(null);
  const [alternatives, setAlternatives] = useState<any[]>([]);
  const [selectedAlternative, setSelectedAlternative] = useState<string | null>(null);
  const [preferences, setPreferences] = useState({
    prioritize: 'time',
    maxWalkingDistance: 1.0,
    accessibility: false,
    avoidModes: [] as string[],
  });
  const [error, setError] = useState<string | null>(null);

  const planJourney = async () => {
    if (!fromLocation || !toLocation) {
      setError('Please enter both origin and destination');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // For demo purposes, using mock coordinates
      // In real app, would geocode the addresses
      const fromCoords = defaultFrom || { latitude: 12.9716, longitude: 77.5946 };
      const toCoords = defaultTo || { latitude: 12.9784, longitude: 77.6408 };

      const response = await fetch('/api/transport/journey-planning', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          from: {
            ...fromCoords,
            address: fromLocation,
          },
          to: {
            ...toCoords,
            address: toLocation,
          },
          preferences,
          departureTime: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to plan journey');
      }

      const data = await response.json();
      
      if (data.success) {
        setJourney(data.data.journey);
        setAlternatives(data.data.alternatives);
        
        if (onJourneySelected) {
          onJourneySelected(data.data.journey);
        }
      } else {
        throw new Error(data.error || 'Journey planning failed');
      }

    } catch (error) {
      console.error('Journey planning failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to plan journey');
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatDistance = (km: number): string => {
    return km < 1 ? `${Math.round(km * 1000)}m` : `${km.toFixed(1)}km`;
  };

  const getSegmentIcon = (modeType: string) => {
    const mode = transportModes[modeType];
    return mode ? mode.icon : Car;
  };

  const getSegmentColor = (modeType: string) => {
    const mode = transportModes[modeType];
    return mode ? mode.color : 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Journey Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Navigation className="h-5 w-5 mr-2" />
            Plan Your Journey
          </CardTitle>
          <CardDescription>
            Find the best route using multiple transport modes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">From</label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Enter pickup location"
                  value={fromLocation}
                  onChange={(e) => setFromLocation(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">To</label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Enter destination"
                  value={toLocation}
                  onChange={(e) => setToLocation(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Preferences */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant={preferences.prioritize === 'time' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreferences(prev => ({ ...prev, prioritize: 'time' }))}
            >
              <Clock className="h-4 w-4 mr-1" />
              Fastest
            </Button>
            <Button
              variant={preferences.prioritize === 'cost' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreferences(prev => ({ ...prev, prioritize: 'cost' }))}
            >
              <DollarSign className="h-4 w-4 mr-1" />
              Cheapest
            </Button>
            <Button
              variant={preferences.prioritize === 'environment' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreferences(prev => ({ ...prev, prioritize: 'environment' }))}
            >
              <Leaf className="h-4 w-4 mr-1" />
              Eco-friendly
            </Button>
            <Button
              variant={preferences.accessibility ? 'default' : 'outline'}
              size="sm"
              onClick={() => setPreferences(prev => ({ ...prev, accessibility: !prev.accessibility }))}
            >
              <Users className="h-4 w-4 mr-1" />
              Accessible
            </Button>
          </div>

          <Button 
            onClick={planJourney} 
            disabled={loading || !fromLocation || !toLocation}
            className="w-full"
          >
            {loading ? (
              <>
                <LoadingSpinner className="mr-2" />
                Planning Journey...
              </>
            ) : (
              <>
                <Navigation className="h-4 w-4 mr-2" />
                Plan Journey
              </>
            )}
          </Button>

          {error && (
            <div className="flex items-center text-red-600 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Journey Results */}
      {journey && (
        <div className="space-y-4">
          {/* Main Journey */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center text-green-800">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Recommended Route
                </CardTitle>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {Math.round(journey.confidence * 100)}% confidence
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {/* Journey Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <Clock className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                  <div className="text-lg font-semibold">{formatDuration(journey.summary.totalDuration)}</div>
                  <div className="text-sm text-gray-600">Duration</div>
                </div>
                <div className="text-center">
                  <DollarSign className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                  <div className="text-lg font-semibold">₹{journey.summary.totalCost}</div>
                  <div className="text-sm text-gray-600">Total Cost</div>
                </div>
                <div className="text-center">
                  <Navigation className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                  <div className="text-lg font-semibold">{formatDistance(journey.summary.totalDistance)}</div>
                  <div className="text-sm text-gray-600">Distance</div>
                </div>
                <div className="text-center">
                  <Leaf className="h-5 w-5 mx-auto mb-1 text-gray-600" />
                  <div className="text-lg font-semibold">{journey.summary.carbonFootprint.toFixed(1)}kg</div>
                  <div className="text-sm text-gray-600">CO₂</div>
                </div>
              </div>

              {/* Journey Segments */}
              <div className="space-y-3">
                <h4 className="font-medium text-gray-900">Journey Steps</h4>
                {journey.segments.map((segment: any, index: number) => {
                  const SegmentIcon = getSegmentIcon(segment.mode.type);
                  const segmentColor = getSegmentColor(segment.mode.type);
                  
                  return (
                    <div key={index} className="flex items-center space-x-4 p-3 bg-white rounded-lg border">
                      <div className={`p-2 rounded-full bg-gray-100 ${segmentColor}`}>
                        <SegmentIcon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium">{segment.mode.name}</div>
                            <div className="text-sm text-gray-600">
                              {segment.instructions.join(' • ')}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">
                              {formatDuration(segment.duration)}
                            </div>
                            {segment.pricing.amount > 0 && (
                              <div className="text-sm text-gray-600">
                                ₹{segment.pricing.amount}
                              </div>
                            )}
                          </div>
                        </div>
                        
                        {/* Real-time updates */}
                        {segment.realTimeUpdates && segment.realTimeUpdates.length > 0 && (
                          <div className="mt-2 flex items-center text-sm">
                            {segment.realTimeUpdates[0].status === 'delayed' ? (
                              <AlertCircle className="h-4 w-4 mr-1 text-orange-500" />
                            ) : (
                              <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                            )}
                            <span className={
                              segment.realTimeUpdates[0].status === 'delayed' 
                                ? 'text-orange-600' 
                                : 'text-green-600'
                            }>
                              {segment.realTimeUpdates[0].status === 'delayed' 
                                ? `Delayed by ${segment.realTimeUpdates[0].delay} min`
                                : 'On time'
                              }
                            </span>
                          </div>
                        )}
                      </div>
                      {index < journey.segments.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Action Button */}
              <Button 
                className="w-full mt-4" 
                onClick={() => onJourneySelected?.(journey)}
              >
                <Zap className="h-4 w-4 mr-2" />
                Book This Journey
              </Button>
            </CardContent>
          </Card>

          {/* Alternative Routes */}
          {alternatives.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Alternative Routes</CardTitle>
                <CardDescription>
                  Other options for your journey
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {alternatives.map((alt, index) => (
                    <div 
                      key={index}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedAlternative === alt.id 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedAlternative(alt.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="outline">{alt.type}</Badge>
                        {alt.savings > 0 && (
                          <Badge variant="secondary" className="text-green-600">
                            Save ₹{alt.savings}
                          </Badge>
                        )}
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>Duration:</span>
                          <span className="font-medium">{formatDuration(alt.duration)}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Cost:</span>
                          <span className="font-medium">₹{alt.cost}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>CO₂:</span>
                          <span className="font-medium">{alt.carbonFootprint.toFixed(1)}kg</span>
                        </div>
                      </div>
                      <div className="text-xs text-gray-600 mt-2">
                        {alt.description}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
