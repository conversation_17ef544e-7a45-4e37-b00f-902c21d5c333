const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// Reward schema
const rewardSchema = new mongoose.Schema({
  userId: { type: String, required: true, index: true },
  type: { type: String, required: true }, // 'ride_completion', 'referral', 'bonus', 'penalty'
  points: { type: Number, required: true },
  reason: { type: String, required: true },
  metadata: { type: Object, default: {} },
  multiplier: { type: Number, default: 1 },
  createdAt: { type: Date, default: Date.now },
  expiresAt: { type: Date }, // For time-limited rewards
  status: { type: String, enum: ['pending', 'awarded', 'expired'], default: 'awarded' }
});

// User points summary schema
const userPointsSchema = new mongoose.Schema({
  userId: { type: String, required: true, unique: true },
  totalPoints: { type: Number, default: 0 },
  availablePoints: { type: Number, default: 0 },
  usedPoints: { type: Number, default: 0 },
  level: { type: String, default: 'Bronze' },
  streak: { type: Number, default: 0 }, // Consecutive days with rides
  lastRideDate: { type: Date },
  achievements: [{ type: String }],
  updatedAt: { type: Date, default: Date.now }
});

const Reward = mongoose.model('Reward', rewardSchema);
const UserPoints = mongoose.model('UserPoints', userPointsSchema);

// Award points to user
router.post('/award', async (req, res) => {
  const { userId, points, reason, type, metadata, multiplier = 1 } = req.body;
  const io = req.app.get('io');
  const logger = req.app.get('logger');

  try {
    const finalPoints = Math.round(points * multiplier);

    // Create reward record
    const reward = new Reward({
      userId,
      type,
      points: finalPoints,
      reason,
      metadata,
      multiplier
    });

    await reward.save();

    // Update user points summary
    let userPoints = await UserPoints.findOne({ userId });
    if (!userPoints) {
      userPoints = new UserPoints({ userId });
    }

    userPoints.totalPoints += finalPoints;
    userPoints.availablePoints += finalPoints;
    userPoints.updatedAt = new Date();

    // Update level based on total points
    userPoints.level = calculateUserLevel(userPoints.totalPoints);

    // Update streak for ride completions
    if (type === 'ride_completion') {
      const today = new Date();
      const lastRide = userPoints.lastRideDate;
      
      if (lastRide) {
        const daysDiff = Math.floor((today - lastRide) / (1000 * 60 * 60 * 24));
        if (daysDiff === 1) {
          userPoints.streak += 1;
        } else if (daysDiff > 1) {
          userPoints.streak = 1;
        }
      } else {
        userPoints.streak = 1;
      }
      
      userPoints.lastRideDate = today;

      // Award streak bonuses
      if (userPoints.streak % 7 === 0) { // Weekly streak bonus
        const streakBonus = 50;
        await awardStreakBonus(userId, streakBonus, userPoints.streak, io, logger);
      }
    }

    await userPoints.save();

    // Send real-time notification
    io.to(`user_${userId}`).emit('points_awarded', {
      points: finalPoints,
      reason,
      totalPoints: userPoints.totalPoints,
      level: userPoints.level,
      streak: userPoints.streak
    });

    logger.info(`Awarded ${finalPoints} points to user ${userId} for: ${reason}`);

    res.json({
      success: true,
      reward,
      userPoints: {
        totalPoints: userPoints.totalPoints,
        availablePoints: userPoints.availablePoints,
        level: userPoints.level,
        streak: userPoints.streak
      }
    });

  } catch (error) {
    logger.error('Reward award error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user points and rewards history
router.get('/user/:userId', async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  try {
    const userPoints = await UserPoints.findOne({ userId });
    
    const rewards = await Reward.find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .exec();

    const totalRewards = await Reward.countDocuments({ userId });

    // Calculate recent activity
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentPoints = await Reward.aggregate([
      { $match: { userId, createdAt: { $gte: last30Days } } },
      { $group: { _id: null, total: { $sum: '$points' } } }
    ]);

    res.json({
      userPoints: userPoints || { userId, totalPoints: 0, availablePoints: 0, level: 'Bronze' },
      rewards,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalRewards,
        pages: Math.ceil(totalRewards / limit)
      },
      recentActivity: {
        pointsLast30Days: recentPoints[0]?.total || 0
      }
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Redeem points
router.post('/redeem', async (req, res) => {
  const { userId, points, reason, metadata } = req.body;
  const io = req.app.get('io');
  const logger = req.app.get('logger');

  try {
    const userPoints = await UserPoints.findOne({ userId });
    
    if (!userPoints || userPoints.availablePoints < points) {
      return res.status(400).json({ error: 'Insufficient points' });
    }

    // Create redemption record (negative points)
    const redemption = new Reward({
      userId,
      type: 'redemption',
      points: -points,
      reason,
      metadata
    });

    await redemption.save();

    // Update user points
    userPoints.availablePoints -= points;
    userPoints.usedPoints += points;
    userPoints.updatedAt = new Date();

    await userPoints.save();

    // Send notification
    io.to(`user_${userId}`).emit('points_redeemed', {
      points,
      reason,
      availablePoints: userPoints.availablePoints
    });

    logger.info(`User ${userId} redeemed ${points} points for: ${reason}`);

    res.json({
      success: true,
      redemption,
      availablePoints: userPoints.availablePoints
    });

  } catch (error) {
    logger.error('Points redemption error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get leaderboard
router.get('/leaderboard', async (req, res) => {
  const { period = 'all', limit = 10 } = req.query;

  try {
    let matchCondition = {};
    
    if (period === 'week') {
      const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      matchCondition.createdAt = { $gte: weekAgo };
    } else if (period === 'month') {
      const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      matchCondition.createdAt = { $gte: monthAgo };
    }

    let leaderboard;
    
    if (period === 'all') {
      // Use UserPoints for all-time leaderboard
      leaderboard = await UserPoints.find({})
        .sort({ totalPoints: -1 })
        .limit(parseInt(limit))
        .select('userId totalPoints level streak');
    } else {
      // Aggregate rewards for period-based leaderboard
      leaderboard = await Reward.aggregate([
        { $match: matchCondition },
        { $group: { 
          _id: '$userId', 
          totalPoints: { $sum: '$points' },
          ridesCount: { $sum: { $cond: [{ $eq: ['$type', 'ride_completion'] }, 1, 0] } }
        }},
        { $sort: { totalPoints: -1 } },
        { $limit: parseInt(limit) }
      ]);
    }

    res.json({
      leaderboard,
      period,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get available rewards/offers
router.get('/offers', async (req, res) => {
  try {
    const offers = [
      {
        id: 'ride_discount_10',
        title: '₹10 Off Next Ride',
        description: 'Get ₹10 discount on your next ride',
        pointsCost: 100,
        type: 'ride_discount',
        value: 10,
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      },
      {
        id: 'free_ride_small',
        title: 'Free Ride (Up to 5km)',
        description: 'Get a free ride for distances up to 5km',
        pointsCost: 250,
        type: 'free_ride',
        value: 25,
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      },
      {
        id: 'priority_support',
        title: 'Priority Customer Support',
        description: '24/7 priority customer support for 1 month',
        pointsCost: 500,
        type: 'service_upgrade',
        value: 'priority',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      }
    ];

    res.json({ offers });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Helper function to calculate user level
function calculateUserLevel(totalPoints) {
  if (totalPoints >= 5000) return 'Platinum';
  if (totalPoints >= 2000) return 'Gold';
  if (totalPoints >= 500) return 'Silver';
  return 'Bronze';
}

// Helper function to award streak bonus
async function awardStreakBonus(userId, bonusPoints, streak, io, logger) {
  const streakReward = new Reward({
    userId,
    type: 'streak_bonus',
    points: bonusPoints,
    reason: `${streak}-day streak bonus`,
    metadata: { streak }
  });

  await streakReward.save();

  const userPoints = await UserPoints.findOne({ userId });
  if (userPoints) {
    userPoints.totalPoints += bonusPoints;
    userPoints.availablePoints += bonusPoints;
    await userPoints.save();
  }

  io.to(`user_${userId}`).emit('streak_bonus', {
    points: bonusPoints,
    streak,
    message: `Congratulations! ${streak}-day streak bonus!`
  });

  logger.info(`Awarded ${bonusPoints} streak bonus to user ${userId} for ${streak} days`);
}

module.exports = router;
