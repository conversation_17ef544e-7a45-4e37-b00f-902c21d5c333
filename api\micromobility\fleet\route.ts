import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { fleetManagementService } from '@/lib/micromobility/fleetManagementService';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions (admin or fleet manager)
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const allowedRoles = ['admin', 'fleet_manager', 'operations'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions for fleet management access' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'status';

    let data;

    switch (action) {
      case 'status':
        data = await fleetManagementService.getFleetStatus();
        break;

      case 'redistribute':
        // Get demand hotspots (would come from analytics)
        const demandHotspots = [
          { location: { latitude: 12.9716, longitude: 77.5946 }, demand: 8 },
          { location: { latitude: 12.9784, longitude: 77.6408 }, demand: 12 },
          { location: { latitude: 12.9352, longitude: 77.6245 }, demand: 5 },
        ];
        
        data = await fleetManagementService.redistributeFleet(demandHotspots);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: status, redistribute' },
          { status: 400 }
        );
    }

    // Add additional insights for fleet management
    const insights = generateFleetInsights(data, action);

    const response = {
      success: true,
      data,
      insights,
      metadata: {
        action,
        timestamp: new Date(),
        userRole: user.role,
        permissions: allowedRoles,
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Fleet management API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Fleet management operation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const allowedRoles = ['admin', 'fleet_manager'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions for fleet operations' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, vehicleIds, maintenanceData, redistributionPlan } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'schedule_maintenance':
        if (!vehicleIds || !Array.isArray(vehicleIds)) {
          return NextResponse.json(
            { error: 'vehicleIds array is required for maintenance scheduling' },
            { status: 400 }
          );
        }
        
        result = await scheduleMaintenanceForVehicles(vehicleIds, maintenanceData);
        break;

      case 'execute_redistribution':
        if (!redistributionPlan || !Array.isArray(redistributionPlan)) {
          return NextResponse.json(
            { error: 'redistributionPlan array is required' },
            { status: 400 }
          );
        }
        
        result = await executeRedistributionPlan(redistributionPlan);
        break;

      case 'update_pricing':
        const { pricingUpdates } = body;
        if (!pricingUpdates) {
          return NextResponse.json(
            { error: 'pricingUpdates object is required' },
            { status: 400 }
          );
        }
        
        result = await updateFleetPricing(pricingUpdates);
        break;

      case 'deploy_vehicles':
        const { deploymentPlan } = body;
        if (!deploymentPlan || !Array.isArray(deploymentPlan)) {
          return NextResponse.json(
            { error: 'deploymentPlan array is required' },
            { status: 400 }
          );
        }
        
        result = await deployVehicles(deploymentPlan);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: schedule_maintenance, execute_redistribution, update_pricing, deploy_vehicles' },
          { status: 400 }
        );
    }

    const response = {
      success: true,
      data: result,
      action,
      executedBy: {
        userId: decoded.userId,
        role: user.role,
        timestamp: new Date(),
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Fleet operation API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Fleet operation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Helper functions for fleet operations
async function scheduleMaintenanceForVehicles(vehicleIds: string[], maintenanceData: any) {
  const scheduledMaintenance = vehicleIds.map(vehicleId => ({
    vehicleId,
    type: maintenanceData?.type || 'routine',
    scheduledDate: maintenanceData?.scheduledDate || new Date(Date.now() + 24 * 60 * 60 * 1000),
    priority: maintenanceData?.priority || 'medium',
    estimatedDuration: maintenanceData?.estimatedDuration || 120, // minutes
    estimatedCost: maintenanceData?.estimatedCost || 500, // INR
  }));

  // In a real implementation, this would update the database
  return {
    scheduled: scheduledMaintenance.length,
    maintenanceJobs: scheduledMaintenance,
    estimatedDowntime: scheduledMaintenance.length * 2, // hours
    totalEstimatedCost: scheduledMaintenance.reduce((sum, job) => sum + job.estimatedCost, 0),
  };
}

async function executeRedistributionPlan(redistributionPlan: any[]) {
  const executedRedistributions = redistributionPlan.map(plan => ({
    ...plan,
    status: 'scheduled',
    estimatedTime: 30, // minutes
    assignedOperator: 'OP001', // Would assign actual operator
  }));

  return {
    redistributions: executedRedistributions.length,
    estimatedCompletionTime: Math.max(...executedRedistributions.map(r => r.estimatedTime)),
    operatorsRequired: Math.ceil(executedRedistributions.length / 3),
    plan: executedRedistributions,
  };
}

async function updateFleetPricing(pricingUpdates: any) {
  // In a real implementation, this would update vehicle pricing in the database
  return {
    vehiclesUpdated: pricingUpdates.vehicleIds?.length || 0,
    priceChanges: pricingUpdates.changes || {},
    effectiveDate: pricingUpdates.effectiveDate || new Date(),
    estimatedRevenueImpact: pricingUpdates.estimatedImpact || 0,
  };
}

async function deployVehicles(deploymentPlan: any[]) {
  const deployments = deploymentPlan.map(deployment => ({
    ...deployment,
    status: 'scheduled',
    estimatedDeploymentTime: 45, // minutes
    operatorAssigned: 'OP002',
  }));

  return {
    vehiclesToDeploy: deployments.length,
    estimatedCompletionTime: Math.max(...deployments.map(d => d.estimatedDeploymentTime)),
    deploymentPlan: deployments,
    coverageIncrease: deployments.length * 0.5, // km² coverage increase
  };
}

function generateFleetInsights(data: any, action: string): string[] {
  const insights: string[] = [];

  if (action === 'status') {
    const { summary, utilization, maintenance } = data;

    // Utilization insights
    if (utilization.current < 50) {
      insights.push('Low fleet utilization detected. Consider redistributing vehicles to high-demand areas.');
    } else if (utilization.current > 85) {
      insights.push('High fleet utilization. Consider deploying additional vehicles to meet demand.');
    }

    // Battery insights
    if (summary.avgBatteryLevel < 40) {
      insights.push('Average battery level is low. Increase charging station deployment.');
    }

    // Maintenance insights
    if (maintenance.criticalIssues > 0) {
      insights.push(`${maintenance.criticalIssues} vehicles require immediate maintenance attention.`);
    }

    if (maintenance.queueLength > summary.totalVehicles * 0.1) {
      insights.push('Maintenance queue is growing. Consider increasing maintenance capacity.');
    }

    // Availability insights
    const availabilityRate = (summary.availableVehicles / summary.totalVehicles) * 100;
    if (availabilityRate < 70) {
      insights.push('Vehicle availability is below optimal levels. Review maintenance and charging processes.');
    }
  }

  if (action === 'redistribute') {
    const { redistributions } = data;
    
    if (redistributions.length > 0) {
      insights.push(`${redistributions.length} vehicles recommended for redistribution to optimize coverage.`);
      
      const highPriorityMoves = redistributions.filter(r => r.priority === 'high').length;
      if (highPriorityMoves > 0) {
        insights.push(`${highPriorityMoves} high-priority redistributions identified for immediate action.`);
      }
    } else {
      insights.push('Fleet distribution is currently optimal. No redistributions needed.');
    }
  }

  return insights;
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
