import { NextRequest } from 'next/server'
import { GET, POST } from '../rides/route'
import { jest } from '@jest/globals'

// Mock MongoDB connection
jest.mock('@/lib/mongodb', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue(true),
}))

// Mock Ride model
const mockRide = {
  find: jest.fn(),
  findById: jest.fn(),
  create: jest.fn(),
  findByIdAndUpdate: jest.fn(),
  findByIdAndDelete: jest.fn(),
  save: jest.fn(),
}

jest.mock('@/lib/models/Ride', () => ({
  Ride: mockRide,
}))

// Mock User model
const mockUser = {
  findById: jest.fn(),
}

jest.mock('@/lib/models/User', () => ({
  User: mockUser,
}))

// Mock JWT
jest.mock('jsonwebtoken', () => ({
  verify: jest.fn(),
  sign: jest.fn(),
}))

const jwt = require('jsonwebtoken')

describe('/api/rides', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default mocks
    jwt.verify.mockReturnValue({ userId: 'test-user-id' })
    mockUser.findById.mockResolvedValue({
      _id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'rider',
    })
  })

  describe('GET /api/rides', () => {
    it('should return rides for authenticated user', async () => {
      const mockRides = [
        {
          _id: 'ride1',
          riderId: 'test-user-id',
          status: 'completed',
          pickupLocation: { address: 'Location A' },
          dropoffLocation: { address: 'Location B' },
          pricing: { total: 100 },
        },
        {
          _id: 'ride2',
          riderId: 'test-user-id',
          status: 'scheduled',
          pickupLocation: { address: 'Location C' },
          dropoffLocation: { address: 'Location D' },
          pricing: { total: 150 },
        },
      ]

      mockRide.find.mockResolvedValue(mockRides)

      const request = new NextRequest('http://localhost:3000/api/rides', {
        headers: {
          'Authorization': 'Bearer valid-token',
        },
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.data.rides).toHaveLength(2)
      expect(mockRide.find).toHaveBeenCalledWith({ riderId: 'test-user-id' })
    })

    it('should return 401 for missing authorization', async () => {
      const request = new NextRequest('http://localhost:3000/api/rides')

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Authorization token required')
    })

    it('should return 401 for invalid token', async () => {
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token')
      })

      const request = new NextRequest('http://localhost:3000/api/rides', {
        headers: {
          'Authorization': 'Bearer invalid-token',
        },
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Invalid or expired token')
    })

    it('should filter rides by status', async () => {
      const mockRides = [
        {
          _id: 'ride1',
          riderId: 'test-user-id',
          status: 'completed',
        },
      ]

      mockRide.find.mockResolvedValue(mockRides)

      const request = new NextRequest('http://localhost:3000/api/rides?status=completed', {
        headers: {
          'Authorization': 'Bearer valid-token',
        },
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(mockRide.find).toHaveBeenCalledWith({
        riderId: 'test-user-id',
        status: 'completed',
      })
    })

    it('should handle database errors', async () => {
      mockRide.find.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/rides', {
        headers: {
          'Authorization': 'Bearer valid-token',
        },
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Failed to fetch rides')
    })
  })

  describe('POST /api/rides', () => {
    const validRideData = {
      pickupLocation: {
        address: '123 Test St',
        coordinates: { lat: 40.7128, lng: -74.0060 },
      },
      dropoffLocation: {
        address: '456 Test Ave',
        coordinates: { lat: 40.7589, lng: -73.9851 },
      },
      scheduledTime: '2023-12-01T10:00:00Z',
      vehicleType: 'bike',
      notes: 'Test ride',
    }

    it('should create a new ride successfully', async () => {
      const mockCreatedRide = {
        _id: 'new-ride-id',
        riderId: 'test-user-id',
        ...validRideData,
        status: 'scheduled',
        pricing: { total: 100 },
        createdAt: new Date(),
      }

      mockRide.create.mockResolvedValue(mockCreatedRide)

      const request = new NextRequest('http://localhost:3000/api/rides', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validRideData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data.success).toBe(true)
      expect(data.data.ride._id).toBe('new-ride-id')
      expect(mockRide.create).toHaveBeenCalledWith(
        expect.objectContaining({
          riderId: 'test-user-id',
          ...validRideData,
        })
      )
    })

    it('should return 400 for missing required fields', async () => {
      const invalidRideData = {
        pickupLocation: {
          address: '123 Test St',
        },
        // Missing dropoffLocation and other required fields
      }

      const request = new NextRequest('http://localhost:3000/api/rides', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidRideData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Missing required fields')
    })

    it('should return 401 for unauthorized user', async () => {
      const request = new NextRequest('http://localhost:3000/api/rides', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validRideData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Authorization token required')
    })

    it('should handle invalid JSON body', async () => {
      const request = new NextRequest('http://localhost:3000/api/rides', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json',
        },
        body: 'invalid-json',
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid JSON body')
    })

    it('should validate coordinates format', async () => {
      const invalidCoordinatesData = {
        ...validRideData,
        pickupLocation: {
          address: '123 Test St',
          coordinates: { lat: 'invalid', lng: 'invalid' },
        },
      }

      const request = new NextRequest('http://localhost:3000/api/rides', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invalidCoordinatesData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid coordinates format')
    })

    it('should handle database creation errors', async () => {
      mockRide.create.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/rides', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer valid-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validRideData),
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.success).toBe(false)
      expect(data.error).toBe('Failed to create ride')
    })
  })
})
