'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight, 
  Play, 
  Star, 
  Users, 
  MapPin, 
  Zap,
  Shield,
  Smartphone,
  Globe,
  TrendingUp,
  CheckCircle,
  Car,
  Bike,
  Package,
  Building2,
  Leaf,
  Clock,
  DollarSign,
  Award
} from 'lucide-react';

export default function HomePage() {
  const [activeFeature, setActiveFeature] = useState(0);
  const [stats, setStats] = useState({
    totalRides: 0,
    citiesCovered: 0,
    happyCustomers: 0,
    carbonSaved: 0,
  });

  useEffect(() => {
    // Animate stats counter
    const targetStats = {
      totalRides: 2500000,
      citiesCovered: 50,
      happyCustomers: 150000,
      carbonSaved: 45000,
    };

    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      
      setStats({
        totalRides: Math.floor(targetStats.totalRides * progress),
        citiesCovered: Math.floor(targetStats.citiesCovered * progress),
        happyCustomers: Math.floor(targetStats.happyCustomers * progress),
        carbonSaved: Math.floor(targetStats.carbonSaved * progress),
      });

      if (currentStep >= steps) {
        clearInterval(interval);
        setStats(targetStats);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: Car,
      title: 'Smart Ride Sharing',
      description: 'AI-powered matching with real-time tracking and premium vehicles',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      icon: Bike,
      title: 'Micro-Mobility',
      description: 'Bikes and scooters with IoT tracking and dynamic pricing',
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      icon: Package,
      title: 'Delivery Ecosystem',
      description: 'Food, packages, and logistics with same-day delivery',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      icon: Building2,
      title: 'Corporate Solutions',
      description: 'Enterprise mobility with analytics and cost optimization',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
  ];

  const testimonials = [
    {
      name: 'Priya Sharma',
      role: 'Marketing Manager',
      company: 'TechCorp',
      content: 'This platform has revolutionized our corporate travel. The analytics and cost savings are incredible!',
      rating: 5,
      avatar: '/avatars/priya.jpg',
    },
    {
      name: 'Rajesh Kumar',
      role: 'Daily Commuter',
      company: 'Bangalore',
      content: 'Multi-modal journey planning saves me 30 minutes daily. The app is intuitive and reliable.',
      rating: 5,
      avatar: '/avatars/rajesh.jpg',
    },
    {
      name: 'Sarah Chen',
      role: 'Restaurant Owner',
      company: 'Spice Garden',
      content: 'The delivery integration boosted our orders by 40%. Excellent driver network and tracking.',
      rating: 5,
      avatar: '/avatars/sarah.jpg',
    },
  ];

  const pricingPlans = [
    {
      name: 'Personal',
      price: 'Free',
      description: 'Perfect for individual users',
      features: [
        'Ride booking & tracking',
        'Multi-modal journey planning',
        'Basic rewards program',
        'Standard support',
      ],
      buttonText: 'Get Started',
      popular: false,
    },
    {
      name: 'Premium',
      price: '₹299/month',
      description: 'Enhanced features for frequent travelers',
      features: [
        'Everything in Personal',
        'Priority booking',
        'Premium vehicles',
        'Advanced analytics',
        'Priority support',
      ],
      buttonText: 'Start Free Trial',
      popular: true,
    },
    {
      name: 'Corporate',
      price: 'Custom',
      description: 'Tailored solutions for businesses',
      features: [
        'Everything in Premium',
        'Corporate dashboard',
        'Bulk booking',
        'Custom integrations',
        'Dedicated account manager',
      ],
      buttonText: 'Contact Sales',
      popular: false,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4 bg-white/20 text-white border-white/30">
                🚀 India's #1 Multi-Modal Transportation Platform
              </Badge>
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                The Future of
                <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                  {' '}Urban Mobility
                </span>
              </h1>
              <p className="text-xl mb-8 text-blue-100 leading-relaxed">
                One platform for rides, deliveries, micro-mobility, and corporate travel. 
                Powered by AI, optimized for sustainability, designed for the future.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 font-semibold">
                  <Smartphone className="mr-2 h-5 w-5" />
                  Download App
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                  <Play className="mr-2 h-5 w-5" />
                  Watch Demo
                </Button>
              </div>
              
              {/* Stats */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
                <div className="text-center">
                  <div className="text-3xl font-bold">{stats.totalRides.toLocaleString()}+</div>
                  <div className="text-blue-200 text-sm">Total Rides</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{stats.citiesCovered}+</div>
                  <div className="text-blue-200 text-sm">Cities</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{stats.happyCustomers.toLocaleString()}+</div>
                  <div className="text-blue-200 text-sm">Happy Users</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">{stats.carbonSaved.toLocaleString()}kg</div>
                  <div className="text-blue-200 text-sm">CO₂ Saved</div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="relative z-10">
                <img 
                  src="/hero-app-mockup.png" 
                  alt="Multi-Modal Transportation App"
                  className="w-full max-w-md mx-auto drop-shadow-2xl"
                  onError={(e) => {
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjgwMCIgdmlld0JveD0iMCAwIDQwMCA4MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iODAwIiByeD0iNDAiIGZpbGw9InVybCgjZ3JhZGllbnQwX2xpbmVhcl8xXzEpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50MF9saW5lYXJfMV8xIiB4MT0iMjAwIiB5MT0iMCIgeDI9IjIwMCIgeTI9IjgwMCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjNjM2NkYxIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzM3MzBBMyIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=';
                  }}
                />
              </div>
              <div className="absolute -top-4 -right-4 w-72 h-72 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-20 blur-3xl"></div>
              <div className="absolute -bottom-4 -left-4 w-72 h-72 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 blur-3xl"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-blue-100 text-blue-800">
              🌟 Complete Mobility Ecosystem
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need in One Platform
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From daily commutes to corporate travel, from food delivery to micro-mobility - 
              we've got every aspect of urban transportation covered.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card 
                  key={index}
                  className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 ${
                    activeFeature === index ? 'ring-2 ring-blue-500 shadow-xl' : ''
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 ${feature.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>
                      <Icon className={`h-8 w-8 ${feature.color}`} />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Feature Benefits */}
          <div className="mt-16 grid grid-cols-1 lg:grid-cols-3 gap-8">
            <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
              <CardContent className="p-6">
                <Leaf className="h-12 w-12 text-green-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">Eco-Friendly</h3>
                <p className="text-gray-600">
                  Reduce your carbon footprint with smart route optimization and shared mobility options.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-200">
              <CardContent className="p-6">
                <Zap className="h-12 w-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">AI-Powered</h3>
                <p className="text-gray-600">
                  Advanced algorithms optimize routes, predict demand, and enhance your travel experience.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
              <CardContent className="p-6">
                <Shield className="h-12 w-12 text-purple-600 mb-4" />
                <h3 className="text-xl font-semibold mb-2">Safe & Secure</h3>
                <p className="text-gray-600">
                  End-to-end encryption, verified drivers, and 24/7 support ensure your safety.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-yellow-100 text-yellow-800">
              ⭐ Customer Love
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Trusted by Millions
            </h2>
            <p className="text-xl text-gray-600">
              See what our customers say about their experience
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white shadow-lg hover:shadow-xl transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-semibold">{testimonial.name}</div>
                      <div className="text-sm text-gray-500">{testimonial.role}, {testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-green-100 text-green-800">
              💰 Simple Pricing
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-600">
              Flexible pricing for individuals and businesses
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <Card 
                key={index} 
                className={`relative ${plan.popular ? 'ring-2 ring-blue-500 shadow-xl scale-105' : 'hover:shadow-lg'} transition-all`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white px-4 py-1">
                      Most Popular
                    </Badge>
                  </div>
                )}
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                    <div className="text-4xl font-bold text-blue-600 mb-2">{plan.price}</div>
                    <p className="text-gray-600">{plan.description}</p>
                  </div>
                  
                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <Button 
                    className={`w-full ${plan.popular ? 'bg-blue-600 hover:bg-blue-700' : ''}`}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    {plan.buttonText}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-bold mb-4">
            Ready to Transform Your Mobility?
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            Join millions of users who have already made the switch to smarter, 
            more sustainable transportation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 font-semibold">
              <Smartphone className="mr-2 h-5 w-5" />
              Get Started Today
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              <Users className="mr-2 h-5 w-5" />
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
