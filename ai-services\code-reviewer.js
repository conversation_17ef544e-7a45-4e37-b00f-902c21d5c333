/**
 * AI-Powered Code Review Engine
 * Provides intelligent code review capabilities with contextual feedback
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

class CodeReviewer {
  constructor(ollamaUrl = 'http://localhost:11434') {
    this.ollamaUrl = ollamaUrl;
    this.model = 'codellama:7b-instruct';
    this.reviewHistory = new Map();
    this.teamPatterns = new Map();
    this.qualityThresholds = {
      complexity: 10,
      maintainability: 7,
      security: 9,
      performance: 8
    };
  }

  /**
   * Review a pull request with comprehensive analysis
   */
  async reviewPullRequest(prData) {
    const { files, diff, metadata } = prData;
    
    console.log(`🔍 Reviewing PR: ${metadata.title}`);
    
    const reviewResults = {
      overall_score: 0,
      files: [],
      summary: '',
      recommendations: [],
      blocking_issues: [],
      suggestions: [],
      security_concerns: [],
      performance_issues: [],
      timestamp: new Date().toISOString()
    };

    // Analyze each changed file
    for (const file of files) {
      const fileReview = await this.reviewFile(file, diff);
      reviewResults.files.push(fileReview);
    }

    // Generate overall assessment
    reviewResults.overall_score = this.calculateOverallScore(reviewResults.files);
    reviewResults.summary = await this.generateReviewSummary(reviewResults);
    reviewResults.recommendations = this.generateRecommendations(reviewResults);

    // Learn from this review
    this.updateLearningPatterns(reviewResults, metadata);

    return reviewResults;
  }

  /**
   * Review individual file changes
   */
  async reviewFile(fileData, diff) {
    const { path: filePath, content, changes } = fileData;
    
    const prompt = `
Review this code change with expert-level analysis:

File: ${filePath}
Changes: ${changes.added} lines added, ${changes.removed} lines removed

Code:
\`\`\`${this.getFileExtension(filePath)}
${content}
\`\`\`

Diff:
\`\`\`diff
${diff}
\`\`\`

Provide detailed review focusing on:
1. Code quality and maintainability (1-10 scale)
2. Security vulnerabilities and concerns
3. Performance implications
4. Best practices adherence
5. Potential bugs or edge cases
6. Readability and documentation
7. Testing requirements
8. Architecture and design patterns

Format response as JSON:
{
  "quality_score": number,
  "security_score": number,
  "performance_score": number,
  "maintainability_score": number,
  "issues": [
    {
      "type": "bug|security|performance|style|architecture",
      "severity": "critical|high|medium|low",
      "line": number,
      "description": "string",
      "suggestion": "string",
      "code_snippet": "string"
    }
  ],
  "positive_aspects": ["string"],
  "suggestions": [
    {
      "type": "improvement|optimization|refactor",
      "description": "string",
      "impact": "high|medium|low",
      "effort": "high|medium|low"
    }
  ],
  "testing_recommendations": ["string"],
  "documentation_needs": ["string"]
}`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.1,
          top_p: 0.9,
          max_tokens: 4096
        }
      });

      const review = this.parseReviewResponse(response.data.response);
      
      return {
        file: filePath,
        review,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Failed to review ${filePath}:`, error.message);
      return this.createFallbackReview(filePath, content);
    }
  }

  /**
   * Generate contextual code review comments
   */
  async generateReviewComments(fileReview) {
    const comments = [];

    for (const issue of fileReview.review.issues) {
      if (issue.severity === 'critical' || issue.severity === 'high') {
        const comment = await this.generateDetailedComment(issue, fileReview.file);
        comments.push({
          path: fileReview.file,
          line: issue.line,
          body: comment,
          severity: issue.severity,
          type: issue.type
        });
      }
    }

    // Add positive feedback comments
    if (fileReview.review.positive_aspects.length > 0) {
      comments.push({
        path: fileReview.file,
        line: 1,
        body: `✅ **Great work!** ${fileReview.review.positive_aspects.join(', ')}`,
        severity: 'info',
        type: 'positive'
      });
    }

    return comments;
  }

  /**
   * Generate detailed comment for specific issue
   */
  async generateDetailedComment(issue, filePath) {
    const prompt = `
Generate a helpful, constructive code review comment for this issue:

File: ${filePath}
Issue Type: ${issue.type}
Severity: ${issue.severity}
Description: ${issue.description}
Suggestion: ${issue.suggestion}
Code: ${issue.code_snippet}

Create a professional, helpful comment that:
1. Explains the issue clearly
2. Provides specific improvement suggestions
3. Includes code examples if helpful
4. Maintains a positive, collaborative tone
5. References best practices or documentation when relevant

Keep it concise but informative (2-4 sentences).`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.2,
          top_p: 0.9,
          max_tokens: 512
        }
      });

      return this.formatComment(response.data.response, issue);

    } catch (error) {
      return this.generateFallbackComment(issue);
    }
  }

  /**
   * Analyze code quality trends over time
   */
  async analyzeQualityTrends(timeframe = '30d') {
    const trends = {
      quality_trend: 'improving', // improving|stable|declining
      metrics: {
        average_quality: 0,
        security_score: 0,
        performance_score: 0,
        maintainability: 0
      },
      insights: [],
      recommendations: [],
      team_patterns: []
    };

    // Analyze historical review data
    const historicalData = this.getHistoricalReviews(timeframe);
    
    if (historicalData.length > 0) {
      trends.metrics = this.calculateTrendMetrics(historicalData);
      trends.insights = this.generateTrendInsights(historicalData);
      trends.recommendations = this.generateTrendRecommendations(trends.metrics);
    }

    return trends;
  }

  /**
   * Learn from team feedback and adapt review criteria
   */
  updateLearningPatterns(reviewResults, metadata) {
    const patterns = {
      author: metadata.author,
      files_changed: reviewResults.files.length,
      quality_scores: reviewResults.files.map(f => f.review.quality_score),
      common_issues: this.extractCommonIssues(reviewResults),
      timestamp: new Date().toISOString()
    };

    // Store patterns for learning
    const key = `${metadata.author}_${new Date().toISOString().split('T')[0]}`;
    this.teamPatterns.set(key, patterns);

    // Adapt thresholds based on team performance
    this.adaptQualityThresholds(patterns);
  }

  /**
   * Generate automated review summary
   */
  async generateReviewSummary(reviewResults) {
    const prompt = `
Generate a concise, professional PR review summary:

Overall Score: ${reviewResults.overall_score}/10
Files Reviewed: ${reviewResults.files.length}
Critical Issues: ${reviewResults.blocking_issues.length}
Suggestions: ${reviewResults.suggestions.length}

Key findings:
${reviewResults.files.map(f => `- ${f.file}: ${f.review.quality_score}/10`).join('\n')}

Create a 2-3 sentence summary that:
1. Highlights the overall assessment
2. Mentions key strengths or concerns
3. Provides clear next steps
4. Maintains a constructive tone`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.3,
          top_p: 0.9,
          max_tokens: 256
        }
      });

      return response.data.response.trim();

    } catch (error) {
      return this.generateFallbackSummary(reviewResults);
    }
  }

  /**
   * Check if PR meets quality gates
   */
  checkQualityGates(reviewResults) {
    const gates = {
      overall_quality: reviewResults.overall_score >= this.qualityThresholds.maintainability,
      no_critical_issues: reviewResults.blocking_issues.length === 0,
      security_compliant: !reviewResults.security_concerns.some(c => c.severity === 'critical'),
      performance_acceptable: !reviewResults.performance_issues.some(p => p.severity === 'critical'),
      test_coverage: this.checkTestCoverage(reviewResults)
    };

    gates.passed = Object.values(gates).every(gate => gate === true);
    
    return gates;
  }

  /**
   * Generate recommendations based on review
   */
  generateRecommendations(reviewResults) {
    const recommendations = [];

    // Quality-based recommendations
    if (reviewResults.overall_score < 7) {
      recommendations.push({
        type: 'quality',
        priority: 'high',
        description: 'Consider refactoring to improve code quality before merging'
      });
    }

    // Security recommendations
    if (reviewResults.security_concerns.length > 0) {
      recommendations.push({
        type: 'security',
        priority: 'critical',
        description: 'Address security concerns before deployment'
      });
    }

    // Performance recommendations
    if (reviewResults.performance_issues.length > 2) {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        description: 'Consider performance optimizations for better user experience'
      });
    }

    return recommendations;
  }

  // Helper methods
  calculateOverallScore(fileReviews) {
    if (fileReviews.length === 0) return 0;
    const scores = fileReviews.map(f => f.review.quality_score);
    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
  }

  parseReviewResponse(response) {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return this.createFallbackReviewData();
    } catch (error) {
      return this.createFallbackReviewData();
    }
  }

  createFallbackReview(filePath, content) {
    return {
      file: filePath,
      review: this.createFallbackReviewData(),
      timestamp: new Date().toISOString()
    };
  }

  createFallbackReviewData() {
    return {
      quality_score: 7,
      security_score: 8,
      performance_score: 7,
      maintainability_score: 7,
      issues: [],
      positive_aspects: ['Code structure looks reasonable'],
      suggestions: [],
      testing_recommendations: ['Add unit tests for new functionality'],
      documentation_needs: []
    };
  }

  formatComment(response, issue) {
    const emoji = this.getIssueEmoji(issue.type, issue.severity);
    return `${emoji} **${issue.type.toUpperCase()}** (${issue.severity})\n\n${response.trim()}`;
  }

  generateFallbackComment(issue) {
    const emoji = this.getIssueEmoji(issue.type, issue.severity);
    return `${emoji} **${issue.type.toUpperCase()}** (${issue.severity})\n\n${issue.description}\n\n💡 **Suggestion:** ${issue.suggestion}`;
  }

  generateFallbackSummary(reviewResults) {
    return `Code review completed. Overall quality score: ${reviewResults.overall_score}/10. ${reviewResults.blocking_issues.length > 0 ? 'Please address critical issues before merging.' : 'Looks good to merge!'}`;
  }

  getIssueEmoji(type, severity) {
    const emojiMap = {
      'bug': '🐛',
      'security': '🔒',
      'performance': '⚡',
      'style': '🎨',
      'architecture': '🏗️'
    };
    return emojiMap[type] || '💡';
  }

  getFileExtension(filePath) {
    return path.extname(filePath).slice(1) || 'text';
  }

  // Placeholder methods for advanced features
  getHistoricalReviews(timeframe) { return []; }
  calculateTrendMetrics(data) { return {}; }
  generateTrendInsights(data) { return []; }
  generateTrendRecommendations(metrics) { return []; }
  extractCommonIssues(results) { return []; }
  adaptQualityThresholds(patterns) { }
  checkTestCoverage(results) { return true; }
}

module.exports = CodeReviewer;
