'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Car, 
  DollarSign, 
  Activity,
  BarChart3,
  PieChart,
  LineChart,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

interface AnalyticsDashboardProps {
  userRole: 'admin' | 'manager' | 'analyst';
  timeRange?: {
    start: Date;
    end: Date;
    granularity: 'hour' | 'day' | 'week' | 'month';
  };
}

interface MetricCard {
  title: string;
  value: string | number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<any>;
  color: string;
}

export default function AnalyticsDashboard({ userRole, timeRange }: AnalyticsDashboardProps) {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange || {
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    end: new Date(),
    granularity: 'day' as const,
  });
  const [businessMetrics, setBusinessMetrics] = useState<any>(null);
  const [predictiveAnalytics, setPredictiveAnalytics] = useState<any>(null);
  const [customerBehavior, setCustomerBehavior] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch analytics data
  const fetchAnalyticsData = async (refresh = false) => {
    if (refresh) setRefreshing(true);
    else setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Fetch business metrics
      const metricsResponse = await fetch('/api/analytics/business-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ timeRange: selectedTimeRange }),
      });

      if (!metricsResponse.ok) {
        throw new Error('Failed to fetch business metrics');
      }

      const metricsData = await metricsResponse.json();
      setBusinessMetrics(metricsData.data);

      // Fetch predictive analytics (if user has access)
      if (userRole === 'admin' || userRole === 'analyst') {
        const predictiveResponse = await fetch('/api/analytics/predictive', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({ timeRange: selectedTimeRange }),
        });

        if (predictiveResponse.ok) {
          const predictiveData = await predictiveResponse.json();
          setPredictiveAnalytics(predictiveData.data);
        }
      }

      // Fetch customer behavior analysis
      const behaviorResponse = await fetch('/api/analytics/customer-behavior', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ timeRange: selectedTimeRange }),
      });

      if (behaviorResponse.ok) {
        const behaviorData = await behaviorResponse.json();
        setCustomerBehavior(behaviorData.data);
      }

    } catch (error) {
      console.error('Analytics data fetch failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to load analytics data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedTimeRange, userRole]);

  // Generate metric cards from business metrics
  const generateMetricCards = (): MetricCard[] => {
    if (!businessMetrics) return [];

    return [
      {
        title: 'Total Revenue',
        value: `₹${businessMetrics.revenue.total.toLocaleString()}`,
        change: businessMetrics.revenue.growth,
        trend: businessMetrics.revenue.growth > 0 ? 'up' : 'down',
        icon: DollarSign,
        color: 'text-green-600',
      },
      {
        title: 'Total Rides',
        value: businessMetrics.rides.total.toLocaleString(),
        change: 12.5, // Would calculate from trends
        trend: 'up',
        icon: Car,
        color: 'text-blue-600',
      },
      {
        title: 'Active Users',
        value: businessMetrics.users.active.toLocaleString(),
        change: 8.3,
        trend: 'up',
        icon: Users,
        color: 'text-purple-600',
      },
      {
        title: 'Completion Rate',
        value: `${businessMetrics.rides.completionRate.toFixed(1)}%`,
        change: 2.1,
        trend: 'up',
        icon: Activity,
        color: 'text-orange-600',
      },
      {
        title: 'Online Drivers',
        value: businessMetrics.drivers.online.toLocaleString(),
        change: -3.2,
        trend: 'down',
        icon: Car,
        color: 'text-indigo-600',
      },
      {
        title: 'Profit Margin',
        value: `${businessMetrics.financial.profitMargin.toFixed(1)}%`,
        change: 1.8,
        trend: 'up',
        icon: TrendingUp,
        color: 'text-emerald-600',
      },
    ];
  };

  const metricCards = generateMetricCards();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <LoadingSpinner className="mr-2" />
        <span>Loading analytics dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <Button onClick={() => fetchAnalyticsData()} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Real-time business intelligence and insights</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={() => fetchAnalyticsData(true)}
            disabled={refreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
      </div>

      {/* Time Range Selector */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <Calendar className="h-5 w-5 text-gray-500" />
            <span className="text-sm font-medium">Time Range:</span>
            <div className="flex space-x-2">
              {['day', 'week', 'month'].map((period) => (
                <Button
                  key={period}
                  variant={selectedTimeRange.granularity === period ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => {
                    const now = new Date();
                    const start = new Date();
                    
                    if (period === 'day') {
                      start.setDate(now.getDate() - 1);
                    } else if (period === 'week') {
                      start.setDate(now.getDate() - 7);
                    } else {
                      start.setMonth(now.getMonth() - 1);
                    }

                    setSelectedTimeRange({
                      start,
                      end: now,
                      granularity: period as 'day' | 'week' | 'month',
                    });
                  }}
                >
                  Last {period}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {metricCards.map((metric, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                  <div className="flex items-center mt-2">
                    {metric.trend === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-green-600 mr-1" />
                    ) : metric.trend === 'down' ? (
                      <TrendingDown className="h-4 w-4 text-red-600 mr-1" />
                    ) : (
                      <Activity className="h-4 w-4 text-gray-600 mr-1" />
                    )}
                    <span className={`text-sm font-medium ${
                      metric.trend === 'up' ? 'text-green-600' : 
                      metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {metric.change > 0 ? '+' : ''}{metric.change}%
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last period</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full bg-gray-100 ${metric.color}`}>
                  <metric.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <LineChart className="h-5 w-5 mr-2" />
              Revenue Trends
            </CardTitle>
            <CardDescription>Revenue performance over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
              <div className="text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Revenue chart would be rendered here</p>
                <p className="text-sm text-gray-400">Integration with charting library needed</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ride Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Ride Distribution
            </CardTitle>
            <CardDescription>Breakdown by ride status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm">Completed</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">
                    {businessMetrics?.rides.completed || 0}
                  </span>
                  <Badge variant="secondary">
                    {businessMetrics?.rides.completionRate.toFixed(1)}%
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                  <span className="text-sm">Cancelled</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">
                    {businessMetrics?.rides.cancelled || 0}
                  </span>
                  <Badge variant="destructive">
                    {((businessMetrics?.rides.cancelled || 0) / (businessMetrics?.rides.total || 1) * 100).toFixed(1)}%
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                  <span className="text-sm">In Progress</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">
                    {(businessMetrics?.rides.total || 0) - (businessMetrics?.rides.completed || 0) - (businessMetrics?.rides.cancelled || 0)}
                  </span>
                  <Badge variant="outline">Active</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Predictive Analytics (Admin/Analyst only) */}
      {(userRole === 'admin' || userRole === 'analyst') && predictiveAnalytics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Predictive Analytics
            </CardTitle>
            <CardDescription>AI-powered forecasts and predictions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">Demand Forecast</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {predictiveAnalytics.demandForecast.nextDay}
                </p>
                <p className="text-sm text-gray-500">rides tomorrow</p>
                <Badge variant="outline" className="mt-2">
                  {(predictiveAnalytics.demandForecast.confidence * 100).toFixed(0)}% confidence
                </Badge>
              </div>
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">Revenue Forecast</h4>
                <p className="text-2xl font-bold text-green-600">
                  ₹{predictiveAnalytics.revenueForecast.nextDay.toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">expected tomorrow</p>
                <Badge variant="outline" className="mt-2">
                  {(predictiveAnalytics.revenueForecast.confidence * 100).toFixed(0)}% confidence
                </Badge>
              </div>
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">Growth Rate</h4>
                <p className="text-2xl font-bold text-purple-600">
                  {predictiveAnalytics.marketTrends.growthRate}%
                </p>
                <p className="text-sm text-gray-500">monthly growth</p>
                <Badge variant="outline" className="mt-2">Trending up</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Customer Behavior Insights */}
      {customerBehavior && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Customer Behavior Insights
            </CardTitle>
            <CardDescription>User engagement and satisfaction metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">Avg. Rides per User</h4>
                <p className="text-xl font-bold text-blue-600">
                  {customerBehavior.userJourney.engagement.ridesPerUser}
                </p>
              </div>
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">Session Duration</h4>
                <p className="text-xl font-bold text-green-600">
                  {customerBehavior.userJourney.engagement.averageSessionDuration}m
                </p>
              </div>
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">Overall Rating</h4>
                <p className="text-xl font-bold text-yellow-600">
                  {customerBehavior.satisfaction.overallRating}/5
                </p>
              </div>
              <div className="text-center">
                <h4 className="font-medium text-gray-900 mb-2">NPS Score</h4>
                <p className="text-xl font-bold text-purple-600">
                  {customerBehavior.satisfaction.nps}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
