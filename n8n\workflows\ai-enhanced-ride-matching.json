{"name": "AI-Enhanced Ride Matching", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ai-ride-request", "responseMode": "responseNode"}, "id": "webhook-ai-ride-request", "name": "AI Ride Request Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://ml-service:8081/predict/demand", "sendBody": true, "bodyParameters": {"parameters": [{"name": "location", "value": "={{ $json.rideRequest.pickup }}"}, {"name": "latitude", "value": "={{ $json.rideRequest.pickupLat }}"}, {"name": "longitude", "value": "={{ $json.rideRequest.pickupLon }}"}, {"name": "hours_ahead", "value": 1}, {"name": "include_weather", "value": true}]}}, "id": "predict-demand", "name": "Predict Demand", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 200]}, {"parameters": {"url": "http://ml-service:8081/optimize/fleet-positioning", "sendBody": true, "bodyParameters": {"parameters": [{"name": "current_drivers", "value": "={{ $json.availableDrivers }}"}, {"name": "time_horizon", "value": 30}]}}, "id": "optimize-fleet", "name": "Optimize Fleet Positioning", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 400]}, {"parameters": {"url": "http://mcp-server:8080/ai/assign-driver", "sendBody": true, "bodyParameters": {"parameters": [{"name": "rideRequest", "value": "={{ $json.rideRequest }}"}, {"name": "availableDrivers", "value": "={{ $json.availableDrivers }}"}, {"name": "demandPrediction", "value": "={{ $('Predict Demand').item.json }}"}, {"name": "fleetOptimization", "value": "={{ $('Optimize Fleet Positioning').item.json }}"}]}}, "id": "ai-enhanced-assignment", "name": "AI Enhanced Driver Assignment", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"url": "http://ml-service:8081/optimize/pricing", "sendBody": true, "bodyParameters": {"parameters": [{"name": "base_distance", "value": "={{ $json.rideRequest.distance }}"}, {"name": "current_demand", "value": "={{ $('Predict Demand').item.json.predicted_demand > 10 ? 'high' : ($('Predict Demand').item.json.predicted_demand > 5 ? 'medium' : 'low') }}"}, {"name": "weather_condition", "value": "={{ $json.rideRequest.weather || 'clear' }}"}, {"name": "time_of_day", "value": "={{ $json.rideRequest.timeOfDay || 'normal' }}"}, {"name": "driver_availability", "value": "={{ $json.availableDrivers.length }}"}]}}, "id": "optimize-pricing", "name": "AI Pricing Optimization", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 500]}, {"parameters": {"url": "http://mcp-server:8080/tools/execute", "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "assign_task"}, {"name": "parameters", "value": "={{ {\n  \"taskId\": $json.rideRequest.requestId || $now,\n  \"driverId\": $('AI Enhanced Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"userId\": $('AI Ride Request Webhook').item.json.rideRequest.userId,\n  \"route\": $('AI Ride Request Webhook').item.json.rideRequest.route,\n  \"estimatedEarnings\": $('AI Pricing Optimization').item.json.driver_earnings,\n  \"aiInsights\": {\n    \"demandLevel\": $('Predict Demand').item.json.predicted_demand,\n    \"confidence\": $('AI Enhanced Driver Assignment').item.json.assignment.confidence,\n    \"pricingVariant\": $('AI Pricing Optimization').item.json.ab_test_variant\n  }\n} }}"}]}}, "id": "assign-task-ai", "name": "Assign Task with AI Insights", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300]}, {"parameters": {"url": "http://mcp-server:8080/tools/execute", "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "notify_driver"}, {"name": "parameters", "value": "={{ {\n  \"driverId\": $('AI Enhanced Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"message\": \"AI-optimized ride request! Pickup: \" + $('AI Ride Request Webhook').item.json.rideRequest.pickup + \" → \" + $('AI Ride Request Webhook').item.json.rideRequest.destination + \". Predicted demand: \" + $('Predict Demand').item.json.predicted_demand.toFixed(1) + \". Earnings: ₹\" + $('AI Pricing Optimization').item.json.driver_earnings,\n  \"taskId\": $('Assign Task with AI Insights').item.json.taskId,\n  \"priority\": \"high\",\n  \"aiData\": {\n    \"confidence\": $('AI Enhanced Driver Assignment').item.json.assignment.confidence,\n    \"demandPrediction\": $('Predict Demand').item.json.predicted_demand,\n    \"pricingOptimized\": true\n  }\n} }}"}]}}, "id": "notify-driver-ai", "name": "Notify Driver with AI Insights", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"url": "http://mcp-server:8080/tools/execute", "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "recommend_driver_positioning"}, {"name": "parameters", "value": "={{ {\n  \"current_drivers\": $json.availableDrivers,\n  \"time_horizon\": 60,\n  \"max_recommendations\": 5\n} }}"}]}}, "id": "fleet-recommendations", "name": "Generate Fleet Recommendations", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 500]}, {"parameters": {"url": "http://app:3000/api/rides", "sendBody": true, "bodyParameters": {"parameters": [{"name": "rideData", "value": "={{ {\n  \"userId\": $('AI Ride Request Webhook').item.json.rideRequest.userId,\n  \"driverId\": $('AI Enhanced Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"pickup\": $('AI Ride Request Webhook').item.json.rideRequest.pickup,\n  \"destination\": $('AI Ride Request Webhook').item.json.rideRequest.destination,\n  \"distance\": $('AI Ride Request Webhook').item.json.rideRequest.distance,\n  \"pricing\": $('AI Pricing Optimization').item.json,\n  \"estimatedPickupTime\": $('AI Enhanced Driver Assignment').item.json.assignment.estimatedPickupTime,\n  \"status\": \"assigned\",\n  \"assignedAt\": $now,\n  \"aiMetadata\": {\n    \"demandPrediction\": $('Predict Demand').item.json,\n    \"assignmentConfidence\": $('AI Enhanced Driver Assignment').item.json.assignment.confidence,\n    \"pricingVariant\": $('AI Pricing Optimization').item.json.ab_test_variant,\n    \"fleetOptimization\": $('Optimize Fleet Positioning').item.json.efficiency_score\n  }\n} }}"}]}}, "id": "create-ai-ride-record", "name": "Create AI-Enhanced Ride Record", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"rideId\": $('Create AI-Enhanced Ride Record').item.json.rideId,\n  \"driverId\": $('AI Enhanced Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"estimatedPickupTime\": $('AI Enhanced Driver Assignment').item.json.assignment.estimatedPickupTime,\n  \"pricing\": $('AI Pricing Optimization').item.json,\n  \"aiInsights\": {\n    \"demandPrediction\": $('Predict Demand').item.json.predicted_demand,\n    \"assignmentConfidence\": $('AI Enhanced Driver Assignment').item.json.assignment.confidence,\n    \"pricingOptimization\": $('AI Pricing Optimization').item.json.ab_test_variant,\n    \"fleetEfficiency\": $('Optimize Fleet Positioning').item.json.efficiency_score\n  },\n  \"fleetRecommendations\": $('Generate Fleet Recommendations').item.json.recommendations,\n  \"message\": \"AI-optimized ride successfully assigned with \" + $('AI Enhanced Driver Assignment').item.json.assignment.confidence + \" confidence\"\n} }}"}, "id": "ai-webhook-response", "name": "AI-Enhanced Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-ai-success", "leftValue": "={{ $('AI Enhanced Driver Assignment').item.json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}, {"id": "condition-confidence", "leftValue": "={{ $('AI Enhanced Driver Assignment').item.json.assignment.confidence }}", "rightValue": 0.7, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}}, "id": "check-ai-assignment-quality", "name": "Check AI Assignment Quality", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 100]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": \"AI assignment confidence too low\",\n  \"confidence\": $('AI Enhanced Driver Assignment').item.json.assignment.confidence,\n  \"fallbackMessage\": \"Falling back to manual assignment\",\n  \"demandPrediction\": $('Predict Demand').item.json.predicted_demand\n} }}"}, "id": "low-confidence-response", "name": "Low Confidence Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 100]}], "connections": {"AI Ride Request Webhook": {"main": [[{"node": "Predict Demand", "type": "main", "index": 0}, {"node": "Optimize Fleet Positioning", "type": "main", "index": 0}]]}, "Predict Demand": {"main": [[{"node": "AI Enhanced Driver Assignment", "type": "main", "index": 0}, {"node": "AI Pricing Optimization", "type": "main", "index": 0}]]}, "Optimize Fleet Positioning": {"main": [[{"node": "AI Enhanced Driver Assignment", "type": "main", "index": 0}]]}, "AI Enhanced Driver Assignment": {"main": [[{"node": "Check AI Assignment Quality", "type": "main", "index": 0}]]}, "AI Pricing Optimization": {"main": [[{"node": "Assign Task with AI Insights", "type": "main", "index": 0}, {"node": "Generate Fleet Recommendations", "type": "main", "index": 0}]]}, "Check AI Assignment Quality": {"main": [[{"node": "Assign Task with AI Insights", "type": "main", "index": 0}], [{"node": "Low Confidence Response", "type": "main", "index": 0}]]}, "Assign Task with AI Insights": {"main": [[{"node": "Notify Driver with AI Insights", "type": "main", "index": 0}]]}, "Notify Driver with AI Insights": {"main": [[{"node": "Create AI-Enhanced Ride Record", "type": "main", "index": 0}]]}, "Generate Fleet Recommendations": {"main": [[{"node": "Create AI-Enhanced Ride Record", "type": "main", "index": 0}]]}, "Create AI-Enhanced Ride Record": {"main": [[{"node": "AI-Enhanced Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "2", "meta": {"templateCredsSetupCompleted": true}, "id": "ai-enhanced-ride-matching", "tags": ["ai", "ml", "predictive", "optimization"]}