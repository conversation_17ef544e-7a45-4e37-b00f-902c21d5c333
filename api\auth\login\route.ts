import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { email, password } = body;

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Check if account is active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated. Please contact support.' },
        { status: 403 }
      );
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id, 
        email: user.email, 
        role: user.role 
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Prepare user response based on role
    const userResponse: any = {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: user.fullName,
      phone: user.phone,
      role: user.role,
      isVerified: user.isVerified,
      profileImage: user.profileImage,
      rewardPoints: user.rewardPoints,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
    };

    // Add role-specific data
    if (user.role === 'driver' && user.driverProfile) {
      userResponse.driverProfile = {
        isApproved: user.driverProfile.isApproved,
        rating: user.driverProfile.rating,
        totalRides: user.driverProfile.totalRides,
        vehicleModel: user.driverProfile.vehicleModel,
        vehicleColor: user.driverProfile.vehicleColor,
        documentsUploaded: user.driverProfile.documentsUploaded,
      };
      userResponse.totalEarnings = user.totalEarnings;
    } else if (user.role === 'rider' && user.riderProfile) {
      userResponse.riderProfile = {
        rating: user.riderProfile.rating,
        totalRides: user.riderProfile.totalRides,
        preferredPaymentMethod: user.riderProfile.preferredPaymentMethod,
      };
      userResponse.totalSpent = user.totalSpent;
    }

    return NextResponse.json({
      message: 'Login successful',
      user: userResponse,
      token,
    }, { status: 200 });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
