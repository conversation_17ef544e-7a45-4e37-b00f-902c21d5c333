'use client';

import { ReactNode, useEffect, useState, useRef } from 'react';
import { cn } from '@/lib/utils';

interface FadeInProps {
  children: ReactNode;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  className?: string;
  once?: boolean;
}

interface SlideInProps {
  children: ReactNode;
  direction: 'left' | 'right' | 'up' | 'down';
  delay?: number;
  duration?: number;
  distance?: number;
  className?: string;
  once?: boolean;
}

interface CountUpProps {
  end: number;
  start?: number;
  duration?: number;
  delay?: number;
  suffix?: string;
  prefix?: string;
  separator?: string;
  decimals?: number;
  className?: string;
}

interface TypewriterProps {
  text: string | string[];
  speed?: number;
  delay?: number;
  loop?: boolean;
  cursor?: boolean;
  className?: string;
}

interface PulseProps {
  children: ReactNode;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface FloatingProps {
  children: ReactNode;
  duration?: number;
  distance?: number;
  className?: string;
}

// Hook for intersection observer
function useIntersectionObserver(options = {}) {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      { threshold: 0.1, ...options }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [hasIntersected]);

  return { ref, isIntersecting, hasIntersected };
}

// Fade In Animation
export function FadeIn({ 
  children, 
  delay = 0, 
  duration = 600, 
  direction = 'up', 
  className,
  once = true 
}: FadeInProps) {
  const { ref, isIntersecting, hasIntersected } = useIntersectionObserver();
  const shouldAnimate = once ? hasIntersected : isIntersecting;

  const directions = {
    up: 'translate-y-8',
    down: '-translate-y-8',
    left: 'translate-x-8',
    right: '-translate-x-8',
    none: '',
  };

  return (
    <div
      ref={ref}
      className={cn(
        'transition-all ease-out',
        shouldAnimate ? 'opacity-100 translate-x-0 translate-y-0' : `opacity-0 ${directions[direction]}`,
        className
      )}
      style={{
        transitionDuration: `${duration}ms`,
        transitionDelay: `${delay}ms`,
      }}
    >
      {children}
    </div>
  );
}

// Slide In Animation
export function SlideIn({ 
  children, 
  direction, 
  delay = 0, 
  duration = 600, 
  distance = 100, 
  className,
  once = true 
}: SlideInProps) {
  const { ref, isIntersecting, hasIntersected } = useIntersectionObserver();
  const shouldAnimate = once ? hasIntersected : isIntersecting;

  const getTransform = () => {
    if (shouldAnimate) return 'translate-x-0 translate-y-0';
    
    switch (direction) {
      case 'left': return `translate-x-${distance}px`;
      case 'right': return `-translate-x-${distance}px`;
      case 'up': return `translate-y-${distance}px`;
      case 'down': return `-translate-y-${distance}px`;
      default: return '';
    }
  };

  return (
    <div
      ref={ref}
      className={cn(
        'transition-all ease-out',
        shouldAnimate ? 'opacity-100' : 'opacity-0',
        className
      )}
      style={{
        transform: getTransform(),
        transitionDuration: `${duration}ms`,
        transitionDelay: `${delay}ms`,
      }}
    >
      {children}
    </div>
  );
}

// Count Up Animation
export function CountUp({ 
  end, 
  start = 0, 
  duration = 2000, 
  delay = 0, 
  suffix = '', 
  prefix = '', 
  separator = ',',
  decimals = 0,
  className 
}: CountUpProps) {
  const [count, setCount] = useState(start);
  const { ref, hasIntersected } = useIntersectionObserver();

  useEffect(() => {
    if (!hasIntersected) return;

    const startTime = Date.now() + delay;
    const endTime = startTime + duration;

    const timer = setInterval(() => {
      const now = Date.now();
      
      if (now < startTime) return;
      
      if (now >= endTime) {
        setCount(end);
        clearInterval(timer);
        return;
      }

      const progress = (now - startTime) / duration;
      const easeOutProgress = 1 - Math.pow(1 - progress, 3);
      const currentCount = start + (end - start) * easeOutProgress;
      
      setCount(currentCount);
    }, 16);

    return () => clearInterval(timer);
  }, [hasIntersected, start, end, duration, delay]);

  const formatNumber = (num: number) => {
    const rounded = Number(num.toFixed(decimals));
    const parts = rounded.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    return parts.join('.');
  };

  return (
    <span ref={ref} className={className}>
      {prefix}{formatNumber(count)}{suffix}
    </span>
  );
}

// Typewriter Effect
export function Typewriter({ 
  text, 
  speed = 100, 
  delay = 0, 
  loop = false, 
  cursor = true,
  className 
}: TypewriterProps) {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showCursor, setShowCursor] = useState(true);

  const textArray = Array.isArray(text) ? text : [text];

  useEffect(() => {
    const timeout = setTimeout(() => {
      const currentText = textArray[currentTextIndex];
      
      if (!isDeleting) {
        if (currentIndex < currentText.length) {
          setDisplayText(currentText.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        } else if (loop && textArray.length > 1) {
          setTimeout(() => setIsDeleting(true), 1000);
        }
      } else {
        if (currentIndex > 0) {
          setDisplayText(currentText.slice(0, currentIndex - 1));
          setCurrentIndex(currentIndex - 1);
        } else {
          setIsDeleting(false);
          setCurrentTextIndex((currentTextIndex + 1) % textArray.length);
        }
      }
    }, delay + (isDeleting ? speed / 2 : speed));

    return () => clearTimeout(timeout);
  }, [currentIndex, currentTextIndex, isDeleting, textArray, speed, delay, loop]);

  useEffect(() => {
    if (cursor) {
      const cursorInterval = setInterval(() => {
        setShowCursor(prev => !prev);
      }, 500);
      return () => clearInterval(cursorInterval);
    }
  }, [cursor]);

  return (
    <span className={className}>
      {displayText}
      {cursor && <span className={showCursor ? 'opacity-100' : 'opacity-0'}>|</span>}
    </span>
  );
}

// Pulse Animation
export function Pulse({ children, color = 'blue', size = 'md', className }: PulseProps) {
  const colors = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500',
  };

  const sizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  return (
    <div className={cn('relative inline-flex items-center', className)}>
      {children}
      <div className={cn('absolute -top-1 -right-1 rounded-full', colors[color], sizes[size])}>
        <div className={cn('absolute inset-0 rounded-full animate-ping', colors[color])}></div>
        <div className={cn('relative rounded-full', colors[color], sizes[size])}></div>
      </div>
    </div>
  );
}

// Floating Animation
export function Floating({ children, duration = 3000, distance = 10, className }: FloatingProps) {
  return (
    <div
      className={cn('animate-float', className)}
      style={{
        animationDuration: `${duration}ms`,
        '--float-distance': `${distance}px`,
      } as any}
    >
      {children}
    </div>
  );
}

// Stagger Children Animation
export function StaggerChildren({ 
  children, 
  staggerDelay = 100, 
  className 
}: { 
  children: ReactNode[]; 
  staggerDelay?: number; 
  className?: string; 
}) {
  return (
    <div className={className}>
      {Array.isArray(children) && children.map((child, index) => (
        <FadeIn key={index} delay={index * staggerDelay}>
          {child}
        </FadeIn>
      ))}
    </div>
  );
}

// Parallax Scroll Effect
export function ParallaxScroll({ 
  children, 
  speed = 0.5, 
  className 
}: { 
  children: ReactNode; 
  speed?: number; 
  className?: string; 
}) {
  const [offset, setOffset] = useState(0);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (ref.current) {
        const rect = ref.current.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * speed;
        setOffset(rate);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return (
    <div
      ref={ref}
      className={className}
      style={{
        transform: `translateY(${offset}px)`,
      }}
    >
      {children}
    </div>
  );
}

// Reveal on Scroll
export function RevealOnScroll({ 
  children, 
  threshold = 0.1, 
  className 
}: { 
  children: ReactNode; 
  threshold?: number; 
  className?: string; 
}) {
  const { ref, hasIntersected } = useIntersectionObserver({ threshold });

  return (
    <div
      ref={ref}
      className={cn(
        'transition-all duration-1000 ease-out',
        hasIntersected 
          ? 'opacity-100 translate-y-0 scale-100' 
          : 'opacity-0 translate-y-8 scale-95',
        className
      )}
    >
      {children}
    </div>
  );
}

// Loading Skeleton
export function LoadingSkeleton({ 
  lines = 3, 
  className 
}: { 
  lines?: number; 
  className?: string; 
}) {
  return (
    <div className={cn('animate-pulse space-y-3', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'h-4 bg-gray-200 rounded',
            index === lines - 1 && 'w-3/4',
            index === 0 && 'w-full',
            index > 0 && index < lines - 1 && 'w-5/6'
          )}
        />
      ))}
    </div>
  );
}
