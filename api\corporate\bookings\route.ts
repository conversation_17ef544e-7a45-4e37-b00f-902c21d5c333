import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { corporateMobilityService } from '@/lib/corporate/corporateMobilityService';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions (corporate users only)
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const allowedRoles = ['corporate_admin', 'corporate_user', 'admin'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Corporate access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      clientId,
      type,
      priority,
      pickupLocation,
      dropoffLocation,
      scheduledTime,
      isRecurring,
      recurringPattern,
      passengers,
      vehiclePreferences,
      purpose,
      costCenter,
      projectCode,
      clientMeeting
    } = body;

    // Validate required fields
    if (!clientId || !pickupLocation || !dropoffLocation || !scheduledTime) {
      return NextResponse.json(
        { error: 'Missing required fields: clientId, pickupLocation, dropoffLocation, scheduledTime' },
        { status: 400 }
      );
    }

    // Validate locations
    if (!pickupLocation.latitude || !pickupLocation.longitude || !pickupLocation.address) {
      return NextResponse.json(
        { error: 'Invalid pickup location. Latitude, longitude, and address are required' },
        { status: 400 }
      );
    }

    if (!dropoffLocation.latitude || !dropoffLocation.longitude || !dropoffLocation.address) {
      return NextResponse.json(
        { error: 'Invalid dropoff location. Latitude, longitude, and address are required' },
        { status: 400 }
      );
    }

    // Validate scheduled time
    const scheduledDate = new Date(scheduledTime);
    if (scheduledDate <= new Date()) {
      return NextResponse.json(
        { error: 'Scheduled time must be in the future' },
        { status: 400 }
      );
    }

    // Create corporate booking
    const booking = await corporateMobilityService.createCorporateBooking({
      clientId,
      employeeId: decoded.userId,
      type: type || 'business_travel',
      priority: priority || 'standard',
      pickupLocation,
      dropoffLocation,
      scheduledTime: scheduledDate,
      isRecurring: isRecurring || false,
      recurringPattern,
      passengers: passengers || [
        {
          name: user.name || 'Employee',
          phone: user.phone || '+91-0000000000',
          isVip: false,
        }
      ],
      vehiclePreferences: vehiclePreferences || {
        type: 'economy',
        features: [],
        accessibility: false,
      },
      purpose: purpose || 'Business travel',
      costCenter: costCenter || 'General',
      projectCode,
      clientMeeting,
    });

    const response = {
      success: true,
      data: {
        booking: {
          id: booking.id,
          type: booking.type,
          priority: booking.priority,
          status: booking.status,
          approvalStatus: booking.approvalStatus,
          scheduledTime: booking.scheduledTime,
          pickupLocation: booking.pickupLocation,
          dropoffLocation: booking.dropoffLocation,
          passengers: booking.passengers,
          vehiclePreferences: booking.vehiclePreferences,
          purpose: booking.purpose,
          costCenter: booking.costCenter,
          projectCode: booking.projectCode,
          pricing: booking.pricing,
          isRecurring: booking.isRecurring,
          recurringPattern: booking.recurringPattern,
          createdAt: booking.createdAt,
        },
        nextSteps: [
          'Your corporate booking has been created',
          booking.approvalStatus === 'pending' 
            ? 'Waiting for approval from your manager'
            : 'Booking confirmed - driver will be assigned closer to pickup time',
          `Estimated cost: ₹${booking.pricing.total}`,
          'You will receive updates via email and SMS',
        ],
        approval: {
          required: booking.approvalStatus === 'pending',
          status: booking.approvalStatus,
          estimatedApprovalTime: booking.approvalStatus === 'pending' ? '2-4 hours' : null,
        },
      },
      metadata: {
        bookingId: booking.id,
        clientId: booking.clientId,
        employeeId: booking.employeeId,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Create corporate booking API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create corporate booking',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const allowedRoles = ['corporate_admin', 'corporate_user', 'admin'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Corporate access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Mock corporate bookings (in real app, would fetch from database)
    const mockBookings = [
      {
        id: 'corp_001',
        clientId: 'corp_001',
        employeeId: decoded.userId,
        type: 'business_travel',
        priority: 'standard',
        status: 'completed',
        approvalStatus: 'approved',
        scheduledTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
        actualPickupTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
        actualDropoffTime: new Date(Date.now() - 1 * 60 * 60 * 1000),
        pickupLocation: { address: 'Office Complex, Bangalore', latitude: 12.9716, longitude: 77.5946 },
        dropoffLocation: { address: 'Client Office, Bangalore', latitude: 12.9784, longitude: 77.6408 },
        purpose: 'Client meeting',
        costCenter: 'Sales',
        pricing: { total: 450, corporateDiscount: 75 },
        rating: 5,
        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
      },
      {
        id: 'corp_002',
        clientId: 'corp_001',
        employeeId: decoded.userId,
        type: 'airport_transfer',
        priority: 'high',
        status: 'confirmed',
        approvalStatus: 'approved',
        scheduledTime: new Date(Date.now() + 4 * 60 * 60 * 1000),
        pickupLocation: { address: 'Hotel Grand, Bangalore', latitude: 12.9352, longitude: 77.6245 },
        dropoffLocation: { address: 'Kempegowda International Airport', latitude: 13.1986, longitude: 77.7066 },
        purpose: 'Business trip to Mumbai',
        costCenter: 'Marketing',
        pricing: { total: 850, corporateDiscount: 127 },
        vehiclePreferences: { type: 'premium', features: ['wifi', 'charging'] },
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      },
    ];

    // Apply filters
    let filteredBookings = mockBookings;
    if (clientId) {
      filteredBookings = filteredBookings.filter(b => b.clientId === clientId);
    }
    if (status) {
      filteredBookings = filteredBookings.filter(b => b.status === status);
    }
    if (type) {
      filteredBookings = filteredBookings.filter(b => b.type === type);
    }

    // Apply pagination
    const paginatedBookings = filteredBookings.slice(offset, offset + limit);

    // Calculate summary statistics
    const totalBookings = filteredBookings.length;
    const totalSpent = filteredBookings
      .filter(b => b.status === 'completed')
      .reduce((sum, b) => sum + b.pricing.total, 0);
    const totalSavings = filteredBookings
      .reduce((sum, b) => sum + (b.pricing.corporateDiscount || 0), 0);

    const response = {
      success: true,
      data: {
        bookings: paginatedBookings,
        pagination: {
          total: filteredBookings.length,
          limit,
          offset,
          hasMore: offset + limit < filteredBookings.length,
        },
        summary: {
          totalBookings,
          totalSpent,
          totalSavings,
          averageBookingValue: totalBookings > 0 ? Math.round(totalSpent / totalBookings) : 0,
          pendingApprovals: filteredBookings.filter(b => b.approvalStatus === 'pending').length,
          upcomingBookings: filteredBookings.filter(b => 
            new Date(b.scheduledTime) > new Date() && b.status !== 'cancelled'
          ).length,
        },
        analytics: {
          bookingsByType: this.getBookingsByType(filteredBookings),
          bookingsByStatus: this.getBookingsByStatus(filteredBookings),
          costCenterBreakdown: this.getCostCenterBreakdown(filteredBookings),
          monthlySpend: this.getMonthlySpend(filteredBookings),
        },
        filters: {
          availableStatuses: ['scheduled', 'confirmed', 'driver_assigned', 'in_progress', 'completed', 'cancelled'],
          availableTypes: ['business_travel', 'employee_transport', 'event_logistics', 'airport_transfer', 'client_pickup'],
          availablePriorities: ['standard', 'high', 'urgent'],
        },
        quickActions: [
          { action: 'book_ride', label: 'Book New Ride', available: true },
          { action: 'recurring_booking', label: 'Set Up Recurring', available: true },
          { action: 'expense_report', label: 'Generate Report', available: totalBookings > 0 },
          { action: 'approve_bookings', label: 'Approve Pending', available: user.role === 'corporate_admin' },
        ],
      },
      metadata: {
        userId: decoded.userId,
        userRole: user.role,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get corporate bookings API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch corporate bookings',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Helper functions for analytics (would be moved to service in real implementation)
function getBookingsByType(bookings: any[]): Record<string, number> {
  return bookings.reduce((acc, booking) => {
    acc[booking.type] = (acc[booking.type] || 0) + 1;
    return acc;
  }, {});
}

function getBookingsByStatus(bookings: any[]): Record<string, number> {
  return bookings.reduce((acc, booking) => {
    acc[booking.status] = (acc[booking.status] || 0) + 1;
    return acc;
  }, {});
}

function getCostCenterBreakdown(bookings: any[]): { costCenter: string; amount: number; count: number }[] {
  const breakdown = bookings.reduce((acc, booking) => {
    const costCenter = booking.costCenter || 'General';
    if (!acc[costCenter]) {
      acc[costCenter] = { amount: 0, count: 0 };
    }
    acc[costCenter].amount += booking.pricing.total;
    acc[costCenter].count += 1;
    return acc;
  }, {} as Record<string, { amount: number; count: number }>);

  return Object.entries(breakdown).map(([costCenter, data]) => ({
    costCenter,
    amount: data.amount,
    count: data.count,
  }));
}

function getMonthlySpend(bookings: any[]): { month: string; amount: number }[] {
  const monthlyData = bookings
    .filter(b => b.status === 'completed')
    .reduce((acc, booking) => {
      const month = new Date(booking.createdAt).toISOString().slice(0, 7); // YYYY-MM
      acc[month] = (acc[month] || 0) + booking.pricing.total;
      return acc;
    }, {} as Record<string, number>);

  return Object.entries(monthlyData).map(([month, amount]) => ({ month, amount }));
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
