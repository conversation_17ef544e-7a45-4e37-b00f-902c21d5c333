'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Car, 
  Bike, 
  Package, 
  Building2, 
  User, 
  MapPin, 
  Clock, 
  Star,
  TrendingUp,
  Leaf,
  MessageCircle,
  Phone,
  Navigation,
  DollarSign,
  BarChart3,
  Shield,
  Zap
} from 'lucide-react';

export default function InterfaceDemo() {
  const [activeTab, setActiveTab] = useState('homepage');
  const [selectedVehicle, setSelectedVehicle] = useState('bike');

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          🎨 MobilityHub Interface Demo
        </h1>
        <p className="text-xl text-gray-600">
          Experience the complete user interface of our Multi-Modal Transportation Ecosystem
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="homepage">🏠 Homepage</TabsTrigger>
          <TabsTrigger value="rides">🚗 Rides</TabsTrigger>
          <TabsTrigger value="vehicles">🛴 Vehicles</TabsTrigger>
          <TabsTrigger value="delivery">📦 Delivery</TabsTrigger>
          <TabsTrigger value="corporate">🏢 Corporate</TabsTrigger>
          <TabsTrigger value="profile">👤 Profile</TabsTrigger>
        </TabsList>

        {/* Homepage Interface */}
        <TabsContent value="homepage" className="space-y-6">
          <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <h2 className="text-3xl font-bold">The Future of Urban Mobility</h2>
                <p className="text-xl opacity-90">
                  One platform for rides, delivery, micro-mobility and corporate travel
                </p>
                <div className="flex justify-center space-x-4">
                  <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                    📱 Download App
                  </Button>
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                    ▶️ Watch Demo
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-blue-600">2,500,000+</div>
                <div className="text-gray-600">Total Rides</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-green-600">50+</div>
                <div className="text-gray-600">Cities</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-purple-600">150,000+</div>
                <div className="text-gray-600">Happy Users</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-green-600">45,000kg</div>
                <div className="text-gray-600">CO₂ Saved</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Car className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-bold mb-2">Smart Ride Sharing</h3>
                <p className="text-sm text-gray-600">AI-powered matching with real-time tracking</p>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Bike className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-bold mb-2">Micro-Mobility</h3>
                <p className="text-sm text-gray-600">Bikes and scooters with IoT tracking</p>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Package className="h-12 w-12 text-orange-600 mx-auto mb-4" />
                <h3 className="font-bold mb-2">Delivery Ecosystem</h3>
                <p className="text-sm text-gray-600">Food and package delivery solutions</p>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardContent className="p-6 text-center">
                <Building2 className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h3 className="font-bold mb-2">Corporate Solutions</h3>
                <p className="text-sm text-gray-600">Business travel and analytics</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Ride Booking Interface */}
        <TabsContent value="rides" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Car className="h-6 w-6 mr-2" />
                Book a Ride
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">📍 From</label>
                  <div className="p-3 border rounded-lg bg-gray-50">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-blue-600 mr-2" />
                      <span>123 MG Road, Bangalore</span>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">📍 To</label>
                  <div className="p-3 border rounded-lg">
                    <input 
                      type="text" 
                      placeholder="Where to?" 
                      className="w-full outline-none"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-3">🚲 Vehicle Type</label>
                <div className="grid grid-cols-4 gap-3">
                  {[
                    { id: 'bike', icon: '🚲', name: 'Bike', price: '₹5/km' },
                    { id: 'scooter', icon: '🛴', name: 'Scooter', price: '₹7/km' },
                    { id: 'car', icon: '🚗', name: 'Car', price: '₹15/km' },
                    { id: 'auto', icon: '🚐', name: 'Auto', price: '₹12/km' },
                  ].map((vehicle) => (
                    <Card 
                      key={vehicle.id}
                      className={`cursor-pointer transition-all ${
                        selectedVehicle === vehicle.id 
                          ? 'ring-2 ring-blue-500 bg-blue-50' 
                          : 'hover:shadow-md'
                      }`}
                      onClick={() => setSelectedVehicle(vehicle.id)}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl mb-2">{vehicle.icon}</div>
                        <div className="font-medium text-sm">{vehicle.name}</div>
                        <div className="text-xs text-gray-600">{vehicle.price}</div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">📅 Date</label>
                  <div className="p-3 border rounded-lg">Now</div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">🕐 Time</label>
                  <div className="p-3 border rounded-lg">10:30 AM</div>
                </div>
              </div>

              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center">
                      <DollarSign className="h-4 w-4 text-green-600 mr-2" />
                      <span>Estimated Cost: ₹85</span>
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-blue-600 mr-2" />
                      <span>Estimated Time: 15 min</span>
                    </div>
                    <div className="flex items-center">
                      <Navigation className="h-4 w-4 text-purple-600 mr-2" />
                      <span>Distance: 5.2 km</span>
                    </div>
                    <div className="flex items-center">
                      <Leaf className="h-4 w-4 text-green-600 mr-2" />
                      <span>Carbon Saved: 2.1 kg</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button className="w-full" size="lg">
                🚗 Book Ride - ₹85
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Vehicle Map Interface */}
        <TabsContent value="vehicles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Bike className="h-6 w-6 mr-2" />
                  Find Nearby Vehicles
                </div>
                <Button variant="outline" size="sm">🔄 Refresh</Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center relative">
                <div className="text-gray-500">🗺️ Interactive Map View</div>
                <div className="absolute top-4 left-4 text-2xl">🛴</div>
                <div className="absolute top-8 right-8 text-2xl">🚲</div>
                <div className="absolute bottom-4 left-8 text-2xl">🛴</div>
                <div className="absolute bottom-8 right-4 text-2xl">🚲</div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="bg-blue-600 text-white px-2 py-1 rounded text-sm">📍 You</div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Badge variant="outline">🚲 Bikes</Badge>
                <Badge variant="outline">🛴 Scooters</Badge>
                <Badge variant="outline">⚡ Electric Only</Badge>
              </div>

              <div className="space-y-3">
                <h3 className="font-medium">📋 Available Vehicles:</h3>
                {[
                  { type: '🚲', id: 'B001', distance: '50m', battery: '85%', price: '₹5/km', unlock: '₹10' },
                  { type: '🛴', id: 'S045', distance: '120m', battery: '92%', price: '₹7/km', unlock: '₹15' },
                  { type: '🚲', id: 'B023', distance: '200m', battery: '78%', price: '₹5/km', unlock: '₹10' },
                ].map((vehicle, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{vehicle.type}</span>
                          <div>
                            <div className="font-medium">{vehicle.type === '🚲' ? 'Bike' : 'Scooter'} #{vehicle.id}</div>
                            <div className="text-sm text-gray-600 flex items-center space-x-4">
                              <span>📍 {vehicle.distance} away</span>
                              <span>🔋 {vehicle.battery}</span>
                              <span>{vehicle.price}</span>
                            </div>
                          </div>
                        </div>
                        <Button size="sm">
                          🔓 Unlock - {vehicle.unlock}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Delivery Interface */}
        <TabsContent value="delivery" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Package className="h-6 w-6 mr-2" />
                  Delivery Services
                </div>
                <Badge>🛒 Cart: 0</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex space-x-4">
                <Button variant="outline" className="flex-1">🍽️ Food Delivery</Button>
                <Button variant="outline" className="flex-1">📦 Package Delivery</Button>
                <Button variant="outline" className="flex-1">🏪 Grocery</Button>
              </div>

              <div className="relative">
                <input 
                  type="text" 
                  placeholder="Search restaurants, cuisines, dishes..." 
                  className="w-full p-3 border rounded-lg pl-10"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">🔍</div>
              </div>

              <div className="flex space-x-2 overflow-x-auto">
                {['🍕 Pizza', '🍔 Burgers', '🍜 Asian', '🥗 Healthy', '☕ Cafe'].map((category) => (
                  <Badge key={category} variant="outline" className="whitespace-nowrap">
                    {category}
                  </Badge>
                ))}
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">🍴 Popular Restaurants:</h3>
                {[
                  { name: 'Pizza Palace', rating: '4.5', distance: '2.1 km', time: '25 min', cuisine: 'Italian • ₹₹', offer: 'Free delivery on ₹300+' },
                  { name: 'Burger Junction', rating: '4.3', distance: '1.8 km', time: '20 min', cuisine: 'Fast Food • ₹', offer: '50% off on first order' },
                  { name: 'Noodle House', rating: '4.7', distance: '3.2 km', time: '30 min', cuisine: 'Chinese • ₹₹', offer: 'Highly rated' },
                ].map((restaurant, index) => (
                  <Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h4 className="font-medium">{restaurant.name}</h4>
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              <span className="text-sm ml-1">{restaurant.rating}</span>
                            </div>
                          </div>
                          <div className="text-sm text-gray-600 mb-1">{restaurant.cuisine}</div>
                          <div className="text-sm text-gray-600 flex items-center space-x-4">
                            <span>📍 {restaurant.distance}</span>
                            <span>⏱️ {restaurant.time}</span>
                          </div>
                          <div className="text-sm text-green-600 mt-1">{restaurant.offer}</div>
                        </div>
                        <Button variant="outline" size="sm">View Menu</Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Corporate Dashboard */}
        <TabsContent value="corporate" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Building2 className="h-6 w-6 mr-2" />
                  Corporate Dashboard - TechCorp Ltd.
                </div>
                <Badge>👤 Admin</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">1,250</div>
                    <div className="text-sm text-gray-600">Total Trips</div>
                    <div className="flex items-center justify-center mt-1">
                      <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                      <span className="text-xs text-green-600">+15%</span>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">₹2,45,000</div>
                    <div className="text-sm text-gray-600">Total Spend</div>
                    <div className="flex items-center justify-center mt-1">
                      <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                      <span className="text-xs text-green-600">+12%</span>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">45</div>
                    <div className="text-sm text-gray-600">Employees Active</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">850 kg</div>
                    <div className="text-sm text-gray-600">CO₂ Saved</div>
                    <div className="flex items-center justify-center mt-1">
                      <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                      <span className="text-xs text-green-600">+25%</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="bg-gray-50">
                <CardContent className="p-4">
                  <h3 className="font-medium mb-3">📈 Monthly Trends</h3>
                  <div className="h-32 bg-white rounded flex items-center justify-center">
                    <span className="text-gray-500">📊 Interactive Chart</span>
                  </div>
                </CardContent>
              </Card>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                <Button variant="outline" className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Manage Employees
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Car className="h-4 w-4 mr-2" />
                  Book Travel
                </Button>
                <Button variant="outline" className="flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Shield className="h-4 w-4 mr-2" />
                  Settings
                </Button>
                <Button variant="outline" className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-2" />
                  Billing
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Profile */}
        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <User className="h-6 w-6 mr-2" />
                  Profile - Rohit Sharma
                </div>
                <Button variant="outline" size="sm">⚙️ Settings</Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">127</div>
                    <div className="text-sm text-gray-600">Total Trips</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">₹8,450</div>
                    <div className="text-sm text-gray-600">Total Spent</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-yellow-600 flex items-center justify-center">
                      4.9 <Star className="h-4 w-4 ml-1 fill-current" />
                    </div>
                    <div className="text-sm text-gray-600">Rating</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">45 kg</div>
                    <div className="text-sm text-gray-600">CO₂ Saved</div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <h3 className="font-medium mb-3">🎯 Achievements</h3>
                <div className="flex space-x-2">
                  <Badge className="bg-green-100 text-green-800">🏆 Eco Warrior</Badge>
                  <Badge className="bg-blue-100 text-blue-800">🚀 Speed Demon</Badge>
                  <Badge className="bg-yellow-100 text-yellow-800">⭐ 5-Star Rider</Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <Button variant="outline" className="flex items-center">
                  <Car className="h-4 w-4 mr-2" />
                  Book Ride
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Bike className="h-4 w-4 mr-2" />
                  Find Vehicle
                </Button>
                <Button variant="outline" className="flex items-center">
                  <Package className="h-4 w-4 mr-2" />
                  Order Food
                </Button>
                <Button variant="outline" className="flex items-center">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View History
                </Button>
              </div>

              <Card className="bg-green-50 border-green-200">
                <CardContent className="p-4">
                  <h3 className="font-medium mb-2 flex items-center">
                    <Leaf className="h-5 w-5 text-green-600 mr-2" />
                    🌱 Carbon Impact
                  </h3>
                  <p className="text-sm text-gray-700 mb-3">
                    This month you saved 12kg CO₂ compared to using cars!
                  </p>
                  <Button size="sm" className="bg-green-600 hover:bg-green-700">
                    🌳 Offset Remaining Carbon - ₹150
                  </Button>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Real-time Chat Demo */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <MessageCircle className="h-6 w-6 mr-2" />
              💬 Real-Time Chat Demo
            </div>
            <Button variant="outline" size="sm">
              <Phone className="h-4 w-4 mr-2" />
              📞 Call
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3 max-h-64 overflow-y-auto">
            <div className="bg-gray-100 p-3 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">🤖 System</div>
              <div>Ride confirmed! Rajesh is on the way. ETA: 5 minutes</div>
            </div>
            <div className="bg-blue-100 p-3 rounded-lg ml-8">
              <div className="text-sm text-gray-600 mb-1">👨‍💼 Rajesh</div>
              <div>Hi! I'm 2 minutes away from pickup location.</div>
              <div className="text-xs text-gray-500 mt-1">🕐 2:45 PM</div>
            </div>
            <div className="bg-green-100 p-3 rounded-lg mr-8">
              <div className="text-sm text-gray-600 mb-1">👤 You</div>
              <div>Great! I'm waiting near the main gate.</div>
              <div className="text-xs text-gray-500 mt-1">🕐 2:46 PM</div>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <div className="flex space-x-2 mb-3">
              <Badge variant="outline">👋 I'm here</Badge>
              <Badge variant="outline">⏰ Running late</Badge>
              <Badge variant="outline">📍 Share location</Badge>
            </div>
            <div className="flex space-x-2">
              <input 
                type="text" 
                placeholder="Type a message..." 
                className="flex-1 p-2 border rounded-lg"
              />
              <Button size="sm">🎤</Button>
              <Button size="sm">📷</Button>
              <Button size="sm">📍</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="text-center mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          🎉 Complete Interface Experience
        </h3>
        <p className="text-gray-600 mb-4">
          This demo showcases the comprehensive user interface of our Multi-Modal Transportation Ecosystem.
          Every screen is designed for optimal user experience across all devices.
        </p>
        <div className="flex justify-center space-x-4">
          <Button>🚀 Launch Platform</Button>
          <Button variant="outline">📖 View Documentation</Button>
        </div>
      </div>
    </div>
  );
}
