'use client';

import { ReactNode, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  MoreHorizontal, 
  ExternalLink,
  Star,
  Heart,
  Share2
} from 'lucide-react';

interface EnhancedCardProps {
  children?: ReactNode;
  className?: string;
  variant?: 'default' | 'gradient' | 'glass' | 'elevated' | 'bordered' | 'interactive';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  clickable?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  trend?: {
    value: number;
    label: string;
    direction: 'up' | 'down' | 'neutral';
  };
  icon?: ReactNode;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'gray';
  loading?: boolean;
}

interface FeatureCardProps {
  title: string;
  description: string;
  icon?: ReactNode;
  badge?: string;
  image?: string;
  features?: string[];
  action?: {
    label: string;
    onClick: () => void;
  };
  variant?: 'default' | 'premium' | 'popular';
}

interface TestimonialCardProps {
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
  verified?: boolean;
}

// Enhanced Base Card Component
export const EnhancedCard = forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ children, className, variant = 'default', size = 'md', hover = false, clickable = false, loading = false, onClick, ...props }, ref) => {
    const variants = {
      default: 'bg-white border border-gray-200 shadow-sm',
      gradient: 'bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 shadow-md',
      glass: 'bg-white/80 backdrop-blur-md border border-white/20 shadow-lg',
      elevated: 'bg-white border-0 shadow-xl',
      bordered: 'bg-white border-2 border-gray-300 shadow-none',
      interactive: 'bg-white border border-gray-200 shadow-sm hover:shadow-xl hover:border-blue-300 transition-all duration-300',
    };

    const sizes = {
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8',
      xl: 'p-10',
    };

    const baseClasses = cn(
      'rounded-xl transition-all duration-300',
      variants[variant],
      sizes[size],
      hover && 'hover:shadow-lg hover:-translate-y-1',
      clickable && 'cursor-pointer hover:shadow-lg hover:-translate-y-1',
      loading && 'animate-pulse',
      className
    );

    if (loading) {
      return (
        <div ref={ref} className={baseClasses} {...props}>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
            <div className="h-8 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
        </div>
      );
    }

    return (
      <div ref={ref} className={baseClasses} onClick={onClick} {...props}>
        {children}
      </div>
    );
  }
);

EnhancedCard.displayName = 'EnhancedCard';

// Stats Card Component
export function StatsCard({ title, value, description, trend, icon, color = 'blue', loading }: StatsCardProps) {
  const colors = {
    blue: 'text-blue-600 bg-blue-100',
    green: 'text-green-600 bg-green-100',
    purple: 'text-purple-600 bg-purple-100',
    orange: 'text-orange-600 bg-orange-100',
    red: 'text-red-600 bg-red-100',
    gray: 'text-gray-600 bg-gray-100',
  };

  const trendColors = {
    up: 'text-green-600 bg-green-100',
    down: 'text-red-600 bg-red-100',
    neutral: 'text-gray-600 bg-gray-100',
  };

  if (loading) {
    return (
      <EnhancedCard loading />
    );
  }

  return (
    <EnhancedCard variant="elevated" hover>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mb-2">{value}</p>
          {description && (
            <p className="text-sm text-gray-500">{description}</p>
          )}
          {trend && (
            <div className={cn('inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-2', trendColors[trend.direction])}>
              {trend.direction === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
              {trend.direction === 'down' && <TrendingDown className="h-3 w-3 mr-1" />}
              {trend.value}% {trend.label}
            </div>
          )}
        </div>
        {icon && (
          <div className={cn('p-3 rounded-full', colors[color])}>
            {icon}
          </div>
        )}
      </div>
    </EnhancedCard>
  );
}

// Feature Card Component
export function FeatureCard({ title, description, icon, badge, image, features, action, variant = 'default' }: FeatureCardProps) {
  const variants = {
    default: 'border-gray-200',
    premium: 'border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50',
    popular: 'border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50 relative',
  };

  return (
    <EnhancedCard className={cn('relative overflow-hidden', variants[variant])} hover clickable>
      {variant === 'popular' && (
        <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 text-xs font-medium rounded-bl-lg">
          Most Popular
        </div>
      )}
      
      {badge && (
        <Badge className="mb-4 bg-blue-100 text-blue-800">{badge}</Badge>
      )}
      
      {image && (
        <div className="mb-4 rounded-lg overflow-hidden">
          <img src={image} alt={title} className="w-full h-48 object-cover" />
        </div>
      )}
      
      <div className="flex items-center mb-4">
        {icon && (
          <div className="p-2 bg-blue-100 rounded-lg mr-3">
            {icon}
          </div>
        )}
        <h3 className="text-xl font-semibold">{title}</h3>
      </div>
      
      <p className="text-gray-600 mb-4">{description}</p>
      
      {features && (
        <ul className="space-y-2 mb-6">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center text-sm text-gray-600">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-2"></div>
              {feature}
            </li>
          ))}
        </ul>
      )}
      
      {action && (
        <Button onClick={action.onClick} className="w-full">
          {action.label}
          <ExternalLink className="ml-2 h-4 w-4" />
        </Button>
      )}
    </EnhancedCard>
  );
}

// Testimonial Card Component
export function TestimonialCard({ name, role, company, content, rating, avatar, verified }: TestimonialCardProps) {
  return (
    <EnhancedCard variant="glass" hover>
      <div className="flex items-center mb-4">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={cn('h-4 w-4', i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300')}
          />
        ))}
        {verified && (
          <Badge className="ml-2 bg-green-100 text-green-800 text-xs">Verified</Badge>
        )}
      </div>
      
      <blockquote className="text-gray-700 mb-6 italic">
        "{content}"
      </blockquote>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white font-semibold mr-3">
            {avatar ? (
              <img src={avatar} alt={name} className="w-10 h-10 rounded-full object-cover" />
            ) : (
              name.charAt(0)
            )}
          </div>
          <div>
            <p className="font-semibold text-gray-900">{name}</p>
            <p className="text-sm text-gray-600">{role}, {company}</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm">
            <Heart className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </EnhancedCard>
  );
}

// Action Card Component
export function ActionCard({ 
  title, 
  description, 
  primaryAction, 
  secondaryAction, 
  icon, 
  color = 'blue' 
}: {
  title: string;
  description: string;
  primaryAction: { label: string; onClick: () => void };
  secondaryAction?: { label: string; onClick: () => void };
  icon?: ReactNode;
  color?: 'blue' | 'green' | 'purple' | 'orange';
}) {
  const colors = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    purple: 'from-purple-500 to-purple-600',
    orange: 'from-orange-500 to-orange-600',
  };

  return (
    <EnhancedCard className={cn('bg-gradient-to-r text-white', colors[color])} hover>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            {icon && <div className="mr-3">{icon}</div>}
            <h3 className="text-xl font-semibold">{title}</h3>
          </div>
          <p className="text-white/90 mb-4">{description}</p>
          <div className="flex space-x-3">
            <Button 
              onClick={primaryAction.onClick}
              className="bg-white text-gray-900 hover:bg-gray-100"
            >
              {primaryAction.label}
            </Button>
            {secondaryAction && (
              <Button 
                onClick={secondaryAction.onClick}
                variant="outline"
                className="border-white text-white hover:bg-white/10"
              >
                {secondaryAction.label}
              </Button>
            )}
          </div>
        </div>
      </div>
    </EnhancedCard>
  );
}
