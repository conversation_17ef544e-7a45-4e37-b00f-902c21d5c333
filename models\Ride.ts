import { Schema, model, models, Document } from 'mongoose';

export interface IRide extends Document {
  _id: string;
  riderId: string;
  driverId?: string;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  
  // Location details
  pickupLocation: {
    address: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
    landmark?: string;
  };
  
  dropoffLocation: {
    address: string;
    coordinates: {
      latitude: number;
      longitude: number;
    };
    landmark?: string;
  };
  
  // Ride details
  estimatedDistance: number; // in kilometers
  estimatedDuration: number; // in minutes
  actualDistance?: number;
  actualDuration?: number;
  
  // Pricing
  baseFare: number;
  distanceFare: number;
  timeFare: number;
  totalFare: number;
  discountAmount: number;
  finalAmount: number;
  
  // Timing
  requestedAt: Date;
  acceptedAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
  
  // Additional details
  rideType: 'standard' | 'premium' | 'shared';
  paymentMethod: 'cash' | 'card' | 'wallet' | 'upi';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
  
  // Ratings and feedback
  riderRating?: number;
  driverRating?: number;
  riderFeedback?: string;
  driverFeedback?: string;
  
  // Special requirements
  specialInstructions?: string;
  isScheduled: boolean;
  scheduledTime?: Date;
  
  // Tracking
  route?: {
    coordinates: number[][];
    distance: number;
    duration: number;
  };
  
  // Cancellation
  cancellationReason?: string;
  cancelledBy?: 'rider' | 'driver' | 'system';
  cancellationFee?: number;
  
  createdAt: Date;
  updatedAt: Date;
}

const RideSchema = new Schema<IRide>({
  riderId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  driverId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'in_progress', 'completed', 'cancelled'],
    default: 'pending',
  },
  
  pickupLocation: {
    address: {
      type: String,
      required: true,
    },
    coordinates: {
      latitude: {
        type: Number,
        required: true,
      },
      longitude: {
        type: Number,
        required: true,
      },
    },
    landmark: String,
  },
  
  dropoffLocation: {
    address: {
      type: String,
      required: true,
    },
    coordinates: {
      latitude: {
        type: Number,
        required: true,
      },
      longitude: {
        type: Number,
        required: true,
      },
    },
    landmark: String,
  },
  
  estimatedDistance: {
    type: Number,
    required: true,
  },
  estimatedDuration: {
    type: Number,
    required: true,
  },
  actualDistance: Number,
  actualDuration: Number,
  
  baseFare: {
    type: Number,
    required: true,
  },
  distanceFare: {
    type: Number,
    required: true,
  },
  timeFare: {
    type: Number,
    required: true,
  },
  totalFare: {
    type: Number,
    required: true,
  },
  discountAmount: {
    type: Number,
    default: 0,
  },
  finalAmount: {
    type: Number,
    required: true,
  },
  
  requestedAt: {
    type: Date,
    default: Date.now,
  },
  acceptedAt: Date,
  startedAt: Date,
  completedAt: Date,
  cancelledAt: Date,
  
  rideType: {
    type: String,
    enum: ['standard', 'premium', 'shared'],
    default: 'standard',
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'wallet', 'upi'],
    required: true,
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending',
  },
  
  riderRating: {
    type: Number,
    min: 1,
    max: 5,
  },
  driverRating: {
    type: Number,
    min: 1,
    max: 5,
  },
  riderFeedback: String,
  driverFeedback: String,
  
  specialInstructions: String,
  isScheduled: {
    type: Boolean,
    default: false,
  },
  scheduledTime: Date,
  
  route: {
    coordinates: [[Number]],
    distance: Number,
    duration: Number,
  },
  
  cancellationReason: String,
  cancelledBy: {
    type: String,
    enum: ['rider', 'driver', 'system'],
  },
  cancellationFee: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
});

// Indexes for performance
RideSchema.index({ riderId: 1 });
RideSchema.index({ driverId: 1 });
RideSchema.index({ status: 1 });
RideSchema.index({ requestedAt: -1 });
RideSchema.index({ 'pickupLocation.coordinates': '2dsphere' });
RideSchema.index({ 'dropoffLocation.coordinates': '2dsphere' });

export const Ride = models.Ride || model<IRide>('Ride', RideSchema);
