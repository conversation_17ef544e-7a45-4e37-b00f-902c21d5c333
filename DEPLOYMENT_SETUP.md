# Deployment Setup Guide

## GitHub Secrets Configuration

To enable the CI/CD pipeline, you need to configure the following secrets in your GitHub repository:

### Required Secrets

1. **GCP_PROJECT_ID**
   - Description: Your Google Cloud Project ID
   - Example: `my-two-wheeler-project`
   - How to find: Go to Google Cloud Console → Project Info

2. **GOOGLE_CLOUD_SA_KEY**
   - Description: Service Account J<PERSON>N key for Google Cloud authentication
   - Format: Complete JSON content of the service account key file
   - How to create:
     1. Go to Google Cloud Console → IAM & Admin → Service Accounts
     2. Create a new service account or use existing one
     3. Grant necessary permissions (Cloud Run Admin, Storage Admin, etc.)
     4. Create and download JSON key
     5. Copy the entire JSON content as the secret value

### Setting Up Secrets in GitHub

1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Click "New repository secret"
4. Add each secret with the exact name and value

### Required Google Cloud Permissions

The service account needs the following roles:
- Cloud Run Admin
- Storage Admin
- Container Registry Service Agent
- Service Account User

### Environment Setup

The CI/CD pipeline uses two environments:
- **staging**: Deploys from `develop` branch
- **production**: Deploys from `main` branch

Make sure to configure these environments in GitHub repository settings if you want additional protection rules.

## Local Development

For local development, create a `.env.local` file with:
```
GCP_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
```

## Troubleshooting

- If secrets are not recognized by IDE: This is normal - secrets are only available during GitHub Actions execution
- If deployment fails: Check Google Cloud Console for detailed error logs
- If authentication fails: Verify service account permissions and key validity
