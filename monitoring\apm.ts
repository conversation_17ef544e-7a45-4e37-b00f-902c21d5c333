// Application Performance Monitoring Service
import { performance } from 'perf_hooks';

export interface MetricData {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

export interface BusinessMetric {
  event: string;
  value: number;
  userId?: string;
  sessionId?: string;
  timestamp: number;
  properties?: Record<string, any>;
}

class APMService {
  private metrics: MetricData[] = [];
  private performanceMetrics: PerformanceMetric[] = [];
  private businessMetrics: BusinessMetric[] = [];
  private isEnabled: boolean = process.env.NODE_ENV === 'production';

  constructor() {
    // Initialize APM service
    this.setupPerformanceObserver();
    this.setupErrorHandling();
  }

  /**
   * Setup performance observer for automatic metrics collection
   */
  private setupPerformanceObserver() {
    if (typeof window !== 'undefined') {
      // Browser-side performance monitoring
      this.setupBrowserMetrics();
    } else {
      // Server-side performance monitoring
      this.setupServerMetrics();
    }
  }

  private setupBrowserMetrics() {
    // Web Vitals monitoring
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            name: 'web_vitals_lcp',
            value: entry.startTime,
            unit: 'ms',
            timestamp: Date.now(),
            tags: { type: 'performance' },
          });
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric({
            name: 'web_vitals_fid',
            value: (entry as any).processingStart - entry.startTime,
            unit: 'ms',
            timestamp: Date.now(),
            tags: { type: 'performance' },
          });
        }
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.recordMetric({
          name: 'web_vitals_cls',
          value: clsValue,
          unit: 'score',
          timestamp: Date.now(),
          tags: { type: 'performance' },
        });
      }).observe({ entryTypes: ['layout-shift'] });
    }
  }

  private setupServerMetrics() {
    // Server-side metrics collection
    setInterval(() => {
      if (process.memoryUsage) {
        const memUsage = process.memoryUsage();
        this.recordMetric({
          name: 'memory_usage_rss',
          value: memUsage.rss / 1024 / 1024,
          unit: 'MB',
          timestamp: Date.now(),
          tags: { type: 'system' },
        });

        this.recordMetric({
          name: 'memory_usage_heap_used',
          value: memUsage.heapUsed / 1024 / 1024,
          unit: 'MB',
          timestamp: Date.now(),
          tags: { type: 'system' },
        });
      }
    }, 30000); // Every 30 seconds
  }

  private setupErrorHandling() {
    if (typeof window !== 'undefined') {
      // Browser error handling
      window.addEventListener('error', (event) => {
        this.recordError({
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
        });
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.recordError({
          message: 'Unhandled Promise Rejection',
          reason: event.reason,
        });
      });
    } else {
      // Server error handling
      process.on('uncaughtException', (error) => {
        this.recordError({
          message: error.message,
          stack: error.stack,
          type: 'uncaughtException',
        });
      });

      process.on('unhandledRejection', (reason, promise) => {
        this.recordError({
          message: 'Unhandled Promise Rejection',
          reason: reason,
          type: 'unhandledRejection',
        });
      });
    }
  }

  /**
   * Record a custom metric
   */
  recordMetric(metric: MetricData) {
    if (!this.isEnabled) return;

    this.metrics.push(metric);
    
    // Send to external APM service (e.g., New Relic, DataDog)
    this.sendToAPMService('metric', metric);
    
    // Keep only last 1000 metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Record performance metric
   */
  recordPerformance(metric: PerformanceMetric) {
    if (!this.isEnabled) return;

    this.performanceMetrics.push(metric);
    
    // Convert to standard metric format
    this.recordMetric({
      name: `performance_${metric.operation}`,
      value: metric.duration,
      unit: 'ms',
      timestamp: metric.timestamp,
      tags: {
        operation: metric.operation,
        success: metric.success.toString(),
      },
      metadata: metric.metadata,
    });

    // Keep only last 1000 performance metrics
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    }
  }

  /**
   * Record business metric
   */
  recordBusinessMetric(metric: BusinessMetric) {
    if (!this.isEnabled) return;

    this.businessMetrics.push(metric);
    
    // Convert to standard metric format
    this.recordMetric({
      name: `business_${metric.event}`,
      value: metric.value,
      unit: 'count',
      timestamp: metric.timestamp,
      tags: {
        event: metric.event,
        userId: metric.userId || 'anonymous',
      },
      metadata: metric.properties,
    });

    // Keep only last 1000 business metrics
    if (this.businessMetrics.length > 1000) {
      this.businessMetrics = this.businessMetrics.slice(-1000);
    }
  }

  /**
   * Record error
   */
  recordError(error: any) {
    if (!this.isEnabled) return;

    const errorMetric: MetricData = {
      name: 'error_count',
      value: 1,
      unit: 'count',
      timestamp: Date.now(),
      tags: {
        type: 'error',
        message: error.message || 'Unknown error',
      },
      metadata: error,
    };

    this.recordMetric(errorMetric);
    
    // Send to error tracking service
    this.sendToErrorTracking(error);
  }

  /**
   * Time a function execution
   */
  async timeFunction<T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const startTime = performance.now();
    let success = true;
    let error: string | undefined;

    try {
      const result = await fn();
      return result;
    } catch (err) {
      success = false;
      error = err instanceof Error ? err.message : 'Unknown error';
      throw err;
    } finally {
      const duration = performance.now() - startTime;
      
      this.recordPerformance({
        operation,
        duration,
        timestamp: Date.now(),
        success,
        error,
        metadata,
      });
    }
  }

  /**
   * Create a timer for manual timing
   */
  startTimer(operation: string) {
    const startTime = performance.now();
    
    return {
      end: (success: boolean = true, metadata?: Record<string, any>) => {
        const duration = performance.now() - startTime;
        this.recordPerformance({
          operation,
          duration,
          timestamp: Date.now(),
          success,
          metadata,
        });
      },
    };
  }

  /**
   * Get current metrics summary
   */
  getMetricsSummary() {
    return {
      totalMetrics: this.metrics.length,
      totalPerformanceMetrics: this.performanceMetrics.length,
      totalBusinessMetrics: this.businessMetrics.length,
      recentMetrics: this.metrics.slice(-10),
      recentPerformanceMetrics: this.performanceMetrics.slice(-10),
      recentBusinessMetrics: this.businessMetrics.slice(-10),
    };
  }

  /**
   * Send data to external APM service
   */
  private async sendToAPMService(type: string, data: any) {
    try {
      // This would integrate with services like New Relic, DataDog, etc.
      if (process.env.APM_ENDPOINT) {
        await fetch(process.env.APM_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.APM_API_KEY}`,
          },
          body: JSON.stringify({
            type,
            data,
            timestamp: Date.now(),
            service: 'rideshare-platform',
            environment: process.env.NODE_ENV,
          }),
        });
      }
    } catch (error) {
      console.error('Failed to send APM data:', error);
    }
  }

  /**
   * Send error to error tracking service
   */
  private async sendToErrorTracking(error: any) {
    try {
      // This would integrate with services like Sentry, Bugsnag, etc.
      if (process.env.ERROR_TRACKING_ENDPOINT) {
        await fetch(process.env.ERROR_TRACKING_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ERROR_TRACKING_API_KEY}`,
          },
          body: JSON.stringify({
            error,
            timestamp: Date.now(),
            service: 'rideshare-platform',
            environment: process.env.NODE_ENV,
            userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
          }),
        });
      }
    } catch (err) {
      console.error('Failed to send error tracking data:', err);
    }
  }

  /**
   * Enable or disable APM
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }
}

// Export singleton instance
export const apmService = new APMService();
export default apmService;
