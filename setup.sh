#!/bin/bash

# Multi-Modal Transportation Ecosystem & Super App Platform Setup Script
# This script sets up the complete mobility ecosystem with AI automation

set -e

echo "🚀 Setting up Multi-Modal Transportation Ecosystem & Super App Platform..."
echo "🌟 Transforming from ride-sharing to comprehensive mobility solution..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directory structure..."
mkdir -p logs
mkdir -p nginx/ssl
mkdir -p n8n/workflows
mkdir -p data/mongodb
mkdir -p data/postgres
mkdir -p data/redis
mkdir -p scripts
mkdir -p fleet-data
mkdir -p transport-data

# Set permissions
chmod +x setup.sh
chmod 755 nginx/
chmod 755 n8n/workflows/

# Create environment file
echo "🔧 Creating environment configuration..."
cat > .env.local << EOF
# Application Configuration
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:3000
APP_VERSION=2.0.0

# Database Configuration - Multi-Modal Transportation Ecosystem
# For Docker MongoDB with authentication
MONGODB_URI=******************************************************************************************

# Alternative: For MongoDB Atlas (Cloud)
# MONGODB_URI=mongodb+srv://<username>:<password>@cluster0.xxxxx.mongodb.net/rideshare?retryWrites=true&w=majority

# Alternative: For local MongoDB without authentication
# MONGODB_URI=mongodb://localhost:27017/rideshare

POSTGRES_URL=*************************************/n8n
REDIS_URL=redis://redis:6379

# n8n Configuration
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=admin123
N8N_WEBHOOK_URL=http://localhost:5678/webhook

# MCP Server Configuration
MCP_SERVER_URL=http://localhost:8080
OLLAMA_URL=http://localhost:11434

# AI/ML Services Configuration
ML_MODEL_ENDPOINT=http://localhost:8000
PRICING_ML_ENDPOINT=http://localhost:8001
DEMAND_ML_ENDPOINT=http://localhost:8002
ETA_ML_ENDPOINT=http://localhost:8003
ML_API_KEY=your_ml_api_key

# External AI Services (Optional)
OPENAI_API_KEY=your_openai_api_key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
WEATHER_API_KEY=your_weather_api_key

# AI Feature Flags
ENABLE_DYNAMIC_PRICING=true
ENABLE_ROUTE_OPTIMIZATION=true
ENABLE_DEMAND_FORECASTING=true
ENABLE_ETA_PREDICTION=true

# Payment Processing
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# Real-time Communication
PUSHER_APP_ID=your_pusher_app_id
PUSHER_KEY=your_pusher_key
PUSHER_SECRET=your_pusher_secret
PUSHER_CLUSTER=ap2

# Security
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=your-32-character-encryption-key

# Notification Services
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
SENDGRID_API_KEY=your_sendgrid_key

# Monitoring and Analytics
SENTRY_DSN=your_sentry_dsn
GOOGLE_ANALYTICS_ID=your_ga_id

# External API Keys for Multi-Modal Transport
OPENWEATHERMAP_API_KEY=sample_key_12345
FIREBASE_API_KEY=sample_key_12345
TRANSIT_API_KEY=sample_key_12345

# Micro-Mobility Configuration
FLEET_MANAGEMENT_ENABLED=true
IOT_DEVICE_API_KEY=your_iot_api_key
VEHICLE_TRACKING_ENABLED=true

# Application Metadata
APP_VERSION=2.0.0
EOF

echo "✅ Environment file created. Please update API keys in .env file."

# Pull and start services
echo "🐳 Starting Docker services..."
docker-compose pull
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check MongoDB
echo "Checking MongoDB..."
docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" || echo "⚠️  MongoDB not ready yet"

# Check PostgreSQL
echo "Checking PostgreSQL..."
docker-compose exec -T postgres pg_isready -U n8n || echo "⚠️  PostgreSQL not ready yet"

# Check Redis
echo "Checking Redis..."
docker-compose exec -T redis redis-cli ping || echo "⚠️  Redis not ready yet"

# Setup Ollama models
echo "🤖 Setting up Ollama AI models..."
docker-compose exec -T ollama ollama pull llama2 || echo "⚠️  Ollama model download failed - will retry later"

# Import n8n workflows
echo "📋 Importing n8n workflows..."
sleep 10

# Create MongoDB initialization script for Multi-Modal Transportation Ecosystem
echo "🗄️  Setting up Multi-Modal Transportation Database..."
cat > scripts/init-mongo.js << EOF
// MongoDB initialization script for Multi-Modal Transportation Ecosystem
print('Starting MongoDB initialization for Multi-Modal Transportation Platform...');

// Switch to the rideshare database
db = db.getSiblingDB('rideshare');

// Create application user
db.createUser({
  user: 'rideshare_user',
  pwd: 'rideshare_password',
  roles: [
    {
      role: 'readWrite',
      db: 'rideshare'
    }
  ]
});

// Create collections with indexes for Multi-Modal Transportation
print('Creating collections and indexes for comprehensive mobility ecosystem...');

// Core Users collection
db.createCollection('users');
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ phone: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ location: '2dsphere' });
db.users.createIndex({ lastActiveAt: 1 });

// Rides collection (enhanced for multi-modal)
db.createCollection('rides');
db.rides.createIndex({ userId: 1 });
db.rides.createIndex({ driverId: 1 });
db.rides.createIndex({ status: 1 });
db.rides.createIndex({ createdAt: 1 });
db.rides.createIndex({ pickupLocation: '2dsphere' });
db.rides.createIndex({ dropoffLocation: '2dsphere' });
db.rides.createIndex({ rideType: 1 });
db.rides.createIndex({ estimatedDistance: 1 });

// Payments collection (unified for all transport modes)
db.createCollection('payments');
db.payments.createIndex({ userId: 1 });
db.payments.createIndex({ rideId: 1 });
db.payments.createIndex({ status: 1 });
db.payments.createIndex({ createdAt: 1 });
db.payments.createIndex({ paymentMethod: 1 });
db.payments.createIndex({ amount: 1 });

// Wallets collection (unified transport wallet)
db.createCollection('wallets');
db.wallets.createIndex({ userId: 1 }, { unique: true });
db.wallets.createIndex({ balance: 1 });
db.wallets.createIndex({ transitCredits: 1 });

// Notifications collection
db.createCollection('notifications');
db.notifications.createIndex({ userId: 1 });
db.notifications.createIndex({ createdAt: 1 });
db.notifications.createIndex({ read: 1 });
db.notifications.createIndex({ type: 1 });

// Chat messages collection
db.createCollection('chatmessages');
db.chatmessages.createIndex({ rideId: 1 });
db.chatmessages.createIndex({ senderId: 1 });
db.chatmessages.createIndex({ createdAt: 1 });

// Micro-mobility vehicles collection
db.createCollection('vehicles');
db.vehicles.createIndex({ location: '2dsphere' });
db.vehicles.createIndex({ status: 1 });
db.vehicles.createIndex({ type: 1 });
db.vehicles.createIndex({ 'battery.level': 1 });
db.vehicles.createIndex({ manufacturer: 1 });
db.vehicles.createIndex({ 'zone.id': 1 });

// Transport routes collection (public transport)
db.createCollection('transportroutes');
db.transportroutes.createIndex({ modeId: 1 });
db.transportroutes.createIndex({ routeNumber: 1 });
db.transportroutes.createIndex({ stops: 1 });

// Transport stops collection (public transport)
db.createCollection('transportstops');
db.transportstops.createIndex({ location: '2dsphere' });
db.transportstops.createIndex({ modes: 1 });
db.transportstops.createIndex({ name: 1 });

// Journey plans collection (multi-modal journeys)
db.createCollection('journeyplans');
db.journeyplans.createIndex({ userId: 1 });
db.journeyplans.createIndex({ fromLocation: '2dsphere' });
db.journeyplans.createIndex({ toLocation: '2dsphere' });
db.journeyplans.createIndex({ createdAt: 1 });

// Fleet management collection
db.createCollection('fleetoperations');
db.fleetoperations.createIndex({ vehicleId: 1 });
db.fleetoperations.createIndex({ operationType: 1 });
db.fleetoperations.createIndex({ scheduledAt: 1 });
db.fleetoperations.createIndex({ status: 1 });

// Analytics and business intelligence
db.createCollection('analytics');
db.analytics.createIndex({ eventType: 1 });
db.analytics.createIndex({ timestamp: 1 });
db.analytics.createIndex({ userId: 1 });
db.analytics.createIndex({ 'properties.rideType': 1 });

// Delivery and logistics (for future expansion)
db.createCollection('deliveries');
db.deliveries.createIndex({ senderId: 1 });
db.deliveries.createIndex({ driverId: 1 });
db.deliveries.createIndex({ status: 1 });
db.deliveries.createIndex({ pickupLocation: '2dsphere' });
db.deliveries.createIndex({ deliveryLocation: '2dsphere' });

print('Multi-Modal Transportation Database initialization completed successfully!');
print('Database: rideshare');
print('User: rideshare_user');
print('Collections created with comprehensive indexes for:');
print('- Ride-sharing and multi-modal transport');
print('- Micro-mobility fleet management');
print('- Public transport integration');
print('- Unified payments and wallets');
print('- Business intelligence and analytics');
print('- Future delivery and logistics expansion');
EOF

# Copy the initialization script to be used by Docker
cp scripts/init-mongo.js ./init-mongo.js

# Wait for MongoDB to be ready and then initialize
echo "⏳ Waiting for MongoDB to be ready..."
sleep 10

# Initialize the database
docker-compose exec -T mongodb mongosh < init-mongo.js || echo "⚠️  Database initialization will be retried..."

# Clean up
rm init-mongo.js

# Install application dependencies
echo "📦 Installing application dependencies..."
if [ -f "package.json" ]; then
    npm install
else
    echo "⚠️  package.json not found. Please run 'npm install' manually."
fi

# Install MCP server dependencies
echo "📦 Installing MCP server dependencies..."
cd mcp-server
npm install
cd ..

# Test database connection
echo "🔍 Testing database connection..."
node -e "
const mongoose = require('mongoose');
const uri = process.env.MONGODB_URI || '******************************************************************************************';
mongoose.connect(uri).then(() => {
  console.log('✅ MongoDB connection successful!');
  process.exit(0);
}).catch(err => {
  console.log('❌ MongoDB connection failed:', err.message);
  console.log('💡 Try: docker-compose up -d mongodb');
  process.exit(1);
});
" || echo "⚠️  Database connection test failed - will retry during app startup"

echo ""
echo "🎉 Multi-Modal Transportation Ecosystem Setup Completed Successfully!"
echo ""
echo "🌟 TRANSFORMATION COMPLETE: From Ride-Sharing to Mobility Super App!"
echo ""
echo "🌐 Access URLs:"
echo "  • 🚗 Main Application: http://localhost:3000"
echo "  • 🔧 n8n Workflows: http://localhost:5678 (admin/admin123)"
echo "  • 🤖 MCP Server: http://localhost:8080"
echo "  • 📊 Admin Dashboard: http://localhost:3000/admin"
echo "  • 🚙 Driver Dashboard: http://localhost:3000/driver"
echo "  • 🗄️  MongoDB UI: http://localhost:8081 (admin/admin123)"
echo ""
echo "🚀 NEW MULTI-MODAL FEATURES:"
echo "  • 🚌 Public Transport Integration"
echo "  • 🛴 Micro-Mobility (Bikes/Scooters)"
echo "  • 🗺️  Multi-Modal Journey Planning"
echo "  • 💳 Unified Payment System"
echo "  • 🤖 AI-Powered Route Optimization"
echo "  • 📱 Real-time Fleet Management"
echo "  • 🌱 Carbon Footprint Tracking"
echo "  • 💰 Dynamic Pricing Engine"
echo ""
echo "📚 Next Steps:"
echo "  1. 🔑 Update API keys in .env.local file"
echo "  2. 🌐 Access the app at http://localhost:3000"
echo "  3. 🔧 Configure n8n workflows at http://localhost:5678"
echo "  4. 🧪 Test multi-modal journey planning"
echo "  5. 📊 Monitor fleet operations in admin dashboard"
echo "  6. 🛴 Test micro-mobility vehicle finder"
echo ""
echo "🔧 Useful Commands:"
echo "  • View logs: docker-compose logs -f [service-name]"
echo "  • Restart services: docker-compose restart"
echo "  • Stop all services: docker-compose down"
echo "  • Update services: docker-compose pull && docker-compose up -d"
echo "  • MongoDB shell: docker-compose exec mongodb mongosh rideshare"
echo "  • Check fleet status: curl http://localhost:3000/api/micromobility/fleet"
echo ""
echo "🚨 TROUBLESHOOTING:"
echo "  • MongoDB connection issues: docker-compose up -d mongodb"
echo "  • Check service health: docker-compose ps"
echo "  • Reset database: docker-compose down -v && docker-compose up -d"
echo ""
echo "📖 For detailed documentation, see README.md"
echo ""
echo "🎯 BUSINESS IMPACT:"
echo "  • 📈 Multiple revenue streams (rideshare + transit + micro-mobility)"
echo "  • 🏆 Competitive moat through ecosystem integration"
echo "  • 🌍 Scalable to any city with public transport"
echo "  • 💡 AI-driven optimization for maximum efficiency"
echo "  • 🔄 Network effects increase with each new service"
echo ""
echo "🌟 Welcome to the future of urban mobility! 🌟"
