'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface RouteInfo {
  path: string;
  title: string;
  description?: string;
  category: string;
  requiresAuth: boolean;
  allowedRoles?: string[];
  icon?: any;
  breadcrumbs?: Array<{ label: string; href?: string }>;
}

interface NavigationState {
  currentRoute: RouteInfo | null;
  previousRoute: RouteInfo | null;
  navigationHistory: RouteInfo[];
  isLoading: boolean;
}

interface RouteManagerContextType {
  navigationState: NavigationState;
  navigateTo: (path: string, options?: NavigationOptions) => void;
  goBack: () => void;
  canGoBack: boolean;
  getRouteInfo: (path: string) => RouteInfo | null;
  updateRouteTitle: (title: string) => void;
  addToHistory: (route: RouteInfo) => void;
  clearHistory: () => void;
}

interface NavigationOptions {
  replace?: boolean;
  shallow?: boolean;
  scroll?: boolean;
}

// Route definitions
const routes: Record<string, RouteInfo> = {
  '/': {
    path: '/',
    title: 'Home',
    description: 'Multi-modal transportation platform',
    category: 'public',
    requiresAuth: false,
    breadcrumbs: [{ label: 'Home' }],
  },
  '/dashboard': {
    path: '/dashboard',
    title: 'Dashboard',
    description: 'Your mobility overview',
    category: 'user',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Dashboard' },
    ],
  },
  '/rides': {
    path: '/rides',
    title: 'Rides',
    description: 'Book and manage rides',
    category: 'rides',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Rides' },
    ],
  },
  '/rides/book': {
    path: '/rides/book',
    title: 'Book a Ride',
    description: 'Find and book your next ride',
    category: 'rides',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Rides', href: '/rides' },
      { label: 'Book Ride' },
    ],
  },
  '/rides/history': {
    path: '/rides/history',
    title: 'Ride History',
    description: 'View your past rides',
    category: 'rides',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Rides', href: '/rides' },
      { label: 'History' },
    ],
  },
  '/vehicles': {
    path: '/vehicles',
    title: 'Vehicles',
    description: 'Bikes and scooters',
    category: 'vehicles',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Vehicles' },
    ],
  },
  '/vehicles/nearby': {
    path: '/vehicles/nearby',
    title: 'Nearby Vehicles',
    description: 'Find vehicles near you',
    category: 'vehicles',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Vehicles', href: '/vehicles' },
      { label: 'Nearby' },
    ],
  },
  '/delivery': {
    path: '/delivery',
    title: 'Delivery',
    description: 'Food and package delivery',
    category: 'delivery',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Delivery' },
    ],
  },
  '/delivery/food': {
    path: '/delivery/food',
    title: 'Food Delivery',
    description: 'Order food from restaurants',
    category: 'delivery',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Delivery', href: '/delivery' },
      { label: 'Food' },
    ],
  },
  '/corporate': {
    path: '/corporate',
    title: 'Corporate',
    description: 'Business travel solutions',
    category: 'corporate',
    requiresAuth: true,
    allowedRoles: ['corporate_admin', 'corporate_user', 'admin'],
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Corporate' },
    ],
  },
  '/corporate/dashboard': {
    path: '/corporate/dashboard',
    title: 'Corporate Dashboard',
    description: 'Business travel analytics',
    category: 'corporate',
    requiresAuth: true,
    allowedRoles: ['corporate_admin', 'admin'],
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Corporate', href: '/corporate' },
      { label: 'Dashboard' },
    ],
  },
  '/profile': {
    path: '/profile',
    title: 'Profile',
    description: 'Manage your account',
    category: 'user',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Profile' },
    ],
  },
  '/settings': {
    path: '/settings',
    title: 'Settings',
    description: 'App preferences and configuration',
    category: 'user',
    requiresAuth: true,
    breadcrumbs: [
      { label: 'Home', href: '/' },
      { label: 'Settings' },
    ],
  },
};

// Create context
const RouteManagerContext = createContext<RouteManagerContextType | null>(null);

// Route Manager Provider
export function RouteManagerProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentRoute: null,
    previousRoute: null,
    navigationHistory: [],
    isLoading: false,
  });

  // Update current route when pathname changes
  useEffect(() => {
    const currentRoute = getRouteInfo(pathname);
    
    setNavigationState(prev => ({
      ...prev,
      previousRoute: prev.currentRoute,
      currentRoute,
      navigationHistory: currentRoute 
        ? [...prev.navigationHistory.slice(-9), currentRoute] // Keep last 10 routes
        : prev.navigationHistory,
    }));
  }, [pathname]);

  // Get route info by path
  const getRouteInfo = (path: string): RouteInfo | null => {
    // Try exact match first
    if (routes[path]) {
      return routes[path];
    }

    // Try to find parent route for dynamic routes
    const segments = path.split('/').filter(Boolean);
    for (let i = segments.length; i > 0; i--) {
      const parentPath = '/' + segments.slice(0, i).join('/');
      if (routes[parentPath]) {
        return {
          ...routes[parentPath],
          path,
          title: routes[parentPath].title,
        };
      }
    }

    // Fallback for unknown routes
    return {
      path,
      title: 'Page',
      category: 'unknown',
      requiresAuth: false,
      breadcrumbs: [{ label: 'Home', href: '/' }, { label: 'Page' }],
    };
  };

  // Navigate to a path
  const navigateTo = (path: string, options: NavigationOptions = {}) => {
    setNavigationState(prev => ({ ...prev, isLoading: true }));
    
    if (options.replace) {
      router.replace(path);
    } else {
      router.push(path);
    }
    
    // Reset loading state after navigation
    setTimeout(() => {
      setNavigationState(prev => ({ ...prev, isLoading: false }));
    }, 100);
  };

  // Go back to previous route
  const goBack = () => {
    if (navigationState.navigationHistory.length > 1) {
      const previousRoute = navigationState.navigationHistory[navigationState.navigationHistory.length - 2];
      navigateTo(previousRoute.path);
    } else {
      router.back();
    }
  };

  // Check if can go back
  const canGoBack = navigationState.navigationHistory.length > 1;

  // Update route title dynamically
  const updateRouteTitle = (title: string) => {
    setNavigationState(prev => ({
      ...prev,
      currentRoute: prev.currentRoute ? { ...prev.currentRoute, title } : null,
    }));
  };

  // Add route to history
  const addToHistory = (route: RouteInfo) => {
    setNavigationState(prev => ({
      ...prev,
      navigationHistory: [...prev.navigationHistory.slice(-9), route],
    }));
  };

  // Clear navigation history
  const clearHistory = () => {
    setNavigationState(prev => ({
      ...prev,
      navigationHistory: prev.currentRoute ? [prev.currentRoute] : [],
    }));
  };

  const contextValue: RouteManagerContextType = {
    navigationState,
    navigateTo,
    goBack,
    canGoBack,
    getRouteInfo,
    updateRouteTitle,
    addToHistory,
    clearHistory,
  };

  return (
    <RouteManagerContext.Provider value={contextValue}>
      {children}
    </RouteManagerContext.Provider>
  );
}

// Hook to use route manager
export function useRouteManager() {
  const context = useContext(RouteManagerContext);
  if (!context) {
    throw new Error('useRouteManager must be used within RouteManagerProvider');
  }
  return context;
}

// Hook for current route info
export function useCurrentRoute() {
  const { navigationState } = useRouteManager();
  return navigationState.currentRoute;
}

// Hook for navigation history
export function useNavigationHistory() {
  const { navigationState } = useRouteManager();
  return navigationState.navigationHistory;
}

// Route Guard Component
export function RouteGuard({ 
  children, 
  fallback,
  requiresAuth = true,
  allowedRoles = [],
}: {
  children: ReactNode;
  fallback?: ReactNode;
  requiresAuth?: boolean;
  allowedRoles?: string[];
}) {
  const { navigationState } = useRouteManager();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token && requiresAuth) {
          setLoading(false);
          return;
        }

        if (token) {
          // Verify token and get user info
          const response = await fetch('/api/auth/me', {
            headers: { 'Authorization': `Bearer ${token}` },
          });
          
          if (response.ok) {
            const userData = await response.json();
            setUser(userData.user);
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [requiresAuth]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
      </div>
    );
  }

  // Check authentication
  if (requiresAuth && !user) {
    return fallback || (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-4">Authentication Required</h2>
        <p className="text-gray-600 mb-6">Please sign in to access this page.</p>
        <button
          onClick={() => window.location.href = '/login'}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
        >
          Sign In
        </button>
      </div>
    );
  }

  // Check role permissions
  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
    return fallback || (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-4">Access Denied</h2>
        <p className="text-gray-600">You don't have permission to access this page.</p>
      </div>
    );
  }

  return <>{children}</>;
}

// Page Title Component
export function PageTitle({ 
  title, 
  description,
  showBreadcrumbs = true,
  actions 
}: {
  title?: string;
  description?: string;
  showBreadcrumbs?: boolean;
  actions?: ReactNode;
}) {
  const { navigationState } = useRouteManager();
  const currentRoute = navigationState.currentRoute;

  const pageTitle = title || currentRoute?.title || 'Page';
  const pageDescription = description || currentRoute?.description;

  useEffect(() => {
    // Update document title
    document.title = `${pageTitle} - MobilityHub`;
  }, [pageTitle]);

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-6 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {showBreadcrumbs && currentRoute?.breadcrumbs && (
          <nav className="flex mb-4" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              {currentRoute.breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <svg className="h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                  {crumb.href ? (
                    <a href={crumb.href} className="text-sm text-gray-500 hover:text-gray-700">
                      {crumb.label}
                    </a>
                  ) : (
                    <span className="text-sm text-gray-900 font-medium">{crumb.label}</span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}
        
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{pageTitle}</h1>
            {pageDescription && (
              <p className="mt-1 text-sm text-gray-500">{pageDescription}</p>
            )}
          </div>
          {actions && <div className="flex items-center space-x-3">{actions}</div>}
        </div>
      </div>
    </div>
  );
}
