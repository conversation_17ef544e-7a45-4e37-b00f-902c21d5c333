import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { businessIntelligenceService } from '@/lib/analytics/businessIntelligence';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has analytics access (admin, manager, or analyst roles)
    const allowedRoles = ['admin', 'manager', 'analyst'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions for analytics access' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { timeRange } = body;

    // Validate time range
    if (!timeRange || !timeRange.start || !timeRange.end) {
      return NextResponse.json(
        { error: 'Valid time range with start and end dates is required' },
        { status: 400 }
      );
    }

    // Parse and validate dates
    const startDate = new Date(timeRange.start);
    const endDate = new Date(timeRange.end);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format in time range' },
        { status: 400 }
      );
    }

    if (startDate >= endDate) {
      return NextResponse.json(
        { error: 'Start date must be before end date' },
        { status: 400 }
      );
    }

    // Limit time range to prevent excessive data processing
    const maxDays = 365; // 1 year
    const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysDiff > maxDays) {
      return NextResponse.json(
        { error: `Time range cannot exceed ${maxDays} days` },
        { status: 400 }
      );
    }

    // Prepare analytics time range
    const analyticsTimeRange = {
      start: startDate,
      end: endDate,
      granularity: timeRange.granularity || 'day' as 'hour' | 'day' | 'week' | 'month',
    };

    // Get business metrics
    const businessMetrics = await businessIntelligenceService.getBusinessMetrics(analyticsTimeRange);

    // Add metadata
    const response = {
      success: true,
      data: businessMetrics,
      metadata: {
        timeRange: analyticsTimeRange,
        generatedAt: new Date(),
        userRole: user.role,
        dataPoints: {
          revenue: businessMetrics.revenue.trends.length,
          rides: businessMetrics.rides.trends.length,
          users: businessMetrics.users.segments.length,
          drivers: businessMetrics.drivers.performance.length,
        },
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Business metrics API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch business metrics',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const allowedRoles = ['admin', 'manager', 'analyst'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions for analytics access' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'week';
    const granularity = searchParams.get('granularity') || 'day';

    // Calculate time range based on period
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    const analyticsTimeRange = {
      start: startDate,
      end: endDate,
      granularity: granularity as 'hour' | 'day' | 'week' | 'month',
    };

    // Get business metrics
    const businessMetrics = await businessIntelligenceService.getBusinessMetrics(analyticsTimeRange);

    // Generate summary insights
    const insights = generateBusinessInsights(businessMetrics);

    const response = {
      success: true,
      data: {
        metrics: businessMetrics,
        insights,
        summary: {
          totalRevenue: businessMetrics.revenue.total,
          totalRides: businessMetrics.rides.total,
          activeUsers: businessMetrics.users.active,
          onlineDrivers: businessMetrics.drivers.online,
          completionRate: businessMetrics.rides.completionRate,
          profitMargin: businessMetrics.financial.profitMargin,
        },
      },
      metadata: {
        period,
        granularity,
        timeRange: analyticsTimeRange,
        generatedAt: new Date(),
        userRole: user.role,
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Business metrics summary API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch business metrics summary',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

function generateBusinessInsights(metrics: any): string[] {
  const insights: string[] = [];

  // Revenue insights
  if (metrics.revenue.growth > 10) {
    insights.push(`Strong revenue growth of ${metrics.revenue.growth.toFixed(1)}% indicates healthy business expansion`);
  } else if (metrics.revenue.growth < 0) {
    insights.push(`Revenue decline of ${Math.abs(metrics.revenue.growth).toFixed(1)}% requires immediate attention`);
  }

  // Ride completion insights
  if (metrics.rides.completionRate > 90) {
    insights.push('Excellent ride completion rate indicates high service reliability');
  } else if (metrics.rides.completionRate < 80) {
    insights.push('Low ride completion rate may indicate driver supply or quality issues');
  }

  // Driver utilization insights
  if (metrics.drivers.utilization > 80) {
    insights.push('High driver utilization suggests strong demand but may indicate need for more drivers');
  } else if (metrics.drivers.utilization < 50) {
    insights.push('Low driver utilization indicates oversupply or weak demand');
  }

  // Financial health insights
  if (metrics.financial.profitMargin > 15) {
    insights.push('Healthy profit margins indicate strong operational efficiency');
  } else if (metrics.financial.profitMargin < 5) {
    insights.push('Low profit margins suggest need for cost optimization or pricing adjustments');
  }

  // User growth insights
  const userGrowthRate = (metrics.users.new / metrics.users.total) * 100;
  if (userGrowthRate > 5) {
    insights.push('Strong user acquisition rate indicates effective marketing and product-market fit');
  } else if (userGrowthRate < 1) {
    insights.push('Low user acquisition rate may require enhanced marketing efforts');
  }

  return insights;
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
