import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { taskId, driverId, rating = 4.8, feedback = '' } = await request.json()

    if (!taskId || !driverId) {
      return NextResponse.json(
        { success: false, error: 'Missing taskId or driverId' },
        { status: 400 }
      )
    }

    // Calculate earnings (mock calculation)
    const distance = 12 // Get from task data
    const baseEarnings = distance * 3 // ₹3 per km for driver
    const finalEarnings = Math.round(baseEarnings * (rating >= 4.5 ? 1.1 : 1.0)) // 10% bonus for high ratings

    // Trigger ride completion workflow
    const workflowResponse = await fetch('http://localhost:5678/webhook/ride-completed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        rideData: {
          rideId: taskId,
          userId: 'user123', // Get from task data
          driverId,
          distance,
          fare: distance * 5, // Total fare
          driverEarnings: finalEarnings,
          rating,
          feedback,
          completedAt: new Date().toISOString()
        }
      })
    })

    if (!workflowResponse.ok) {
      throw new Error('Failed to trigger completion workflow')
    }

    const workflowResult = await workflowResponse.json()

    // Update driver availability
    await fetch('http://localhost:8080/tools/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tool: 'notify_driver',
        parameters: {
          driverId,
          message: `Ride completed! You earned ₹${finalEarnings}. Great job!`,
          taskId,
          priority: 'low'
        }
      })
    })

    return NextResponse.json({
      success: true,
      message: 'Ride completed successfully',
      taskId,
      earnings: finalEarnings,
      pointsAwarded: workflowResult.driverPointsAwarded || Math.round(finalEarnings * 0.1),
      ratingBonus: rating >= 4.5 ? 5 : 0
    })
  } catch (error) {
    console.error('Task completion error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to complete task' },
      { status: 500 }
    )
  }
}
