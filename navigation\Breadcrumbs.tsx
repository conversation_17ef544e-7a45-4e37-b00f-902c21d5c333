// @ts-nocheck
'use client';

import { Fragment } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { 
  ChevronRight, 
  Home, 
  Car, 
  Bike, 
  Package, 
  Building2,
  User,
  Settings,
  CreditCard,
  BarChart3,
  Calendar,
  MapPin,
  Clock,
  Star,
  Shield,
  HelpCircle
} from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: any;
  current?: boolean;
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  maxItems?: number;
}

// Route configuration for automatic breadcrumb generation
const routeConfig: Record<string, { label: string; icon?: any }> = {
  '/': { label: 'Home', icon: Home },
  '/dashboard': { label: 'Dashboard', icon: Home },
  '/rides': { label: 'Rides', icon: Car },
  '/rides/book': { label: 'Book Ride', icon: MapPin },
  '/rides/history': { label: 'Ride History', icon: Clock },
  '/rides/scheduled': { label: 'Scheduled Rides', icon: Calendar },
  '/rides/favorites': { label: 'Favorite Routes', icon: Star },
  '/vehicles': { label: 'Vehicles', icon: Bike },
  '/vehicles/nearby': { label: 'Nearby Vehicles', icon: MapPin },
  '/vehicles/rentals': { label: 'My Rentals', icon: Clock },
  '/vehicles/reservations': { label: 'Reservations', icon: Calendar },
  '/delivery': { label: 'Delivery', icon: Package },
  '/delivery/food': { label: 'Food Delivery', icon: Package },
  '/delivery/packages': { label: 'Package Delivery', icon: Package },
  '/delivery/track': { label: 'Track Orders', icon: MapPin },
  '/delivery/history': { label: 'Order History', icon: Clock },
  '/corporate': { label: 'Corporate', icon: Building2 },
  '/corporate/dashboard': { label: 'Corporate Dashboard', icon: BarChart3 },
  '/corporate/book': { label: 'Book Business Travel', icon: Car },
  '/corporate/analytics': { label: 'Analytics', icon: BarChart3 },
  '/corporate/team': { label: 'Team Management', icon: User },
  '/profile': { label: 'Profile', icon: User },
  '/settings': { label: 'Settings', icon: Settings },
  '/billing': { label: 'Billing', icon: CreditCard },
  '/help': { label: 'Help & Support', icon: HelpCircle },
  '/safety': { label: 'Safety', icon: Shield },
};

export default function Breadcrumbs({ 
  items, 
  className, 
  showHome = true, 
  maxItems = 5 
}: BreadcrumbsProps) {
  const pathname = usePathname();

  // Generate breadcrumbs from current path if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (items) return items;

    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Add home if requested
    if (showHome && pathname !== '/') {
      breadcrumbs.push({
        label: 'Home',
        href: '/',
        icon: Home,
      });
    }

    // Build breadcrumbs from path segments
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const config = routeConfig[currentPath];
      
      if (config) {
        breadcrumbs.push({
          label: config.label,
          href: index === pathSegments.length - 1 ? undefined : currentPath,
          icon: config.icon,
          current: index === pathSegments.length - 1,
        });
      } else {
        // Fallback for dynamic routes
        const label = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        breadcrumbs.push({
          label,
          href: index === pathSegments.length - 1 ? undefined : currentPath,
          current: index === pathSegments.length - 1,
        });
      }
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Truncate breadcrumbs if they exceed maxItems
  const displayBreadcrumbs = breadcrumbs.length > maxItems 
    ? [
        breadcrumbs[0],
        { label: '...', href: undefined },
        ...breadcrumbs.slice(-2)
      ]
    : breadcrumbs;

  if (breadcrumbs.length <= 1) {
    return null; // Don't show breadcrumbs for single-level pages
  }

  return (
    <nav className={cn('flex items-center space-x-1 text-sm', className)} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {displayBreadcrumbs.map((item, index) => {
          const Icon = item.icon;
          const isLast = index === displayBreadcrumbs.length - 1;
          const isEllipsis = item.label === '...';

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-400 mx-1" />
              )}
              
              {isEllipsis ? (
                <span className="text-gray-500 px-2">...</span>
              ) : item.href ? (
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-1 px-2 py-1 rounded-md transition-colors',
                    'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  )}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{item.label}</span>
                </Link>
              ) : (
                <span
                  className={cn(
                    'flex items-center space-x-1 px-2 py-1 rounded-md',
                    isLast 
                      ? 'text-gray-900 font-medium bg-gray-100' 
                      : 'text-gray-600'
                  )}
                  aria-current={isLast ? 'page' : undefined}
                >
                  {Icon && <Icon className="h-4 w-4" />}
                  <span>{item.label}</span>
                </span>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// Specialized breadcrumb components for different sections
export function RideBreadcrumbs({ currentPage }: { currentPage?: string }) {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Rides', href: '/rides', icon: Car },
  ];

  if (currentPage) {
    items.push({ label: currentPage, current: true });
  }

  return <Breadcrumbs items={items} />;
}

export function VehicleBreadcrumbs({ currentPage }: { currentPage?: string }) {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Vehicles', href: '/vehicles', icon: Bike },
  ];

  if (currentPage) {
    items.push({ label: currentPage, current: true });
  }

  return <Breadcrumbs items={items} />;
}

export function DeliveryBreadcrumbs({ currentPage }: { currentPage?: string }) {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Delivery', href: '/delivery', icon: Package },
  ];

  if (currentPage) {
    items.push({ label: currentPage, current: true });
  }

  return <Breadcrumbs items={items} />;
}

export function CorporateBreadcrumbs({ currentPage }: { currentPage?: string }) {
  const items: BreadcrumbItem[] = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'Corporate', href: '/corporate', icon: Building2 },
  ];

  if (currentPage) {
    items.push({ label: currentPage, current: true });
  }

  return <Breadcrumbs items={items} />;
}

// Page Header with Breadcrumbs
export function PageHeader({ 
  title, 
  description, 
  breadcrumbs, 
  actions,
  className 
}: {
  title: string;
  description?: string;
  breadcrumbs?: BreadcrumbItem[];
  actions?: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('bg-white border-b border-gray-200 px-4 py-6 sm:px-6 lg:px-8', className)}>
      <div className="max-w-7xl mx-auto">
        {/* Breadcrumbs */}
        <div className="mb-4">
          <Breadcrumbs items={breadcrumbs} />
        </div>
        
        {/* Header Content */}
        <div className="flex items-center justify-between">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl">
              {title}
            </h1>
            {description && (
              <p className="mt-1 text-sm text-gray-500">{description}</p>
            )}
          </div>
          
          {actions && (
            <div className="flex items-center space-x-3">
              {actions}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Breadcrumb with Actions
export function BreadcrumbsWithActions({ 
  breadcrumbs, 
  actions, 
  className 
}: {
  breadcrumbs?: BreadcrumbItem[];
  actions?: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn('flex items-center justify-between py-3 px-4 sm:px-6 lg:px-8', className)}>
      <Breadcrumbs items={breadcrumbs} />
      {actions && (
        <div className="flex items-center space-x-2">
          {actions}
        </div>
      )}
    </div>
  );
}

// Mobile-friendly breadcrumbs
export function MobileBreadcrumbs({ 
  items, 
  className 
}: {
  items?: BreadcrumbItem[];
  className?: string;
}) {
  const breadcrumbs = items || [];
  
  if (breadcrumbs.length <= 1) return null;

  // On mobile, show only the last two items
  const mobileBreadcrumbs = breadcrumbs.length > 2 
    ? breadcrumbs.slice(-2) 
    : breadcrumbs;

  return (
    <nav className={cn('flex items-center space-x-1 text-sm sm:hidden', className)}>
      {mobileBreadcrumbs.map((item, index) => {
        const Icon = item.icon;
        const isLast = index === mobileBreadcrumbs.length - 1;

        return (
          <Fragment key={index}>
            {index > 0 && (
              <ChevronRight className="h-4 w-4 text-gray-400" />
            )}
            
            {item.href && !isLast ? (
              <Link
                href={item.href}
                className="flex items-center space-x-1 text-gray-600 hover:text-blue-600"
              >
                {Icon && <Icon className="h-4 w-4" />}
                <span className="truncate max-w-24">{item.label}</span>
              </Link>
            ) : (
              <span className="flex items-center space-x-1 text-gray-900 font-medium">
                {Icon && <Icon className="h-4 w-4" />}
                <span className="truncate max-w-32">{item.label}</span>
              </span>
            )}
          </Fragment>
        );
      })}
    </nav>
  );
}
