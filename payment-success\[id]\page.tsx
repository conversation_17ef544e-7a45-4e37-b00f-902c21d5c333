"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, MapPin, Clock, Star, Home } from "lucide-react"

export default function PaymentSuccessPage({ params }: { params: { id: string } }) {
  const [countdown, setCountdown] = useState(5)

  // Sample ride details (would come from API in real app)
  const ride = {
    id: params.id,
    route: {
      start: "Andheri East",
      end: "Bandra Kurla Complex",
      distance: 12,
      departureTime: "08:00 AM",
      arrivalTime: "08:30 AM",
    },
    price: 60,
    serviceFee: 5,
    total: 65,
    driver: {
      name: "<PERSON><PERSON>",
      rating: 4.8,
      phone: "+91 98765 43210",
    },
    paymentMethod: "Wallet",
    transactionId: "TXN" + Math.floor(Math.random() * 1000000),
    cashbackEarned: 3,
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto text-center">
        <div className="flex justify-center mb-6">
          <div className="h-20 w-20 rounded-full bg-green-100 flex items-center justify-center">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
        </div>

        <h1 className="text-3xl font-bold mb-2">Payment Successful!</h1>
        <p className="text-muted-foreground mb-8">
          Your ride has been booked successfully. The driver will pick you up shortly.
        </p>

        <Card>
          <CardHeader>
            <CardTitle>Ride Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">From</p>
                <p className="font-medium">{ride.route.start}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">To</p>
                <p className="font-medium">{ride.route.end}</p>
              </div>
            </div>

            <div className="flex justify-between text-sm">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1 text-muted-foreground" />
                <span>{ride.route.distance} km</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                <span>
                  {ride.route.departureTime} - {ride.route.arrivalTime}
                </span>
              </div>
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between mb-2">
                <span className="text-muted-foreground">Driver</span>
                <span className="font-medium">{ride.driver.name}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-muted-foreground">Driver Rating</span>
                <span className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                  {ride.driver.rating}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Contact</span>
                <span>{ride.driver.phone}</span>
              </div>
            </div>

            <div className="border-t pt-4">
              <div className="flex justify-between mb-2">
                <span className="text-muted-foreground">Payment Method</span>
                <span>{ride.paymentMethod}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-muted-foreground">Amount Paid</span>
                <span>₹{ride.total}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="text-muted-foreground">Transaction ID</span>
                <span className="text-xs">{ride.transactionId}</span>
              </div>
              <div className="flex justify-between text-green-600">
                <span>Cashback Earned</span>
                <span>₹{ride.cashbackEarned}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4">
            <p className="text-sm text-center text-muted-foreground">
              Redirecting to dashboard in {countdown} seconds...
            </p>
            <div className="flex gap-4 w-full">
              <Button variant="outline" className="flex-1" asChild>
                <Link href="/dashboard">
                  <Home className="mr-2 h-4 w-4" />
                  Dashboard
                </Link>
              </Button>
              <Button className="flex-1" asChild>
                <Link href={`/dashboard`}>Track Ride</Link>
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}

