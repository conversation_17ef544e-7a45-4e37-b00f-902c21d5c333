'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useWebSocket } from '@/lib/hooks/useWebSocket';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  MessageCircle, 
  Send, 
  MapPin, 
  Clock,
  Phone,
  AlertTriangle,
  Wifi,
  WifiOff
} from 'lucide-react';

interface RideChatProps {
  rideId: string;
  otherUser: {
    _id: string;
    firstName: string;
    lastName: string;
    role: 'rider' | 'driver';
    profileImage?: string;
    phone?: string;
  };
  onEmergencyAlert?: () => void;
}

interface ChatMessage {
  rideId: string;
  senderId: string;
  senderRole: 'rider' | 'driver';
  message: string;
  timestamp: number;
  messageType: 'text' | 'location' | 'system';
}

export default function RideChat({ rideId, otherUser, onEmergencyAlert }: RideChatProps) {
  const { user } = useAuth();
  const { 
    sendChatMessage, 
    getChatMessages, 
    joinRide, 
    leaveRide, 
    isConnected 
  } = useWebSocket();
  
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Join ride room and get existing messages
  useEffect(() => {
    if (rideId) {
      joinRide(rideId);
      setMessages(getChatMessages(rideId));
    }

    return () => {
      if (rideId) {
        leaveRide(rideId);
      }
    };
  }, [rideId]);

  // Update messages when new ones arrive
  useEffect(() => {
    const currentMessages = getChatMessages(rideId);
    setMessages(currentMessages);
  }, [getChatMessages(rideId)]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim() || !isConnected) return;

    sendChatMessage(rideId, message.trim());
    setMessage('');
    inputRef.current?.focus();
  };

  const handleShareLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const locationMessage = `📍 Current location: ${position.coords.latitude.toFixed(6)}, ${position.coords.longitude.toFixed(6)}`;
          sendChatMessage(rideId, locationMessage, 'location');
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  const formatMessageTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const isMyMessage = (senderId: string) => {
    return senderId === user?._id;
  };

  const MessageBubble = ({ msg }: { msg: ChatMessage }) => {
    const isMine = isMyMessage(msg.senderId);
    
    return (
      <div className={`flex ${isMine ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`flex ${isMine ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}>
          {!isMine && (
            <Avatar className="w-6 h-6">
              <AvatarImage src={otherUser.profileImage} />
              <AvatarFallback className="text-xs">
                {getInitials(otherUser.firstName, otherUser.lastName)}
              </AvatarFallback>
            </Avatar>
          )}
          
          <div className={`px-3 py-2 rounded-lg ${
            isMine 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 text-gray-900'
          }`}>
            {msg.messageType === 'location' ? (
              <div className="flex items-center space-x-1">
                <MapPin className="w-4 h-4" />
                <span className="text-sm">{msg.message}</span>
              </div>
            ) : msg.messageType === 'system' ? (
              <div className="flex items-center space-x-1 text-xs italic">
                <span>{msg.message}</span>
              </div>
            ) : (
              <p className="text-sm">{msg.message}</p>
            )}
            
            <div className={`text-xs mt-1 ${
              isMine ? 'text-blue-100' : 'text-gray-500'
            }`}>
              {formatMessageTime(msg.timestamp)}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card className="h-96 flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="w-8 h-8">
              <AvatarImage src={otherUser.profileImage} />
              <AvatarFallback>
                {getInitials(otherUser.firstName, otherUser.lastName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">
                {otherUser.firstName} {otherUser.lastName}
              </CardTitle>
              <CardDescription className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {otherUser.role === 'driver' ? 'Driver' : 'Rider'}
                </Badge>
                <Badge variant="outline" className={isConnected ? 'text-green-600' : 'text-red-600'}>
                  {isConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
                </Badge>
              </CardDescription>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Phone className="w-4 h-4" />
            </Button>
            {onEmergencyAlert && (
              <Button variant="outline" size="sm" onClick={onEmergencyAlert}>
                <AlertTriangle className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages Area */}
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-1">
            {messages.length === 0 ? (
              <div className="text-center py-8">
                <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-sm">No messages yet</p>
                <p className="text-gray-400 text-xs">Start a conversation with your {otherUser.role}</p>
              </div>
            ) : (
              messages.map((msg, index) => (
                <MessageBubble key={`${msg.timestamp}-${index}`} msg={msg} />
              ))
            )}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="border-t p-4">
          <form onSubmit={handleSendMessage} className="flex space-x-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleShareLocation}
              disabled={!isConnected}
            >
              <MapPin className="w-4 h-4" />
            </Button>
            
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={isConnected ? "Type a message..." : "Connecting..."}
              disabled={!isConnected}
              className="flex-1"
            />
            
            <Button 
              type="submit" 
              size="sm"
              disabled={!message.trim() || !isConnected}
            >
              <Send className="w-4 h-4" />
            </Button>
          </form>
          
          {!isConnected && (
            <p className="text-xs text-red-500 mt-1">
              Connection lost. Trying to reconnect...
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
