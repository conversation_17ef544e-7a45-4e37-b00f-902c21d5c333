import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';

interface HealthCheck {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: {
      status: 'connected' | 'disconnected' | 'error';
      responseTime?: number;
      error?: string;
    };
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
  };
  checks: {
    name: string;
    status: 'pass' | 'fail';
    responseTime?: number;
    error?: string;
  }[];
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const healthCheck: HealthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: 'disconnected',
        },
        memory: {
          used: 0,
          total: 0,
          percentage: 0,
        },
        cpu: {
          usage: 0,
        },
      },
      checks: [],
    };

    // Check database connection
    try {
      const dbStartTime = Date.now();
      await connectDB();
      const dbResponseTime = Date.now() - dbStartTime;
      
      healthCheck.services.database = {
        status: 'connected',
        responseTime: dbResponseTime,
      };
      
      healthCheck.checks.push({
        name: 'database',
        status: 'pass',
        responseTime: dbResponseTime,
      });
    } catch (error) {
      healthCheck.services.database = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown database error',
      };
      
      healthCheck.checks.push({
        name: 'database',
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown database error',
      });
      
      healthCheck.status = 'unhealthy';
    }

    // Check memory usage
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const memoryPercentage = (usedMemory / totalMemory) * 100;

      healthCheck.services.memory = {
        used: usedMemory,
        total: totalMemory,
        percentage: Math.round(memoryPercentage * 100) / 100,
      };

      healthCheck.checks.push({
        name: 'memory',
        status: memoryPercentage < 90 ? 'pass' : 'fail',
        responseTime: Date.now() - startTime,
      });

      if (memoryPercentage >= 90) {
        healthCheck.status = 'unhealthy';
      }
    } catch (error) {
      healthCheck.checks.push({
        name: 'memory',
        status: 'fail',
        error: error instanceof Error ? error.message : 'Memory check failed',
      });
    }

    // Check CPU usage (simplified)
    try {
      const cpuUsage = process.cpuUsage();
      const cpuPercentage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
      
      healthCheck.services.cpu = {
        usage: Math.round(cpuPercentage * 100) / 100,
      };

      healthCheck.checks.push({
        name: 'cpu',
        status: 'pass',
        responseTime: Date.now() - startTime,
      });
    } catch (error) {
      healthCheck.checks.push({
        name: 'cpu',
        status: 'fail',
        error: error instanceof Error ? error.message : 'CPU check failed',
      });
    }

    // Check external services (optional)
    if (process.env.NODE_ENV === 'production') {
      // Check Redis connection
      try {
        // Add Redis health check here if using Redis
        healthCheck.checks.push({
          name: 'redis',
          status: 'pass',
          responseTime: 5, // Mock response time
        });
      } catch (error) {
        healthCheck.checks.push({
          name: 'redis',
          status: 'fail',
          error: error instanceof Error ? error.message : 'Redis connection failed',
        });
        healthCheck.status = 'unhealthy';
      }

      // Check external APIs
      try {
        // Add external API health checks here
        healthCheck.checks.push({
          name: 'external_apis',
          status: 'pass',
          responseTime: 10, // Mock response time
        });
      } catch (error) {
        healthCheck.checks.push({
          name: 'external_apis',
          status: 'fail',
          error: error instanceof Error ? error.message : 'External API check failed',
        });
      }
    }

    // Determine overall status
    const failedChecks = healthCheck.checks.filter(check => check.status === 'fail');
    if (failedChecks.length > 0) {
      healthCheck.status = 'unhealthy';
    }

    // Return appropriate HTTP status
    const httpStatus = healthCheck.status === 'healthy' ? 200 : 503;

    return NextResponse.json(healthCheck, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Health check error:', error);
    
    const errorResponse: HealthCheck = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: 'error',
          error: 'Health check failed',
        },
        memory: {
          used: 0,
          total: 0,
          percentage: 0,
        },
        cpu: {
          usage: 0,
        },
      },
      checks: [
        {
          name: 'health_check',
          status: 'fail',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      ],
    };

    return NextResponse.json(errorResponse, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
}

// Simple health check for load balancers
export async function HEAD(request: NextRequest) {
  try {
    // Quick database ping
    await connectDB();
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
