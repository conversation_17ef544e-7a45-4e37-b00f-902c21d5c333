// Unified Payment System for Multi-Modal Transport
import { apmService } from '../monitoring/apm';
import { RazorpayService } from '../services/razorpayService';

export interface TransportPayment {
  id: string;
  userId: string;
  journeyId: string;
  segments: PaymentSegment[];
  totalAmount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'wallet' | 'card' | 'upi' | 'transit_card' | 'subscription';
  createdAt: Date;
  completedAt?: Date;
  metadata: {
    journeyType: 'single_mode' | 'multi_modal';
    primaryMode: string;
    carbonOffset?: number;
    discountsApplied: string[];
  };
}

export interface PaymentSegment {
  segmentId: string;
  transportMode: string;
  operator: string;
  amount: number;
  fareType: 'base' | 'peak' | 'off_peak' | 'subscription' | 'promotional';
  taxes: number;
  discounts: number;
  carbonOffsetFee?: number;
  description: string;
}

export interface TransportWallet {
  userId: string;
  balance: number;
  currency: string;
  transitCredits: number; // Credits for public transport
  carbonCredits: number; // Environmental credits
  subscriptions: TransportSubscription[];
  paymentMethods: PaymentMethod[];
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    paymentMethodId: string;
  };
  spendingLimits: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

export interface TransportSubscription {
  id: string;
  type: 'unlimited_rides' | 'monthly_pass' | 'zone_pass' | 'corporate';
  operator: string;
  validFrom: Date;
  validUntil: Date;
  remainingRides?: number;
  zones?: string[];
  amount: number;
  autoRenew: boolean;
  status: 'active' | 'expired' | 'cancelled' | 'suspended';
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'upi' | 'bank_account' | 'transit_card';
  provider: string;
  lastFour?: string;
  expiryDate?: string;
  isDefault: boolean;
  isVerified: boolean;
  metadata: Record<string, any>;
}

export interface FareCalculation {
  segments: PaymentSegment[];
  subtotal: number;
  taxes: number;
  discounts: number;
  carbonOffset: number;
  total: number;
  savings: {
    multiModalDiscount: number;
    subscriptionSavings: number;
    loyaltyDiscount: number;
    carbonIncentive: number;
  };
  breakdown: {
    description: string;
    amount: number;
  }[];
}

export interface PaymentRequest {
  userId: string;
  journeyId: string;
  segments: Array<{
    segmentId: string;
    transportMode: string;
    operator: string;
    baseAmount: number;
    fareType: string;
  }>;
  paymentMethod: string;
  applySubscriptions: boolean;
  addCarbonOffset: boolean;
}

class UnifiedPaymentService {
  private wallets: Map<string, TransportWallet> = new Map();
  private subscriptions: Map<string, TransportSubscription> = new Map();
  private fareRules: Map<string, any> = new Map();

  constructor() {
    this.initializeFareRules();
  }

  /**
   * Calculate fare for multi-modal journey
   */
  async calculateFare(
    userId: string,
    segments: Array<{
      segmentId: string;
      transportMode: string;
      operator: string;
      distance: number;
      duration: number;
      timeOfDay: Date;
    }>
  ): Promise<FareCalculation> {
    const timer = apmService.startTimer('fare_calculation');
    
    try {
      const wallet = await this.getOrCreateWallet(userId);
      const paymentSegments: PaymentSegment[] = [];
      let subtotal = 0;
      let totalTaxes = 0;
      let totalDiscounts = 0;

      // Calculate fare for each segment
      for (const segment of segments) {
        const segmentFare = await this.calculateSegmentFare(segment, wallet);
        paymentSegments.push(segmentFare);
        subtotal += segmentFare.amount;
        totalTaxes += segmentFare.taxes;
        totalDiscounts += segmentFare.discounts;
      }

      // Apply multi-modal discounts
      const multiModalDiscount = this.calculateMultiModalDiscount(paymentSegments);
      totalDiscounts += multiModalDiscount;

      // Apply subscription savings
      const subscriptionSavings = this.calculateSubscriptionSavings(paymentSegments, wallet);
      totalDiscounts += subscriptionSavings;

      // Apply loyalty discounts
      const loyaltyDiscount = this.calculateLoyaltyDiscount(userId, subtotal);
      totalDiscounts += loyaltyDiscount;

      // Calculate carbon offset
      const carbonOffset = this.calculateCarbonOffset(segments);

      // Apply carbon incentive (discount for choosing eco-friendly options)
      const carbonIncentive = this.calculateCarbonIncentive(segments);
      totalDiscounts += carbonIncentive;

      const total = Math.max(0, subtotal + totalTaxes - totalDiscounts + carbonOffset);

      const fareCalculation: FareCalculation = {
        segments: paymentSegments,
        subtotal,
        taxes: totalTaxes,
        discounts: totalDiscounts,
        carbonOffset,
        total,
        savings: {
          multiModalDiscount,
          subscriptionSavings,
          loyaltyDiscount,
          carbonIncentive,
        },
        breakdown: this.generateFareBreakdown(paymentSegments, totalTaxes, totalDiscounts, carbonOffset),
      };

      timer.end(true);
      return fareCalculation;

    } catch (error) {
      timer.end(false);
      console.error('Fare calculation failed:', error);
      throw error;
    }
  }

  /**
   * Process payment for multi-modal journey
   */
  async processPayment(request: PaymentRequest): Promise<TransportPayment> {
    const timer = apmService.startTimer('transport_payment');
    
    try {
      // Calculate fare
      const fareCalculation = await this.calculateFare(
        request.userId,
        request.segments.map(s => ({
          segmentId: s.segmentId,
          transportMode: s.transportMode,
          operator: s.operator,
          distance: 5, // Would come from journey data
          duration: 15, // Would come from journey data
          timeOfDay: new Date(),
        }))
      );

      // Create payment record
      const payment: TransportPayment = {
        id: `tp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: request.userId,
        journeyId: request.journeyId,
        segments: fareCalculation.segments,
        totalAmount: fareCalculation.total,
        currency: 'INR',
        status: 'pending',
        paymentMethod: request.paymentMethod,
        createdAt: new Date(),
        metadata: {
          journeyType: fareCalculation.segments.length > 1 ? 'multi_modal' : 'single_mode',
          primaryMode: fareCalculation.segments[0]?.transportMode || 'unknown',
          carbonOffset: request.addCarbonOffset ? fareCalculation.carbonOffset : undefined,
          discountsApplied: this.getAppliedDiscounts(fareCalculation),
        },
      };

      // Process payment based on method
      await this.executePayment(payment, request.paymentMethod);

      // Update wallet and subscriptions
      await this.updateWalletAfterPayment(request.userId, payment);

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'transport_payment_processed',
        value: payment.totalAmount,
        timestamp: Date.now(),
        properties: {
          journeyType: payment.metadata.journeyType,
          paymentMethod: payment.paymentMethod,
          segments: payment.segments.length,
        },
      });

      timer.end(true);
      return payment;

    } catch (error) {
      timer.end(false);
      console.error('Transport payment processing failed:', error);
      throw error;
    }
  }

  /**
   * Get or create transport wallet for user
   */
  async getOrCreateWallet(userId: string): Promise<TransportWallet> {
    let wallet = this.wallets.get(userId);
    
    if (!wallet) {
      wallet = {
        userId,
        balance: 0,
        currency: 'INR',
        transitCredits: 0,
        carbonCredits: 0,
        subscriptions: [],
        paymentMethods: [],
        autoRecharge: {
          enabled: false,
          threshold: 100,
          amount: 500,
          paymentMethodId: '',
        },
        spendingLimits: {
          daily: 1000,
          weekly: 5000,
          monthly: 20000,
        },
      };
      
      this.wallets.set(userId, wallet);
    }
    
    return wallet;
  }

  /**
   * Add funds to transport wallet
   */
  async addFunds(
    userId: string,
    amount: number,
    paymentMethodId: string,
    source: 'manual' | 'auto_recharge' | 'refund' | 'cashback'
  ): Promise<void> {
    const wallet = await this.getOrCreateWallet(userId);
    
    // Process payment through Razorpay
    if (source === 'manual' || source === 'auto_recharge') {
      const paymentResult = await RazorpayService.processWalletTopup(
        userId,
        amount,
        paymentMethodId
      );
      
      if (!paymentResult.success) {
        throw new Error('Failed to process wallet top-up payment');
      }
    }
    
    // Add funds to wallet
    wallet.balance += amount;
    
    // Record transaction
    apmService.recordBusinessMetric({
      event: 'wallet_topup',
      value: amount,
      timestamp: Date.now(),
      properties: {
        source,
        userId,
      },
    });
  }

  /**
   * Purchase transport subscription
   */
  async purchaseSubscription(
    userId: string,
    subscriptionType: string,
    operator: string,
    duration: number, // in days
    paymentMethodId: string
  ): Promise<TransportSubscription> {
    const subscriptionPrice = this.getSubscriptionPrice(subscriptionType, operator, duration);
    
    // Process payment
    const paymentResult = await RazorpayService.processSubscriptionPayment(
      userId,
      subscriptionPrice,
      paymentMethodId,
      subscriptionType
    );
    
    if (!paymentResult.success) {
      throw new Error('Failed to process subscription payment');
    }
    
    // Create subscription
    const subscription: TransportSubscription = {
      id: `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: subscriptionType as any,
      operator,
      validFrom: new Date(),
      validUntil: new Date(Date.now() + duration * 24 * 60 * 60 * 1000),
      amount: subscriptionPrice,
      autoRenew: false,
      status: 'active',
    };
    
    // Add to user's wallet
    const wallet = await this.getOrCreateWallet(userId);
    wallet.subscriptions.push(subscription);
    
    // Cache subscription
    this.subscriptions.set(subscription.id, subscription);
    
    return subscription;
  }

  /**
   * Calculate segment fare
   */
  private async calculateSegmentFare(
    segment: any,
    wallet: TransportWallet
  ): Promise<PaymentSegment> {
    const fareRules = this.fareRules.get(segment.transportMode) || this.getDefaultFareRules();
    
    // Base fare calculation
    let baseAmount = fareRules.baseFare;
    
    // Distance-based fare
    if (fareRules.perKmRate) {
      baseAmount += segment.distance * fareRules.perKmRate;
    }
    
    // Time-based fare
    if (fareRules.perMinuteRate) {
      baseAmount += segment.duration * fareRules.perMinuteRate;
    }
    
    // Peak hour multiplier
    const isPeakHour = this.isPeakHour(segment.timeOfDay);
    if (isPeakHour && fareRules.peakMultiplier) {
      baseAmount *= fareRules.peakMultiplier;
    }
    
    // Calculate taxes
    const taxes = baseAmount * (fareRules.taxRate || 0.05);
    
    // Calculate discounts
    let discounts = 0;
    
    // Check for applicable subscriptions
    const applicableSubscription = wallet.subscriptions.find(sub => 
      sub.status === 'active' && 
      sub.operator === segment.operator &&
      sub.validUntil > new Date()
    );
    
    if (applicableSubscription) {
      discounts = baseAmount * 0.5; // 50% discount for subscription holders
    }
    
    return {
      segmentId: segment.segmentId,
      transportMode: segment.transportMode,
      operator: segment.operator,
      amount: Math.max(0, baseAmount + taxes - discounts),
      fareType: isPeakHour ? 'peak' : 'base',
      taxes,
      discounts,
      description: `${segment.transportMode} - ${segment.operator}`,
    };
  }

  /**
   * Execute payment based on method
   */
  private async executePayment(payment: TransportPayment, paymentMethod: string): Promise<void> {
    switch (paymentMethod) {
      case 'wallet':
        await this.processWalletPayment(payment);
        break;
      case 'card':
      case 'upi':
        await this.processExternalPayment(payment, paymentMethod);
        break;
      default:
        throw new Error(`Unsupported payment method: ${paymentMethod}`);
    }
  }

  /**
   * Process wallet payment
   */
  private async processWalletPayment(payment: TransportPayment): Promise<void> {
    const wallet = await this.getOrCreateWallet(payment.userId);
    
    if (wallet.balance < payment.totalAmount) {
      // Check if auto-recharge is enabled
      if (wallet.autoRecharge.enabled && wallet.balance < wallet.autoRecharge.threshold) {
        await this.addFunds(
          payment.userId,
          wallet.autoRecharge.amount,
          wallet.autoRecharge.paymentMethodId,
          'auto_recharge'
        );
      } else {
        throw new Error('Insufficient wallet balance');
      }
    }
    
    // Deduct amount from wallet
    wallet.balance -= payment.totalAmount;
    payment.status = 'completed';
    payment.completedAt = new Date();
  }

  /**
   * Process external payment
   */
  private async processExternalPayment(payment: TransportPayment, method: string): Promise<void> {
    // This would integrate with Razorpay for external payments
    const paymentResult = await RazorpayService.processTransportPayment(
      payment.userId,
      payment.totalAmount,
      method,
      payment.journeyId
    );
    
    if (paymentResult.success) {
      payment.status = 'completed';
      payment.completedAt = new Date();
    } else {
      payment.status = 'failed';
      throw new Error('External payment processing failed');
    }
  }

  /**
   * Utility methods
   */
  private initializeFareRules(): void {
    // Initialize fare rules for different transport modes
    this.fareRules.set('bus', {
      baseFare: 15,
      perKmRate: 2,
      peakMultiplier: 1.2,
      taxRate: 0.05,
    });
    
    this.fareRules.set('metro', {
      baseFare: 20,
      perKmRate: 3,
      peakMultiplier: 1.3,
      taxRate: 0.05,
    });
    
    this.fareRules.set('rideshare', {
      baseFare: 30,
      perKmRate: 10,
      perMinuteRate: 1.5,
      peakMultiplier: 1.5,
      taxRate: 0.05,
    });
  }

  private getDefaultFareRules() {
    return {
      baseFare: 20,
      perKmRate: 5,
      taxRate: 0.05,
    };
  }

  private isPeakHour(time: Date): boolean {
    const hour = time.getHours();
    return (hour >= 7 && hour <= 10) || (hour >= 17 && hour <= 20);
  }

  private calculateMultiModalDiscount(segments: PaymentSegment[]): number {
    if (segments.length > 1) {
      const totalAmount = segments.reduce((sum, segment) => sum + segment.amount, 0);
      return totalAmount * 0.1; // 10% discount for multi-modal journeys
    }
    return 0;
  }

  private calculateSubscriptionSavings(segments: PaymentSegment[], wallet: TransportWallet): number {
    // Calculate savings from active subscriptions
    return segments.reduce((savings, segment) => {
      const subscription = wallet.subscriptions.find(sub => 
        sub.status === 'active' && sub.operator === segment.operator
      );
      return subscription ? savings + (segment.amount * 0.3) : savings;
    }, 0);
  }

  private calculateLoyaltyDiscount(userId: string, amount: number): number {
    // Simple loyalty discount based on user tier
    // Would integrate with actual loyalty system
    return amount * 0.05; // 5% loyalty discount
  }

  private calculateCarbonOffset(segments: any[]): number {
    // Calculate carbon offset fee for eco-friendly transport choices
    const carbonFootprint = segments.reduce((total, segment) => {
      const emissionFactors = {
        bus: 0.05,
        metro: 0.03,
        rideshare: 0.12,
        bike: 0,
        walk: 0,
      };
      return total + (segment.distance * (emissionFactors[segment.transportMode] || 0.1));
    }, 0);
    
    return carbonFootprint * 10; // ₹10 per kg CO2
  }

  private calculateCarbonIncentive(segments: any[]): number {
    // Provide incentive for choosing eco-friendly transport
    const ecoFriendlyModes = ['bus', 'metro', 'bike', 'walk'];
    const ecoSegments = segments.filter(s => ecoFriendlyModes.includes(s.transportMode));
    
    if (ecoSegments.length === segments.length) {
      return segments.length * 5; // ₹5 per eco-friendly segment
    }
    
    return 0;
  }

  private generateFareBreakdown(
    segments: PaymentSegment[],
    taxes: number,
    discounts: number,
    carbonOffset: number
  ) {
    const breakdown = [
      ...segments.map(segment => ({
        description: segment.description,
        amount: segment.amount,
      })),
    ];
    
    if (taxes > 0) {
      breakdown.push({ description: 'Taxes & Fees', amount: taxes });
    }
    
    if (discounts > 0) {
      breakdown.push({ description: 'Discounts Applied', amount: -discounts });
    }
    
    if (carbonOffset > 0) {
      breakdown.push({ description: 'Carbon Offset', amount: carbonOffset });
    }
    
    return breakdown;
  }

  private getAppliedDiscounts(fareCalculation: FareCalculation): string[] {
    const discounts: string[] = [];
    
    if (fareCalculation.savings.multiModalDiscount > 0) {
      discounts.push('Multi-Modal Discount');
    }
    
    if (fareCalculation.savings.subscriptionSavings > 0) {
      discounts.push('Subscription Savings');
    }
    
    if (fareCalculation.savings.loyaltyDiscount > 0) {
      discounts.push('Loyalty Discount');
    }
    
    if (fareCalculation.savings.carbonIncentive > 0) {
      discounts.push('Eco-Friendly Incentive');
    }
    
    return discounts;
  }

  private async updateWalletAfterPayment(userId: string, payment: TransportPayment): Promise<void> {
    const wallet = await this.getOrCreateWallet(userId);
    
    // Add carbon credits for eco-friendly choices
    if (payment.metadata.carbonOffset) {
      wallet.carbonCredits += payment.metadata.carbonOffset;
    }
    
    // Add transit credits for public transport usage
    const publicTransportSegments = payment.segments.filter(s => 
      ['bus', 'metro', 'train'].includes(s.transportMode)
    );
    
    if (publicTransportSegments.length > 0) {
      wallet.transitCredits += publicTransportSegments.length * 2;
    }
  }

  private getSubscriptionPrice(type: string, operator: string, duration: number): number {
    const basePrices = {
      unlimited_rides: 500,
      monthly_pass: 300,
      zone_pass: 200,
      corporate: 1000,
    };
    
    const basePrice = basePrices[type] || 300;
    return basePrice * (duration / 30); // Price per month
  }
}

// Export singleton instance
export const unifiedPaymentService = new UnifiedPaymentService();
export default unifiedPaymentService;
