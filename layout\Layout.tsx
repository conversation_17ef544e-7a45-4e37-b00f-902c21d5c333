'use client';

import { ReactNode, useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import Header from './Header';
import Footer from './Footer';
import { LoadingSpinner } from '@/components/ui/loading';

interface LayoutProps {
  children: ReactNode;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
}

export default function Layout({ children }: LayoutProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();

  // Pages that should not show header/footer
  const noLayoutPages = ['/login', '/register', '/onboarding'];
  const shouldShowLayout = !noLayoutPages.includes(pathname);

  // Pages that should not show footer
  const noFooterPages = ['/dashboard', '/admin', '/driver', '/corporate'];
  const shouldShowFooter = shouldShowLayout && !noFooterPages.some(page => pathname.startsWith(page));

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setLoading(false);
        return;
      }

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData.user);
      } else {
        // Token is invalid, remove it
        localStorage.removeItem('token');
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <LoadingSpinner className="text-white" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading MobilityHub</h2>
          <p className="text-gray-600">Preparing your mobility experience...</p>
        </div>
      </div>
    );
  }

  if (!shouldShowLayout) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header user={user} />
      
      <main className="flex-1">
        {children}
      </main>
      
      {shouldShowFooter && <Footer />}
    </div>
  );
}
