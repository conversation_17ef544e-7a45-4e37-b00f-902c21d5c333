// AI-Powered Route Optimization Service
import { apmService } from '../monitoring/apm';

export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface RouteOptimizationRequest {
  pickup: Location;
  dropoff: Location;
  driverLocation?: Location;
  preferences?: {
    avoidTolls?: boolean;
    avoidHighways?: boolean;
    preferFastestRoute?: boolean;
    preferShortestRoute?: boolean;
  };
  trafficData?: {
    currentConditions: 'light' | 'moderate' | 'heavy' | 'severe';
    incidents?: Array<{
      type: 'accident' | 'construction' | 'closure';
      location: Location;
      severity: 'low' | 'medium' | 'high';
    }>;
  };
  timeOfDay?: Date;
  weatherConditions?: {
    condition: 'clear' | 'rain' | 'fog' | 'snow';
    visibility: number; // in km
    temperature: number; // in celsius
  };
}

export interface OptimizedRoute {
  routeId: string;
  totalDistance: number; // in km
  estimatedDuration: number; // in minutes
  estimatedCost: number; // in INR
  confidence: number; // 0-1 score
  waypoints: Location[];
  alternativeRoutes?: OptimizedRoute[];
  trafficImpact: {
    delayMinutes: number;
    congestionLevel: 'low' | 'medium' | 'high';
  };
  weatherImpact: {
    delayMinutes: number;
    riskLevel: 'low' | 'medium' | 'high';
  };
  optimizationFactors: {
    distanceWeight: number;
    timeWeight: number;
    costWeight: number;
    safetyWeight: number;
  };
}

export interface MLModelFeatures {
  // Temporal features
  hourOfDay: number;
  dayOfWeek: number;
  isWeekend: boolean;
  isHoliday: boolean;
  
  // Geographic features
  pickupLatitude: number;
  pickupLongitude: number;
  dropoffLatitude: number;
  dropoffLongitude: number;
  straightLineDistance: number;
  
  // Traffic features
  trafficDensity: number;
  averageSpeed: number;
  incidentCount: number;
  
  // Weather features
  temperature: number;
  precipitation: number;
  visibility: number;
  windSpeed: number;
  
  // Historical features
  historicalAverageDuration: number;
  historicalTrafficPattern: number;
  popularityScore: number;
}

class RouteOptimizationService {
  private modelEndpoint: string;
  private apiKey: string;
  private cache: Map<string, OptimizedRoute> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.modelEndpoint = process.env.ML_MODEL_ENDPOINT || 'http://localhost:8000';
    this.apiKey = process.env.ML_API_KEY || '';
  }

  /**
   * Optimize route using AI/ML algorithms
   */
  async optimizeRoute(request: RouteOptimizationRequest): Promise<OptimizedRoute> {
    const timer = apmService.startTimer('route_optimization');
    
    try {
      // Generate cache key
      const cacheKey = this.generateCacheKey(request);
      
      // Check cache first
      const cachedRoute = this.getCachedRoute(cacheKey);
      if (cachedRoute) {
        timer.end(true);
        return cachedRoute;
      }

      // Extract features for ML model
      const features = await this.extractFeatures(request);
      
      // Get multiple route options
      const routeOptions = await this.generateRouteOptions(request);
      
      // Score and rank routes using ML
      const scoredRoutes = await this.scoreRoutes(routeOptions, features);
      
      // Select optimal route
      const optimizedRoute = this.selectOptimalRoute(scoredRoutes, request);
      
      // Apply real-time adjustments
      const finalRoute = await this.applyRealTimeAdjustments(optimizedRoute, request);
      
      // Cache the result
      this.cacheRoute(cacheKey, finalRoute);
      
      // Record metrics
      apmService.recordBusinessMetric({
        event: 'route_optimized',
        value: 1,
        timestamp: Date.now(),
        properties: {
          distance: finalRoute.totalDistance,
          duration: finalRoute.estimatedDuration,
          confidence: finalRoute.confidence,
        },
      });

      timer.end(true);
      return finalRoute;
      
    } catch (error) {
      timer.end(false);
      console.error('Route optimization failed:', error);
      
      // Fallback to basic route calculation
      return this.getFallbackRoute(request);
    }
  }

  /**
   * Extract features for ML model
   */
  private async extractFeatures(request: RouteOptimizationRequest): Promise<MLModelFeatures> {
    const now = request.timeOfDay || new Date();
    const distance = this.calculateStraightLineDistance(request.pickup, request.dropoff);
    
    // Get historical data
    const historicalData = await this.getHistoricalData(request.pickup, request.dropoff, now);
    
    // Get current traffic data
    const trafficData = await this.getCurrentTrafficData(request.pickup, request.dropoff);
    
    return {
      // Temporal features
      hourOfDay: now.getHours(),
      dayOfWeek: now.getDay(),
      isWeekend: now.getDay() === 0 || now.getDay() === 6,
      isHoliday: await this.isHoliday(now),
      
      // Geographic features
      pickupLatitude: request.pickup.latitude,
      pickupLongitude: request.pickup.longitude,
      dropoffLatitude: request.dropoff.latitude,
      dropoffLongitude: request.dropoff.longitude,
      straightLineDistance: distance,
      
      // Traffic features
      trafficDensity: trafficData.density,
      averageSpeed: trafficData.averageSpeed,
      incidentCount: request.trafficData?.incidents?.length || 0,
      
      // Weather features
      temperature: request.weatherConditions?.temperature || 25,
      precipitation: 0, // Would come from weather API
      visibility: request.weatherConditions?.visibility || 10,
      windSpeed: 0, // Would come from weather API
      
      // Historical features
      historicalAverageDuration: historicalData.averageDuration,
      historicalTrafficPattern: historicalData.trafficPattern,
      popularityScore: historicalData.popularityScore,
    };
  }

  /**
   * Generate multiple route options
   */
  private async generateRouteOptions(request: RouteOptimizationRequest): Promise<OptimizedRoute[]> {
    const routes: OptimizedRoute[] = [];
    
    // Generate different route strategies
    const strategies = [
      'fastest',
      'shortest',
      'balanced',
      'eco_friendly',
      'avoid_traffic',
    ];

    for (const strategy of strategies) {
      try {
        const route = await this.generateRouteForStrategy(request, strategy);
        routes.push(route);
      } catch (error) {
        console.error(`Failed to generate ${strategy} route:`, error);
      }
    }

    return routes;
  }

  /**
   * Generate route for specific strategy
   */
  private async generateRouteForStrategy(
    request: RouteOptimizationRequest, 
    strategy: string
  ): Promise<OptimizedRoute> {
    // This would integrate with mapping services like Google Maps, Mapbox, or OpenStreetMap
    const baseDistance = this.calculateStraightLineDistance(request.pickup, request.dropoff);
    const routeFactor = this.getRouteFactorForStrategy(strategy);
    
    const distance = baseDistance * routeFactor;
    const baseDuration = this.estimateBaseDuration(distance);
    
    // Apply traffic and weather adjustments
    const trafficDelay = this.calculateTrafficDelay(request.trafficData, baseDuration);
    const weatherDelay = this.calculateWeatherDelay(request.weatherConditions, baseDuration);
    
    const totalDuration = baseDuration + trafficDelay + weatherDelay;
    
    return {
      routeId: `${strategy}_${Date.now()}`,
      totalDistance: distance,
      estimatedDuration: totalDuration,
      estimatedCost: this.calculateBaseCost(distance, totalDuration),
      confidence: 0.8, // Will be updated by ML scoring
      waypoints: [request.pickup, request.dropoff], // Simplified
      trafficImpact: {
        delayMinutes: trafficDelay,
        congestionLevel: this.getTrafficLevel(request.trafficData),
      },
      weatherImpact: {
        delayMinutes: weatherDelay,
        riskLevel: this.getWeatherRiskLevel(request.weatherConditions),
      },
      optimizationFactors: this.getOptimizationFactors(strategy),
    };
  }

  /**
   * Score routes using ML model
   */
  private async scoreRoutes(
    routes: OptimizedRoute[], 
    features: MLModelFeatures
  ): Promise<OptimizedRoute[]> {
    try {
      // Call ML model API for scoring
      const response = await fetch(`${this.modelEndpoint}/score-routes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          routes: routes.map(r => ({
            routeId: r.routeId,
            distance: r.totalDistance,
            duration: r.estimatedDuration,
            cost: r.estimatedCost,
          })),
          features,
        }),
      });

      if (response.ok) {
        const scores = await response.json();
        
        // Update route confidence scores
        return routes.map(route => ({
          ...route,
          confidence: scores[route.routeId] || route.confidence,
        }));
      }
    } catch (error) {
      console.error('ML scoring failed:', error);
    }

    // Fallback to rule-based scoring
    return this.ruleBasedScoring(routes, features);
  }

  /**
   * Rule-based scoring fallback
   */
  private ruleBasedScoring(routes: OptimizedRoute[], features: MLModelFeatures): OptimizedRoute[] {
    return routes.map(route => {
      let score = 0.5; // Base score
      
      // Prefer shorter routes
      if (route.totalDistance < features.straightLineDistance * 1.2) {
        score += 0.2;
      }
      
      // Prefer faster routes during peak hours
      if (features.hourOfDay >= 7 && features.hourOfDay <= 10 || 
          features.hourOfDay >= 17 && features.hourOfDay <= 20) {
        if (route.estimatedDuration < features.historicalAverageDuration * 1.1) {
          score += 0.2;
        }
      }
      
      // Weather adjustments
      if (features.visibility < 5 && route.weatherImpact.riskLevel === 'low') {
        score += 0.1;
      }
      
      return {
        ...route,
        confidence: Math.min(Math.max(score, 0), 1),
      };
    });
  }

  /**
   * Select optimal route from scored options
   */
  private selectOptimalRoute(routes: OptimizedRoute[], request: RouteOptimizationRequest): OptimizedRoute {
    // Sort by confidence score
    const sortedRoutes = routes.sort((a, b) => b.confidence - a.confidence);
    
    // Apply user preferences
    if (request.preferences?.preferFastestRoute) {
      return sortedRoutes.sort((a, b) => a.estimatedDuration - b.estimatedDuration)[0];
    }
    
    if (request.preferences?.preferShortestRoute) {
      return sortedRoutes.sort((a, b) => a.totalDistance - b.totalDistance)[0];
    }
    
    // Return highest confidence route
    const optimal = sortedRoutes[0];
    
    // Add alternative routes
    optimal.alternativeRoutes = sortedRoutes.slice(1, 3);
    
    return optimal;
  }

  /**
   * Apply real-time adjustments
   */
  private async applyRealTimeAdjustments(
    route: OptimizedRoute, 
    request: RouteOptimizationRequest
  ): Promise<OptimizedRoute> {
    // Get real-time traffic updates
    const realTimeTraffic = await this.getRealTimeTrafficData(route.waypoints);
    
    // Adjust duration based on real-time data
    const trafficAdjustment = realTimeTraffic.delayFactor || 1;
    const adjustedDuration = route.estimatedDuration * trafficAdjustment;
    
    // Update cost based on adjusted duration
    const adjustedCost = this.calculateBaseCost(route.totalDistance, adjustedDuration);
    
    return {
      ...route,
      estimatedDuration: adjustedDuration,
      estimatedCost: adjustedCost,
      trafficImpact: {
        ...route.trafficImpact,
        delayMinutes: (adjustedDuration - route.estimatedDuration),
      },
    };
  }

  /**
   * Utility methods
   */
  private calculateStraightLineDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private estimateBaseDuration(distance: number): number {
    // Estimate based on average city speed (25 km/h)
    return (distance / 25) * 60; // Convert to minutes
  }

  private calculateBaseCost(distance: number, duration: number): number {
    const baseFare = 30; // Base fare in INR
    const perKmRate = 10; // Per km rate
    const perMinuteRate = 1.5; // Per minute rate
    
    return baseFare + (distance * perKmRate) + (duration * perMinuteRate);
  }

  private generateCacheKey(request: RouteOptimizationRequest): string {
    const key = `${request.pickup.latitude},${request.pickup.longitude}-${request.dropoff.latitude},${request.dropoff.longitude}`;
    return Buffer.from(key).toString('base64');
  }

  private getCachedRoute(key: string): OptimizedRoute | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - parseInt(cached.routeId.split('_')[1]) < this.cacheExpiry) {
      return cached;
    }
    return null;
  }

  private cacheRoute(key: string, route: OptimizedRoute): void {
    this.cache.set(key, route);
    
    // Clean up old cache entries
    if (this.cache.size > 1000) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  private async getFallbackRoute(request: RouteOptimizationRequest): Promise<OptimizedRoute> {
    const distance = this.calculateStraightLineDistance(request.pickup, request.dropoff) * 1.3;
    const duration = this.estimateBaseDuration(distance);
    
    return {
      routeId: `fallback_${Date.now()}`,
      totalDistance: distance,
      estimatedDuration: duration,
      estimatedCost: this.calculateBaseCost(distance, duration),
      confidence: 0.6,
      waypoints: [request.pickup, request.dropoff],
      trafficImpact: { delayMinutes: 0, congestionLevel: 'medium' },
      weatherImpact: { delayMinutes: 0, riskLevel: 'low' },
      optimizationFactors: { distanceWeight: 0.3, timeWeight: 0.4, costWeight: 0.2, safetyWeight: 0.1 },
    };
  }

  // Placeholder methods for external data sources
  private async getHistoricalData(pickup: Location, dropoff: Location, time: Date) {
    return { averageDuration: 20, trafficPattern: 0.8, popularityScore: 0.7 };
  }

  private async getCurrentTrafficData(pickup: Location, dropoff: Location) {
    return { density: 0.6, averageSpeed: 25 };
  }

  private async getRealTimeTrafficData(waypoints: Location[]) {
    return { delayFactor: 1.1 };
  }

  private async isHoliday(date: Date): Promise<boolean> {
    // Would integrate with holiday API
    return false;
  }

  private getRouteFactorForStrategy(strategy: string): number {
    const factors = {
      fastest: 1.1,
      shortest: 1.05,
      balanced: 1.15,
      eco_friendly: 1.2,
      avoid_traffic: 1.25,
    };
    return factors[strategy] || 1.15;
  }

  private calculateTrafficDelay(trafficData: any, baseDuration: number): number {
    if (!trafficData) return 0;
    
    const congestionFactors = {
      light: 0,
      moderate: baseDuration * 0.1,
      heavy: baseDuration * 0.3,
      severe: baseDuration * 0.5,
    };
    
    return congestionFactors[trafficData.currentConditions] || 0;
  }

  private calculateWeatherDelay(weatherConditions: any, baseDuration: number): number {
    if (!weatherConditions) return 0;
    
    const weatherFactors = {
      clear: 0,
      rain: baseDuration * 0.15,
      fog: baseDuration * 0.25,
      snow: baseDuration * 0.4,
    };
    
    return weatherFactors[weatherConditions.condition] || 0;
  }

  private getTrafficLevel(trafficData: any): 'low' | 'medium' | 'high' {
    if (!trafficData) return 'low';
    
    const levelMap = {
      light: 'low',
      moderate: 'medium',
      heavy: 'high',
      severe: 'high',
    };
    
    return levelMap[trafficData.currentConditions] || 'medium';
  }

  private getWeatherRiskLevel(weatherConditions: any): 'low' | 'medium' | 'high' {
    if (!weatherConditions) return 'low';
    
    const riskMap = {
      clear: 'low',
      rain: 'medium',
      fog: 'high',
      snow: 'high',
    };
    
    return riskMap[weatherConditions.condition] || 'low';
  }

  private getOptimizationFactors(strategy: string) {
    const factors = {
      fastest: { distanceWeight: 0.2, timeWeight: 0.5, costWeight: 0.2, safetyWeight: 0.1 },
      shortest: { distanceWeight: 0.5, timeWeight: 0.2, costWeight: 0.2, safetyWeight: 0.1 },
      balanced: { distanceWeight: 0.3, timeWeight: 0.3, costWeight: 0.3, safetyWeight: 0.1 },
      eco_friendly: { distanceWeight: 0.4, timeWeight: 0.2, costWeight: 0.3, safetyWeight: 0.1 },
      avoid_traffic: { distanceWeight: 0.2, timeWeight: 0.4, costWeight: 0.2, safetyWeight: 0.2 },
    };
    
    return factors[strategy] || factors.balanced;
  }
}

// Export singleton instance
export const routeOptimizationService = new RouteOptimizationService();
export default routeOptimizationService;
