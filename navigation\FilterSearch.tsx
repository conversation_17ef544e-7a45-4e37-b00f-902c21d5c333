'use client';

import { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { 
  Search, 
  Filter, 
  X, 
  SlidersHorizontal,
  Calendar,
  MapPin,
  Star,
  DollarSign,
  Clock,
  Users,
  Car,
  Bike,
  Package,
  Building2
} from 'lucide-react';

interface FilterOption {
  id: string;
  label: string;
  value: string | number;
  count?: number;
  icon?: any;
}

interface FilterGroup {
  id: string;
  label: string;
  type: 'checkbox' | 'radio' | 'range' | 'select' | 'date';
  options?: FilterOption[];
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
}

interface ActiveFilter {
  groupId: string;
  groupLabel: string;
  optionId: string;
  optionLabel: string;
  value: any;
}

interface FilterSearchProps {
  searchPlaceholder?: string;
  filterGroups?: FilterGroup[];
  onSearchChange?: (query: string) => void;
  onFiltersChange?: (filters: Record<string, any>) => void;
  className?: string;
  showQuickFilters?: boolean;
  quickFilters?: FilterOption[];
}

interface QuickSearchProps {
  suggestions?: string[];
  recentSearches?: string[];
  onSearch?: (query: string) => void;
  onSuggestionClick?: (suggestion: string) => void;
  placeholder?: string;
  className?: string;
}

// Main Filter and Search Component
export default function FilterSearch({
  searchPlaceholder = "Search...",
  filterGroups = [],
  onSearchChange,
  onFiltersChange,
  className,
  showQuickFilters = true,
  quickFilters = []
}: FilterSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Handle search input
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
    onSearchChange?.(query);
  }, [onSearchChange]);

  // Handle filter changes
  const handleFilterChange = useCallback((groupId: string, value: any) => {
    const newFilters = { ...activeFilters, [groupId]: value };
    setActiveFilters(newFilters);
    onFiltersChange?.(newFilters);
  }, [activeFilters, onFiltersChange]);

  // Clear specific filter
  const clearFilter = (groupId: string) => {
    const newFilters = { ...activeFilters };
    delete newFilters[groupId];
    setActiveFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setActiveFilters({});
    onFiltersChange?.({});
  };

  // Get active filter count
  const activeFilterCount = Object.keys(activeFilters).length;

  // Get active filter badges
  const getActiveFilterBadges = (): ActiveFilter[] => {
    const badges: ActiveFilter[] = [];
    
    Object.entries(activeFilters).forEach(([groupId, value]) => {
      const group = filterGroups.find(g => g.id === groupId);
      if (!group) return;

      if (Array.isArray(value)) {
        value.forEach(v => {
          const option = group.options?.find(o => o.value === v);
          if (option) {
            badges.push({
              groupId,
              groupLabel: group.label,
              optionId: option.id,
              optionLabel: option.label,
              value: v
            });
          }
        });
      } else if (value !== null && value !== undefined && value !== '') {
        const option = group.options?.find(o => o.value === value);
        badges.push({
          groupId,
          groupLabel: group.label,
          optionId: option?.id || 'custom',
          optionLabel: option?.label || String(value),
          value
        });
      }
    });

    return badges;
  };

  const activeFilterBadges = getActiveFilterBadges();

  return (
    <div className={cn('space-y-4', className)}>
      {/* Search and Filter Bar */}
      <div className="flex items-center space-x-3">
        {/* Search Input */}
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 pr-4"
          />
        </div>

        {/* Filter Button */}
        <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <Badge className="ml-2 h-5 w-5 p-0 text-xs bg-blue-500 text-white">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="end">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Filters</h3>
                {activeFilterCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAllFilters}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    Clear all
                  </Button>
                )}
              </div>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {filterGroups.map((group) => (
                <FilterGroup
                  key={group.id}
                  group={group}
                  value={activeFilters[group.id]}
                  onChange={(value) => handleFilterChange(group.id, value)}
                />
              ))}
            </div>
          </PopoverContent>
        </Popover>

        {/* Advanced Filters Button */}
        <Button variant="outline" size="sm">
          <SlidersHorizontal className="h-4 w-4" />
        </Button>
      </div>

      {/* Quick Filters */}
      {showQuickFilters && quickFilters.length > 0 && (
        <div className="flex items-center space-x-2 overflow-x-auto pb-2">
          <span className="text-sm text-gray-500 whitespace-nowrap">Quick filters:</span>
          {quickFilters.map((filter) => {
            const Icon = filter.icon;
            return (
              <Button
                key={filter.id}
                variant="outline"
                size="sm"
                className="whitespace-nowrap"
                onClick={() => handleSearchChange(filter.label)}
              >
                {Icon && <Icon className="h-3 w-3 mr-1" />}
                {filter.label}
                {filter.count && (
                  <Badge variant="secondary" className="ml-1 text-xs">
                    {filter.count}
                  </Badge>
                )}
              </Button>
            );
          })}
        </div>
      )}

      {/* Active Filter Badges */}
      {activeFilterBadges.length > 0 && (
        <div className="flex items-center space-x-2 flex-wrap">
          <span className="text-sm text-gray-500">Active filters:</span>
          {activeFilterBadges.map((filter, index) => (
            <Badge
              key={`${filter.groupId}-${filter.optionId}-${index}`}
              variant="secondary"
              className="flex items-center space-x-1"
            >
              <span>{filter.optionLabel}</span>
              <button
                onClick={() => clearFilter(filter.groupId)}
                className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}

// Individual Filter Group Component
function FilterGroup({ 
  group, 
  value, 
  onChange 
}: { 
  group: FilterGroup; 
  value: any; 
  onChange: (value: any) => void; 
}) {
  const handleCheckboxChange = (optionValue: any, checked: boolean) => {
    const currentValues = Array.isArray(value) ? value : [];
    if (checked) {
      onChange([...currentValues, optionValue]);
    } else {
      onChange(currentValues.filter((v: any) => v !== optionValue));
    }
  };

  return (
    <div className="p-4 border-b border-gray-200 last:border-b-0">
      <Label className="text-sm font-medium mb-3 block">{group.label}</Label>
      
      {group.type === 'checkbox' && group.options && (
        <div className="space-y-2">
          {group.options.map((option) => {
            const Icon = option.icon;
            const isChecked = Array.isArray(value) ? value.includes(option.value) : false;
            
            return (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={isChecked}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange(option.value, checked as boolean)
                  }
                />
                <label
                  htmlFor={option.id}
                  className="text-sm flex items-center space-x-2 cursor-pointer flex-1"
                >
                  {Icon && <Icon className="h-4 w-4 text-gray-500" />}
                  <span>{option.label}</span>
                  {option.count && (
                    <Badge variant="secondary" className="text-xs">
                      {option.count}
                    </Badge>
                  )}
                </label>
              </div>
            );
          })}
        </div>
      )}

      {group.type === 'select' && group.options && (
        <Select value={value || ''} onValueChange={onChange}>
          <SelectTrigger>
            <SelectValue placeholder={group.placeholder || 'Select option'} />
          </SelectTrigger>
          <SelectContent>
            {group.options.map((option) => (
              <SelectItem key={option.id} value={String(option.value)}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      {group.type === 'range' && (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              placeholder="Min"
              min={group.min}
              max={group.max}
              step={group.step}
              value={value?.min || ''}
              onChange={(e) => onChange({ ...value, min: e.target.value })}
              className="flex-1"
            />
            <span className="text-gray-500">to</span>
            <Input
              type="number"
              placeholder="Max"
              min={group.min}
              max={group.max}
              step={group.step}
              value={value?.max || ''}
              onChange={(e) => onChange({ ...value, max: e.target.value })}
              className="flex-1"
            />
          </div>
        </div>
      )}
    </div>
  );
}

// Quick Search Component with Suggestions
export function QuickSearch({
  suggestions = [],
  recentSearches = [],
  onSearch,
  onSuggestionClick,
  placeholder = "Quick search...",
  className
}: QuickSearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery);
    onSearch?.(searchQuery);
    setIsOpen(false);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    onSuggestionClick?.(suggestion);
    setIsOpen(false);
  };

  return (
    <div className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch(query)}
          className="pl-10 pr-4"
        />
      </div>

      {isOpen && (suggestions.length > 0 || recentSearches.length > 0) && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
          {recentSearches.length > 0 && (
            <div className="p-2 border-b border-gray-100">
              <div className="text-xs text-gray-500 mb-2">Recent searches</div>
              {recentSearches.map((search, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(search)}
                  className="w-full text-left px-2 py-1 text-sm hover:bg-gray-50 rounded flex items-center space-x-2"
                >
                  <Clock className="h-3 w-3 text-gray-400" />
                  <span>{search}</span>
                </button>
              ))}
            </div>
          )}

          {suggestions.length > 0 && (
            <div className="p-2">
              <div className="text-xs text-gray-500 mb-2">Suggestions</div>
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-2 py-1 text-sm hover:bg-gray-50 rounded flex items-center space-x-2"
                >
                  <Search className="h-3 w-3 text-gray-400" />
                  <span>{suggestion}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
