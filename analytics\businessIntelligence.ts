// Advanced Business Intelligence and Analytics Service
import { apmService } from '../monitoring/apm';
import connectDB from '../mongodb';
import { Ride } from '../models/Ride';
import { User } from '../models/User';
import { Payment } from '../models/Payment';
import { Wallet } from '../models/Wallet';

export interface AnalyticsTimeRange {
  start: Date;
  end: Date;
  granularity: 'hour' | 'day' | 'week' | 'month';
}

export interface BusinessMetrics {
  revenue: {
    total: number;
    growth: number;
    breakdown: {
      rides: number;
      platformFees: number;
      surgePricing: number;
      other: number;
    };
    trends: Array<{
      period: string;
      value: number;
      change: number;
    }>;
  };
  rides: {
    total: number;
    completed: number;
    cancelled: number;
    completionRate: number;
    averageDistance: number;
    averageDuration: number;
    trends: Array<{
      period: string;
      total: number;
      completed: number;
      cancelled: number;
    }>;
  };
  users: {
    total: number;
    active: number;
    new: number;
    retention: {
      daily: number;
      weekly: number;
      monthly: number;
    };
    segments: Array<{
      segment: string;
      count: number;
      percentage: number;
      revenue: number;
    }>;
  };
  drivers: {
    total: number;
    active: number;
    online: number;
    utilization: number;
    earnings: {
      total: number;
      average: number;
      median: number;
    };
    performance: Array<{
      driverId: string;
      name: string;
      rating: number;
      ridesCompleted: number;
      earnings: number;
      onlineHours: number;
    }>;
  };
  financial: {
    grossRevenue: number;
    netRevenue: number;
    costs: {
      paymentProcessing: number;
      driverPayouts: number;
      operations: number;
      marketing: number;
    };
    profitMargin: number;
    cashFlow: Array<{
      period: string;
      inflow: number;
      outflow: number;
      net: number;
    }>;
  };
}

export interface PredictiveAnalytics {
  demandForecast: {
    nextHour: number;
    nextDay: number;
    nextWeek: number;
    confidence: number;
    factors: string[];
  };
  revenueForecast: {
    nextDay: number;
    nextWeek: number;
    nextMonth: number;
    confidence: number;
    assumptions: string[];
  };
  churnPrediction: {
    riskUsers: Array<{
      userId: string;
      riskScore: number;
      factors: string[];
      recommendedActions: string[];
    }>;
    riskDrivers: Array<{
      driverId: string;
      riskScore: number;
      factors: string[];
      recommendedActions: string[];
    }>;
  };
  marketTrends: {
    growthRate: number;
    seasonality: Array<{
      period: string;
      factor: number;
    }>;
    competitorImpact: number;
    recommendations: string[];
  };
}

export interface CustomerBehaviorAnalysis {
  userJourney: {
    acquisition: {
      channels: Array<{
        channel: string;
        users: number;
        cost: number;
        ltv: number;
        roi: number;
      }>;
    };
    engagement: {
      averageSessionDuration: number;
      ridesPerUser: number;
      timeToFirstRide: number;
      dropoffPoints: Array<{
        stage: string;
        dropoffRate: number;
      }>;
    };
    retention: {
      cohorts: Array<{
        cohort: string;
        day1: number;
        day7: number;
        day30: number;
        day90: number;
      }>;
    };
  };
  preferences: {
    rideTypes: Array<{
      type: string;
      usage: number;
      satisfaction: number;
    }>;
    timePatterns: Array<{
      hour: number;
      usage: number;
    }>;
    locationPatterns: Array<{
      area: string;
      pickups: number;
      dropoffs: number;
    }>;
  };
  satisfaction: {
    overallRating: number;
    nps: number;
    feedback: Array<{
      category: string;
      sentiment: 'positive' | 'neutral' | 'negative';
      count: number;
      impact: number;
    }>;
  };
}

class BusinessIntelligenceService {
  private cache: Map<string, any> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes

  /**
   * Get comprehensive business metrics
   */
  async getBusinessMetrics(timeRange: AnalyticsTimeRange): Promise<BusinessMetrics> {
    const timer = apmService.startTimer('business_metrics');
    
    try {
      await connectDB();

      // Check cache first
      const cacheKey = this.generateCacheKey('business_metrics', timeRange);
      const cachedMetrics = this.getCachedData(cacheKey);
      if (cachedMetrics) {
        timer.end(true);
        return cachedMetrics;
      }

      // Get all metrics in parallel
      const [
        revenueMetrics,
        rideMetrics,
        userMetrics,
        driverMetrics,
        financialMetrics
      ] = await Promise.all([
        this.getRevenueMetrics(timeRange),
        this.getRideMetrics(timeRange),
        this.getUserMetrics(timeRange),
        this.getDriverMetrics(timeRange),
        this.getFinancialMetrics(timeRange),
      ]);

      const businessMetrics: BusinessMetrics = {
        revenue: revenueMetrics,
        rides: rideMetrics,
        users: userMetrics,
        drivers: driverMetrics,
        financial: financialMetrics,
      };

      // Cache the result
      this.cacheData(cacheKey, businessMetrics);

      timer.end(true);
      return businessMetrics;

    } catch (error) {
      timer.end(false);
      console.error('Business metrics calculation failed:', error);
      throw error;
    }
  }

  /**
   * Get predictive analytics
   */
  async getPredictiveAnalytics(timeRange: AnalyticsTimeRange): Promise<PredictiveAnalytics> {
    const timer = apmService.startTimer('predictive_analytics');
    
    try {
      await connectDB();

      // Check cache first
      const cacheKey = this.generateCacheKey('predictive_analytics', timeRange);
      const cachedAnalytics = this.getCachedData(cacheKey);
      if (cachedAnalytics) {
        timer.end(true);
        return cachedAnalytics;
      }

      // Get historical data for predictions
      const historicalData = await this.getHistoricalData(timeRange);

      // Calculate predictions
      const [
        demandForecast,
        revenueForecast,
        churnPrediction,
        marketTrends
      ] = await Promise.all([
        this.calculateDemandForecast(historicalData),
        this.calculateRevenueForecast(historicalData),
        this.calculateChurnPrediction(historicalData),
        this.calculateMarketTrends(historicalData),
      ]);

      const predictiveAnalytics: PredictiveAnalytics = {
        demandForecast,
        revenueForecast,
        churnPrediction,
        marketTrends,
      };

      // Cache the result
      this.cacheData(cacheKey, predictiveAnalytics);

      timer.end(true);
      return predictiveAnalytics;

    } catch (error) {
      timer.end(false);
      console.error('Predictive analytics calculation failed:', error);
      throw error;
    }
  }

  /**
   * Get customer behavior analysis
   */
  async getCustomerBehaviorAnalysis(timeRange: AnalyticsTimeRange): Promise<CustomerBehaviorAnalysis> {
    const timer = apmService.startTimer('customer_behavior_analysis');
    
    try {
      await connectDB();

      // Check cache first
      const cacheKey = this.generateCacheKey('customer_behavior', timeRange);
      const cachedAnalysis = this.getCachedData(cacheKey);
      if (cachedAnalysis) {
        timer.end(true);
        return cachedAnalysis;
      }

      // Get behavior data in parallel
      const [
        userJourney,
        preferences,
        satisfaction
      ] = await Promise.all([
        this.analyzeUserJourney(timeRange),
        this.analyzeUserPreferences(timeRange),
        this.analyzeSatisfaction(timeRange),
      ]);

      const behaviorAnalysis: CustomerBehaviorAnalysis = {
        userJourney,
        preferences,
        satisfaction,
      };

      // Cache the result
      this.cacheData(cacheKey, behaviorAnalysis);

      timer.end(true);
      return behaviorAnalysis;

    } catch (error) {
      timer.end(false);
      console.error('Customer behavior analysis failed:', error);
      throw error;
    }
  }

  /**
   * Get revenue metrics
   */
  private async getRevenueMetrics(timeRange: AnalyticsTimeRange) {
    const revenueData = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          platformFees: { $sum: '$platformFee' },
          count: { $sum: 1 }
        }
      }
    ]);

    const total = revenueData[0]?.totalRevenue || 0;
    const platformFees = revenueData[0]?.platformFees || 0;

    // Calculate growth (simplified - would compare with previous period)
    const growth = 15.5; // Placeholder

    // Get trends data
    const trends = await this.getRevenueTrends(timeRange);

    return {
      total,
      growth,
      breakdown: {
        rides: total - platformFees,
        platformFees,
        surgePricing: total * 0.1, // Estimated
        other: 0,
      },
      trends,
    };
  }

  /**
   * Get ride metrics
   */
  private async getRideMetrics(timeRange: AnalyticsTimeRange) {
    const rideData = await Ride.aggregate([
      {
        $match: {
          createdAt: { $gte: timeRange.start, $lte: timeRange.end }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          avgDistance: { $avg: '$estimatedDistance' },
          avgDuration: { $avg: '$estimatedDuration' }
        }
      }
    ]);

    const total = rideData.reduce((sum, item) => sum + item.count, 0);
    const completed = rideData.find(item => item._id === 'completed')?.count || 0;
    const cancelled = rideData.find(item => item._id === 'cancelled')?.count || 0;
    const completionRate = total > 0 ? (completed / total) * 100 : 0;

    const averageDistance = rideData.reduce((sum, item) => sum + (item.avgDistance || 0), 0) / rideData.length || 0;
    const averageDuration = rideData.reduce((sum, item) => sum + (item.avgDuration || 0), 0) / rideData.length || 0;

    // Get trends data
    const trends = await this.getRideTrends(timeRange);

    return {
      total,
      completed,
      cancelled,
      completionRate,
      averageDistance,
      averageDuration,
      trends,
    };
  }

  /**
   * Get user metrics
   */
  private async getUserMetrics(timeRange: AnalyticsTimeRange) {
    const userData = await User.aggregate([
      {
        $facet: {
          total: [
            { $match: { createdAt: { $lte: timeRange.end } } },
            { $count: 'count' }
          ],
          new: [
            { $match: { createdAt: { $gte: timeRange.start, $lte: timeRange.end } } },
            { $count: 'count' }
          ],
          active: [
            { $match: { lastActiveAt: { $gte: timeRange.start, $lte: timeRange.end } } },
            { $count: 'count' }
          ],
          segments: [
            {
              $group: {
                _id: '$role',
                count: { $sum: 1 }
              }
            }
          ]
        }
      }
    ]);

    const total = userData[0]?.total[0]?.count || 0;
    const newUsers = userData[0]?.new[0]?.count || 0;
    const active = userData[0]?.active[0]?.count || 0;

    return {
      total,
      active,
      new: newUsers,
      retention: {
        daily: 65, // Placeholder - would calculate from actual data
        weekly: 45,
        monthly: 25,
      },
      segments: userData[0]?.segments.map((segment: any) => ({
        segment: segment._id,
        count: segment.count,
        percentage: (segment.count / total) * 100,
        revenue: 0, // Would calculate from payment data
      })) || [],
    };
  }

  /**
   * Get driver metrics
   */
  private async getDriverMetrics(timeRange: AnalyticsTimeRange) {
    const driverData = await User.aggregate([
      {
        $match: { role: 'driver' }
      },
      {
        $facet: {
          total: [{ $count: 'count' }],
          active: [
            { $match: { lastActiveAt: { $gte: timeRange.start, $lte: timeRange.end } } },
            { $count: 'count' }
          ],
          online: [
            { $match: { isOnline: true } },
            { $count: 'count' }
          ]
        }
      }
    ]);

    const total = driverData[0]?.total[0]?.count || 0;
    const active = driverData[0]?.active[0]?.count || 0;
    const online = driverData[0]?.online[0]?.count || 0;

    // Get earnings data
    const earningsData = await Wallet.aggregate([
      {
        $match: { 
          userId: { $in: await User.find({ role: 'driver' }).distinct('_id') }
        }
      },
      {
        $group: {
          _id: null,
          totalEarnings: { $sum: '$totalEarned' },
          avgEarnings: { $avg: '$totalEarned' }
        }
      }
    ]);

    const totalEarnings = earningsData[0]?.totalEarnings || 0;
    const avgEarnings = earningsData[0]?.avgEarnings || 0;

    return {
      total,
      active,
      online,
      utilization: active > 0 ? (online / active) * 100 : 0,
      earnings: {
        total: totalEarnings,
        average: avgEarnings,
        median: avgEarnings * 0.8, // Simplified
      },
      performance: [], // Would populate with actual driver performance data
    };
  }

  /**
   * Get financial metrics
   */
  private async getFinancialMetrics(timeRange: AnalyticsTimeRange) {
    const paymentData = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: timeRange.start, $lte: timeRange.end },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: null,
          grossRevenue: { $sum: '$amount' },
          platformFees: { $sum: '$platformFee' },
          paymentProcessingFees: { $sum: '$paymentGatewayFee' }
        }
      }
    ]);

    const grossRevenue = paymentData[0]?.grossRevenue || 0;
    const platformFees = paymentData[0]?.platformFees || 0;
    const paymentProcessingFees = paymentData[0]?.paymentProcessingFees || 0;

    // Calculate other costs (simplified)
    const driverPayouts = grossRevenue * 0.7; // 70% to drivers
    const operations = grossRevenue * 0.1; // 10% operations
    const marketing = grossRevenue * 0.05; // 5% marketing

    const totalCosts = driverPayouts + operations + marketing + paymentProcessingFees;
    const netRevenue = grossRevenue - totalCosts;
    const profitMargin = grossRevenue > 0 ? (netRevenue / grossRevenue) * 100 : 0;

    return {
      grossRevenue,
      netRevenue,
      costs: {
        paymentProcessing: paymentProcessingFees,
        driverPayouts,
        operations,
        marketing,
      },
      profitMargin,
      cashFlow: [], // Would populate with actual cash flow data
    };
  }

  /**
   * Utility methods for trends and predictions
   */
  private async getRevenueTrends(timeRange: AnalyticsTimeRange) {
    // Simplified trend calculation
    return [
      { period: 'Week 1', value: 50000, change: 5.2 },
      { period: 'Week 2', value: 52600, change: 5.2 },
      { period: 'Week 3', value: 55373, change: 5.3 },
      { period: 'Week 4', value: 58342, change: 5.4 },
    ];
  }

  private async getRideTrends(timeRange: AnalyticsTimeRange) {
    // Simplified trend calculation
    return [
      { period: 'Week 1', total: 1200, completed: 1080, cancelled: 120 },
      { period: 'Week 2', total: 1260, completed: 1134, cancelled: 126 },
      { period: 'Week 3', total: 1323, total: 1191, cancelled: 132 },
      { period: 'Week 4', total: 1389, completed: 1250, cancelled: 139 },
    ];
  }

  private async getHistoricalData(timeRange: AnalyticsTimeRange) {
    // Would fetch comprehensive historical data
    return {
      rides: [],
      revenue: [],
      users: [],
      drivers: [],
    };
  }

  private async calculateDemandForecast(historicalData: any) {
    // Simplified demand forecasting
    return {
      nextHour: 45,
      nextDay: 1200,
      nextWeek: 8500,
      confidence: 0.78,
      factors: ['Historical patterns', 'Weather conditions', 'Events'],
    };
  }

  private async calculateRevenueForecast(historicalData: any) {
    // Simplified revenue forecasting
    return {
      nextDay: 58000,
      nextWeek: 410000,
      nextMonth: 1750000,
      confidence: 0.82,
      assumptions: ['Current growth rate', 'Seasonal patterns', 'Market conditions'],
    };
  }

  private async calculateChurnPrediction(historicalData: any) {
    // Simplified churn prediction
    return {
      riskUsers: [],
      riskDrivers: [],
    };
  }

  private async calculateMarketTrends(historicalData: any) {
    // Simplified market trend analysis
    return {
      growthRate: 15.5,
      seasonality: [
        { period: 'Q1', factor: 0.9 },
        { period: 'Q2', factor: 1.1 },
        { period: 'Q3', factor: 1.0 },
        { period: 'Q4', factor: 1.2 },
      ],
      competitorImpact: 0.05,
      recommendations: [
        'Focus on driver acquisition during peak seasons',
        'Implement dynamic pricing during high demand periods',
        'Improve user retention through loyalty programs',
      ],
    };
  }

  private async analyzeUserJourney(timeRange: AnalyticsTimeRange) {
    // Simplified user journey analysis
    return {
      acquisition: {
        channels: [
          { channel: 'Organic', users: 1200, cost: 0, ltv: 450, roi: Infinity },
          { channel: 'Social Media', users: 800, cost: 15000, ltv: 380, roi: 20.3 },
          { channel: 'Referral', users: 600, cost: 9000, ltv: 520, roi: 34.7 },
        ],
      },
      engagement: {
        averageSessionDuration: 8.5,
        ridesPerUser: 3.2,
        timeToFirstRide: 2.1,
        dropoffPoints: [
          { stage: 'Registration', dropoffRate: 15 },
          { stage: 'First Ride', dropoffRate: 25 },
          { stage: 'Payment', dropoffRate: 8 },
        ],
      },
      retention: {
        cohorts: [
          { cohort: 'Jan 2024', day1: 85, day7: 65, day30: 35, day90: 20 },
          { cohort: 'Feb 2024', day1: 88, day7: 68, day30: 38, day90: 22 },
        ],
      },
    };
  }

  private async analyzeUserPreferences(timeRange: AnalyticsTimeRange) {
    // Simplified preference analysis
    return {
      rideTypes: [
        { type: 'Standard', usage: 70, satisfaction: 4.2 },
        { type: 'Premium', usage: 20, satisfaction: 4.6 },
        { type: 'Shared', usage: 10, satisfaction: 3.8 },
      ],
      timePatterns: Array.from({ length: 24 }, (_, hour) => ({
        hour,
        usage: Math.random() * 100,
      })),
      locationPatterns: [
        { area: 'Business District', pickups: 450, dropoffs: 380 },
        { area: 'Residential', pickups: 320, dropoffs: 420 },
        { area: 'Entertainment', pickups: 280, dropoffs: 250 },
      ],
    };
  }

  private async analyzeSatisfaction(timeRange: AnalyticsTimeRange) {
    // Simplified satisfaction analysis
    return {
      overallRating: 4.3,
      nps: 65,
      feedback: [
        { category: 'Driver Quality', sentiment: 'positive' as const, count: 450, impact: 0.8 },
        { category: 'App Experience', sentiment: 'positive' as const, count: 380, impact: 0.6 },
        { category: 'Pricing', sentiment: 'neutral' as const, count: 220, impact: 0.4 },
        { category: 'Wait Time', sentiment: 'negative' as const, count: 150, impact: 0.7 },
      ],
    };
  }

  /**
   * Cache management
   */
  private generateCacheKey(type: string, timeRange: AnalyticsTimeRange): string {
    const key = `${type}_${timeRange.start.getTime()}_${timeRange.end.getTime()}_${timeRange.granularity}`;
    return Buffer.from(key).toString('base64');
  }

  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    return null;
  }

  private cacheData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Clean up old cache entries
    if (this.cache.size > 100) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }
}

// Export singleton instance
export const businessIntelligenceService = new BusinessIntelligenceService();
export default businessIntelligenceService;
