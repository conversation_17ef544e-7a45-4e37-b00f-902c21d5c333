// Micro-Mobility Fleet Management Service
import { apmService } from '../monitoring/apm';
import { Location } from '../ai/routeOptimization';

export interface MicroMobilityVehicle {
  id: string;
  type: 'bike' | 'scooter' | 'e_bike' | 'e_scooter';
  model: string;
  manufacturer: string;
  status: 'available' | 'in_use' | 'maintenance' | 'charging' | 'offline' | 'damaged';
  location: Location;
  battery: {
    level: number; // 0-100%
    isCharging: boolean;
    estimatedRange: number; // in km
    lastCharged: Date;
    chargingCycles: number;
  };
  hardware: {
    gpsEnabled: boolean;
    lockStatus: 'locked' | 'unlocked';
    lights: boolean;
    horn: boolean;
    sensors: {
      accelerometer: boolean;
      gyroscope: boolean;
      temperature: boolean;
      vibration: boolean;
    };
  };
  maintenance: {
    lastService: Date;
    nextService: Date;
    mileage: number; // total km traveled
    issues: MaintenanceIssue[];
    serviceHistory: ServiceRecord[];
  };
  pricing: {
    unlockFee: number;
    perMinuteRate: number;
    perKmRate: number;
    maxDailyRate: number;
  };
  zone: {
    id: string;
    name: string;
    type: 'parking' | 'no_parking' | 'slow_zone' | 'restricted';
  };
  lastRide?: {
    userId: string;
    startTime: Date;
    endTime: Date;
    distance: number;
    duration: number;
    rating: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface MaintenanceIssue {
  id: string;
  type: 'mechanical' | 'electrical' | 'cosmetic' | 'safety';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  reportedBy: 'user' | 'system' | 'maintenance';
  reportedAt: Date;
  resolvedAt?: Date;
  cost?: number;
}

export interface ServiceRecord {
  id: string;
  type: 'routine' | 'repair' | 'battery_replacement' | 'upgrade';
  description: string;
  technician: string;
  cost: number;
  partsReplaced: string[];
  serviceDate: Date;
  nextServiceDue: Date;
}

export interface ParkingZone {
  id: string;
  name: string;
  type: 'designated' | 'preferred' | 'allowed' | 'restricted';
  location: Location;
  radius: number; // in meters
  capacity: number;
  currentOccupancy: number;
  pricing: {
    parkingFee: number;
    incentive: number; // for parking in preferred zones
  };
  rules: {
    maxParkingTime: number; // in minutes
    allowedVehicleTypes: string[];
    operatingHours: {
      start: string;
      end: string;
    };
  };
  facilities: {
    chargingStations: number;
    security: boolean;
    shelter: boolean;
    maintenance: boolean;
  };
}

export interface RideRequest {
  userId: string;
  vehicleType: 'bike' | 'scooter' | 'e_bike' | 'e_scooter';
  location: Location;
  maxWalkingDistance: number; // in meters
  preferences: {
    batteryLevel: number; // minimum battery level
    maxUnlockFee: number;
    preferredBrands: string[];
  };
}

export interface RideSession {
  id: string;
  userId: string;
  vehicleId: string;
  startTime: Date;
  endTime?: Date;
  startLocation: Location;
  endLocation?: Location;
  route: Location[];
  distance: number;
  duration: number; // in minutes
  cost: {
    unlockFee: number;
    timeCharge: number;
    distanceCharge: number;
    parkingFee: number;
    total: number;
  };
  status: 'active' | 'completed' | 'cancelled' | 'paused';
  issues: string[];
  rating?: number;
  feedback?: string;
}

class FleetManagementService {
  private vehicles: Map<string, MicroMobilityVehicle> = new Map();
  private parkingZones: Map<string, ParkingZone> = new Map();
  private activeRides: Map<string, RideSession> = new Map();
  private maintenanceQueue: Map<string, MaintenanceIssue[]> = new Map();

  constructor() {
    this.initializeFleet();
    this.startFleetMonitoring();
  }

  /**
   * Find available vehicles near location
   */
  async findNearbyVehicles(request: RideRequest): Promise<MicroMobilityVehicle[]> {
    const timer = apmService.startTimer('find_nearby_vehicles');
    
    try {
      const availableVehicles: MicroMobilityVehicle[] = [];

      for (const vehicle of this.vehicles.values()) {
        // Check basic availability criteria
        if (vehicle.status !== 'available') continue;
        if (vehicle.type !== request.vehicleType) continue;
        if (vehicle.battery.level < request.preferences.batteryLevel) continue;
        if (vehicle.pricing.unlockFee > request.preferences.maxUnlockFee) continue;

        // Check distance
        const distance = this.calculateDistance(request.location, vehicle.location);
        if (distance * 1000 > request.maxWalkingDistance) continue; // Convert km to meters

        // Check brand preference
        if (request.preferences.preferredBrands.length > 0 && 
            !request.preferences.preferredBrands.includes(vehicle.manufacturer)) continue;

        availableVehicles.push(vehicle);
      }

      // Sort by distance and battery level
      availableVehicles.sort((a, b) => {
        const distanceA = this.calculateDistance(request.location, a.location);
        const distanceB = this.calculateDistance(request.location, b.location);
        
        // Primary sort by distance, secondary by battery level
        if (Math.abs(distanceA - distanceB) < 0.1) { // Within 100m
          return b.battery.level - a.battery.level;
        }
        return distanceA - distanceB;
      });

      timer.end(true);
      return availableVehicles.slice(0, 10); // Return top 10 vehicles

    } catch (error) {
      timer.end(false);
      console.error('Find nearby vehicles failed:', error);
      throw error;
    }
  }

  /**
   * Reserve a vehicle for a user
   */
  async reserveVehicle(vehicleId: string, userId: string): Promise<{ success: boolean; reservationId?: string; expiresAt?: Date }> {
    const timer = apmService.startTimer('reserve_vehicle');
    
    try {
      const vehicle = this.vehicles.get(vehicleId);
      if (!vehicle) {
        throw new Error('Vehicle not found');
      }

      if (vehicle.status !== 'available') {
        throw new Error('Vehicle is not available');
      }

      // Reserve the vehicle for 10 minutes
      vehicle.status = 'in_use'; // Temporarily mark as in use
      const reservationId = `res_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

      // Set a timeout to release the reservation
      setTimeout(() => {
        if (vehicle.status === 'in_use' && !this.activeRides.has(vehicleId)) {
          vehicle.status = 'available';
        }
      }, 10 * 60 * 1000);

      timer.end(true);
      return { success: true, reservationId, expiresAt };

    } catch (error) {
      timer.end(false);
      console.error('Vehicle reservation failed:', error);
      return { success: false };
    }
  }

  /**
   * Start a ride session
   */
  async startRide(vehicleId: string, userId: string, location: Location): Promise<RideSession> {
    const timer = apmService.startTimer('start_ride');
    
    try {
      const vehicle = this.vehicles.get(vehicleId);
      if (!vehicle) {
        throw new Error('Vehicle not found');
      }

      if (vehicle.status !== 'in_use') {
        throw new Error('Vehicle is not reserved or available');
      }

      // Create ride session
      const rideSession: RideSession = {
        id: `ride_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        vehicleId,
        startTime: new Date(),
        startLocation: location,
        route: [location],
        distance: 0,
        duration: 0,
        cost: {
          unlockFee: vehicle.pricing.unlockFee,
          timeCharge: 0,
          distanceCharge: 0,
          parkingFee: 0,
          total: vehicle.pricing.unlockFee,
        },
        status: 'active',
        issues: [],
      };

      // Update vehicle status
      vehicle.status = 'in_use';
      vehicle.hardware.lockStatus = 'unlocked';
      vehicle.location = location;

      // Store active ride
      this.activeRides.set(rideSession.id, rideSession);

      // Start ride tracking
      this.startRideTracking(rideSession.id);

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'micromobility_ride_started',
        value: 1,
        timestamp: Date.now(),
        properties: {
          vehicleType: vehicle.type,
          batteryLevel: vehicle.battery.level,
          unlockFee: vehicle.pricing.unlockFee,
        },
      });

      timer.end(true);
      return rideSession;

    } catch (error) {
      timer.end(false);
      console.error('Start ride failed:', error);
      throw error;
    }
  }

  /**
   * End a ride session
   */
  async endRide(rideId: string, endLocation: Location, rating?: number, feedback?: string): Promise<RideSession> {
    const timer = apmService.startTimer('end_ride');
    
    try {
      const rideSession = this.activeRides.get(rideId);
      if (!rideSession) {
        throw new Error('Ride session not found');
      }

      const vehicle = this.vehicles.get(rideSession.vehicleId);
      if (!vehicle) {
        throw new Error('Vehicle not found');
      }

      // Calculate final costs
      const endTime = new Date();
      const duration = (endTime.getTime() - rideSession.startTime.getTime()) / (1000 * 60); // minutes
      const distance = this.calculateTotalDistance(rideSession.route, endLocation);

      const timeCharge = duration * vehicle.pricing.perMinuteRate;
      const distanceCharge = distance * vehicle.pricing.perKmRate;
      
      // Check if parked in restricted zone
      const parkingZone = this.findParkingZone(endLocation);
      const parkingFee = parkingZone?.type === 'restricted' ? 50 : 0; // ₹50 penalty for restricted parking

      const total = Math.min(
        rideSession.cost.unlockFee + timeCharge + distanceCharge + parkingFee,
        vehicle.pricing.maxDailyRate
      );

      // Update ride session
      rideSession.endTime = endTime;
      rideSession.endLocation = endLocation;
      rideSession.duration = duration;
      rideSession.distance = distance;
      rideSession.cost = {
        unlockFee: rideSession.cost.unlockFee,
        timeCharge,
        distanceCharge,
        parkingFee,
        total,
      };
      rideSession.status = 'completed';
      rideSession.rating = rating;
      rideSession.feedback = feedback;

      // Update vehicle
      vehicle.status = 'available';
      vehicle.hardware.lockStatus = 'locked';
      vehicle.location = endLocation;
      vehicle.maintenance.mileage += distance;
      vehicle.lastRide = {
        userId: rideSession.userId,
        startTime: rideSession.startTime,
        endTime: endTime,
        distance,
        duration,
        rating: rating || 0,
      };

      // Check if vehicle needs maintenance
      await this.checkMaintenanceNeeds(vehicle);

      // Remove from active rides
      this.activeRides.delete(rideId);

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'micromobility_ride_completed',
        value: total,
        timestamp: Date.now(),
        properties: {
          vehicleType: vehicle.type,
          duration,
          distance,
          rating: rating || 0,
        },
      });

      timer.end(true);
      return rideSession;

    } catch (error) {
      timer.end(false);
      console.error('End ride failed:', error);
      throw error;
    }
  }

  /**
   * Get fleet status and analytics
   */
  async getFleetStatus(): Promise<{
    summary: any;
    vehicles: any;
    maintenance: any;
    utilization: any;
  }> {
    const totalVehicles = this.vehicles.size;
    const availableVehicles = Array.from(this.vehicles.values()).filter(v => v.status === 'available').length;
    const inUseVehicles = Array.from(this.vehicles.values()).filter(v => v.status === 'in_use').length;
    const maintenanceVehicles = Array.from(this.vehicles.values()).filter(v => v.status === 'maintenance').length;
    const chargingVehicles = Array.from(this.vehicles.values()).filter(v => v.status === 'charging').length;

    // Calculate average battery level
    const avgBatteryLevel = Array.from(this.vehicles.values())
      .reduce((sum, v) => sum + v.battery.level, 0) / totalVehicles;

    // Get vehicles by type
    const vehiclesByType = Array.from(this.vehicles.values()).reduce((acc, vehicle) => {
      acc[vehicle.type] = (acc[vehicle.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get maintenance queue
    const maintenanceQueue = Array.from(this.maintenanceQueue.entries()).map(([vehicleId, issues]) => ({
      vehicleId,
      issueCount: issues.length,
      criticalIssues: issues.filter(i => i.severity === 'critical').length,
    }));

    // Calculate utilization rate (simplified)
    const utilizationRate = (inUseVehicles / totalVehicles) * 100;

    return {
      summary: {
        totalVehicles,
        availableVehicles,
        inUseVehicles,
        maintenanceVehicles,
        chargingVehicles,
        avgBatteryLevel: Math.round(avgBatteryLevel),
        utilizationRate: Math.round(utilizationRate * 100) / 100,
      },
      vehicles: {
        byType: vehiclesByType,
        byStatus: {
          available: availableVehicles,
          in_use: inUseVehicles,
          maintenance: maintenanceVehicles,
          charging: chargingVehicles,
        },
      },
      maintenance: {
        queueLength: maintenanceQueue.length,
        criticalIssues: maintenanceQueue.reduce((sum, item) => sum + item.criticalIssues, 0),
        queue: maintenanceQueue,
      },
      utilization: {
        current: utilizationRate,
        target: 70, // Target utilization rate
        trend: 'stable', // Would calculate from historical data
      },
    };
  }

  /**
   * Redistribute vehicles based on demand
   */
  async redistributeFleet(demandHotspots: Array<{ location: Location; demand: number }>): Promise<{
    redistributions: Array<{
      vehicleId: string;
      from: Location;
      to: Location;
      priority: 'high' | 'medium' | 'low';
    }>;
  }> {
    const redistributions: any[] = [];

    // Find vehicles in low-demand areas
    const lowDemandVehicles = Array.from(this.vehicles.values()).filter(vehicle => {
      if (vehicle.status !== 'available') return false;
      
      // Check if vehicle is in a low-demand area
      const nearbyDemand = demandHotspots.find(hotspot => 
        this.calculateDistance(vehicle.location, hotspot.location) < 0.5 // Within 500m
      );
      
      return !nearbyDemand || nearbyDemand.demand < 2;
    });

    // Find high-demand areas with insufficient vehicles
    const highDemandAreas = demandHotspots.filter(hotspot => {
      const nearbyVehicles = Array.from(this.vehicles.values()).filter(vehicle =>
        vehicle.status === 'available' &&
        this.calculateDistance(vehicle.location, hotspot.location) < 0.5
      );
      
      return hotspot.demand > nearbyVehicles.length * 2; // Need more vehicles
    });

    // Create redistribution plan
    for (const demandArea of highDemandAreas) {
      const vehiclesNeeded = Math.min(3, Math.ceil(demandArea.demand / 2)); // Max 3 vehicles per area
      
      for (let i = 0; i < vehiclesNeeded && lowDemandVehicles.length > 0; i++) {
        const vehicle = lowDemandVehicles.shift()!;
        
        redistributions.push({
          vehicleId: vehicle.id,
          from: vehicle.location,
          to: demandArea.location,
          priority: demandArea.demand > 5 ? 'high' : 'medium',
        });
      }
    }

    return { redistributions };
  }

  /**
   * Private helper methods
   */
  private initializeFleet(): void {
    // Initialize sample fleet (would load from database)
    const sampleVehicles: Partial<MicroMobilityVehicle>[] = [
      {
        id: 'bike_001',
        type: 'e_bike',
        model: 'Urban Cruiser',
        manufacturer: 'EcoRide',
        status: 'available',
        location: { latitude: 12.9716, longitude: 77.5946 },
        battery: { level: 85, isCharging: false, estimatedRange: 25, lastCharged: new Date(), chargingCycles: 150 },
        pricing: { unlockFee: 10, perMinuteRate: 2, perKmRate: 5, maxDailyRate: 200 },
      },
      {
        id: 'scooter_001',
        type: 'e_scooter',
        model: 'City Zip',
        manufacturer: 'QuickMove',
        status: 'available',
        location: { latitude: 12.9784, longitude: 77.6408 },
        battery: { level: 92, isCharging: false, estimatedRange: 30, lastCharged: new Date(), chargingCycles: 89 },
        pricing: { unlockFee: 15, perMinuteRate: 3, perKmRate: 8, maxDailyRate: 300 },
      },
    ];

    sampleVehicles.forEach((vehicleData, index) => {
      const vehicle: MicroMobilityVehicle = {
        ...vehicleData,
        hardware: {
          gpsEnabled: true,
          lockStatus: 'locked',
          lights: true,
          horn: true,
          sensors: {
            accelerometer: true,
            gyroscope: true,
            temperature: true,
            vibration: true,
          },
        },
        maintenance: {
          lastService: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          nextService: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
          mileage: Math.random() * 1000,
          issues: [],
          serviceHistory: [],
        },
        zone: {
          id: 'zone_001',
          name: 'Central Business District',
          type: 'parking',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      } as MicroMobilityVehicle;

      this.vehicles.set(vehicle.id, vehicle);
    });

    // Initialize parking zones
    const sampleZones: ParkingZone[] = [
      {
        id: 'zone_001',
        name: 'Central Business District',
        type: 'designated',
        location: { latitude: 12.9716, longitude: 77.5946 },
        radius: 500,
        capacity: 50,
        currentOccupancy: 12,
        pricing: { parkingFee: 0, incentive: 5 },
        rules: {
          maxParkingTime: 480, // 8 hours
          allowedVehicleTypes: ['bike', 'scooter', 'e_bike', 'e_scooter'],
          operatingHours: { start: '06:00', end: '22:00' },
        },
        facilities: {
          chargingStations: 10,
          security: true,
          shelter: true,
          maintenance: true,
        },
      },
    ];

    sampleZones.forEach(zone => this.parkingZones.set(zone.id, zone));
  }

  private startFleetMonitoring(): void {
    // Monitor fleet status every 30 seconds
    setInterval(() => {
      this.updateFleetStatus();
    }, 30000);

    // Check maintenance needs every hour
    setInterval(() => {
      this.checkAllMaintenanceNeeds();
    }, 60 * 60 * 1000);
  }

  private updateFleetStatus(): void {
    // Update battery levels, GPS positions, etc.
    for (const vehicle of this.vehicles.values()) {
      if (vehicle.status === 'in_use') {
        // Simulate battery drain during use
        vehicle.battery.level = Math.max(0, vehicle.battery.level - 0.5);
        
        // Check if battery is critically low
        if (vehicle.battery.level < 10) {
          this.reportMaintenanceIssue(vehicle.id, {
            type: 'electrical',
            severity: 'high',
            description: 'Battery level critically low',
            reportedBy: 'system',
          });
        }
      } else if (vehicle.status === 'charging') {
        // Simulate charging
        vehicle.battery.level = Math.min(100, vehicle.battery.level + 2);
        
        if (vehicle.battery.level >= 95) {
          vehicle.status = 'available';
          vehicle.battery.isCharging = false;
        }
      }
    }
  }

  private async checkMaintenanceNeeds(vehicle: MicroMobilityVehicle): Promise<void> {
    const issues: Partial<MaintenanceIssue>[] = [];

    // Check mileage-based maintenance
    if (vehicle.maintenance.mileage > 500 && 
        vehicle.maintenance.nextService < new Date()) {
      issues.push({
        type: 'mechanical',
        severity: 'medium',
        description: 'Routine maintenance due',
        reportedBy: 'system',
      });
    }

    // Check battery health
    if (vehicle.battery.chargingCycles > 500) {
      issues.push({
        type: 'electrical',
        severity: 'medium',
        description: 'Battery replacement recommended',
        reportedBy: 'system',
      });
    }

    // Report issues
    for (const issue of issues) {
      await this.reportMaintenanceIssue(vehicle.id, issue);
    }
  }

  private async checkAllMaintenanceNeeds(): void {
    for (const vehicle of this.vehicles.values()) {
      await this.checkMaintenanceNeeds(vehicle);
    }
  }

  private async reportMaintenanceIssue(vehicleId: string, issueData: Partial<MaintenanceIssue>): Promise<void> {
    const issue: MaintenanceIssue = {
      id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: issueData.type || 'mechanical',
      severity: issueData.severity || 'medium',
      description: issueData.description || 'Maintenance required',
      reportedBy: issueData.reportedBy || 'system',
      reportedAt: new Date(),
    };

    // Add to maintenance queue
    const existingIssues = this.maintenanceQueue.get(vehicleId) || [];
    existingIssues.push(issue);
    this.maintenanceQueue.set(vehicleId, existingIssues);

    // Update vehicle status if critical
    if (issue.severity === 'critical') {
      const vehicle = this.vehicles.get(vehicleId);
      if (vehicle && vehicle.status === 'available') {
        vehicle.status = 'maintenance';
      }
    }
  }

  private startRideTracking(rideId: string): void {
    const trackingInterval = setInterval(() => {
      const rideSession = this.activeRides.get(rideId);
      if (!rideSession || rideSession.status !== 'active') {
        clearInterval(trackingInterval);
        return;
      }

      // Simulate location updates (would come from GPS)
      const vehicle = this.vehicles.get(rideSession.vehicleId);
      if (vehicle) {
        // Update vehicle location and add to route
        const newLocation = this.simulateMovement(vehicle.location);
        vehicle.location = newLocation;
        rideSession.route.push(newLocation);
        
        // Update distance
        rideSession.distance = this.calculateTotalDistance(rideSession.route);
      }
    }, 10000); // Update every 10 seconds
  }

  private calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private calculateTotalDistance(route: Location[], endLocation?: Location): number {
    let totalDistance = 0;
    const fullRoute = endLocation ? [...route, endLocation] : route;
    
    for (let i = 1; i < fullRoute.length; i++) {
      totalDistance += this.calculateDistance(fullRoute[i - 1], fullRoute[i]);
    }
    
    return totalDistance;
  }

  private findParkingZone(location: Location): ParkingZone | undefined {
    for (const zone of this.parkingZones.values()) {
      const distance = this.calculateDistance(location, zone.location);
      if (distance * 1000 <= zone.radius) { // Convert km to meters
        return zone;
      }
    }
    return undefined;
  }

  private simulateMovement(currentLocation: Location): Location {
    // Simulate small movement for demo purposes
    const deltaLat = (Math.random() - 0.5) * 0.001; // ~100m
    const deltaLon = (Math.random() - 0.5) * 0.001;
    
    return {
      latitude: currentLocation.latitude + deltaLat,
      longitude: currentLocation.longitude + deltaLon,
    };
  }
}

}

// Dynamic Pricing Service for Micro-Mobility
export class MicroMobilityPricingService {
  private basePricing = {
    bike: { unlockFee: 10, perMinuteRate: 1.5, perKmRate: 3 },
    scooter: { unlockFee: 15, perMinuteRate: 2, perKmRate: 5 },
    e_bike: { unlockFee: 12, perMinuteRate: 2, perKmRate: 4 },
    e_scooter: { unlockFee: 18, perMinuteRate: 3, perKmRate: 6 },
  };

  /**
   * Calculate dynamic pricing based on demand, battery, and location
   */
  calculateDynamicPricing(
    vehicleType: string,
    batteryLevel: number,
    location: Location,
    demandLevel: 'low' | 'medium' | 'high' | 'peak',
    timeOfDay: Date
  ) {
    const basePrices = this.basePricing[vehicleType] || this.basePricing.bike;

    // Base multipliers
    let unlockMultiplier = 1;
    let rateMultiplier = 1;

    // Demand-based pricing
    const demandMultipliers = {
      low: 0.8,
      medium: 1.0,
      high: 1.3,
      peak: 1.6,
    };
    rateMultiplier *= demandMultipliers[demandLevel];

    // Battery-based incentives
    if (batteryLevel < 30) {
      rateMultiplier *= 0.9; // 10% discount for low battery
    } else if (batteryLevel > 80) {
      rateMultiplier *= 1.1; // 10% premium for high battery
    }

    // Time-based pricing
    const hour = timeOfDay.getHours();
    if (hour >= 7 && hour <= 9 || hour >= 17 && hour <= 19) {
      rateMultiplier *= 1.2; // Rush hour premium
    } else if (hour >= 22 || hour <= 6) {
      rateMultiplier *= 0.9; // Night discount
    }

    return {
      unlockFee: Math.round(basePrices.unlockFee * unlockMultiplier),
      perMinuteRate: Math.round(basePrices.perMinuteRate * rateMultiplier * 100) / 100,
      perKmRate: Math.round(basePrices.perKmRate * rateMultiplier * 100) / 100,
      maxDailyRate: Math.round(basePrices.unlockFee * 15 * rateMultiplier), // 15x unlock fee
      multiplier: rateMultiplier,
      factors: {
        demand: demandLevel,
        battery: batteryLevel,
        timeOfDay: hour,
      },
    };
  }
}

// Export singleton instances
export const fleetManagementService = new FleetManagementService();
export const microMobilityPricingService = new MicroMobilityPricingService();
export default fleetManagementService;
