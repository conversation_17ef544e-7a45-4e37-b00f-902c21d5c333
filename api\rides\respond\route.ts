import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Ride } from '@/lib/models/Ride';
import { User } from '@/lib/models/User';
import { authenticateRequest } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (user.role !== 'driver') {
      return NextResponse.json(
        { error: 'Only drivers can respond to ride requests' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { rideId, action, requestId } = body;

    if (!rideId || !action || !['accept', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Valid ride ID and action (accept/reject) are required' },
        { status: 400 }
      );
    }

    // Verify the ride exists and is still pending
    const ride = await Ride.findOne({ 
      _id: rideId,
      status: 'pending'
    }).populate('riderId', 'firstName lastName phone');

    if (!ride) {
      return NextResponse.json(
        { error: 'Ride not found or no longer available' },
        { status: 404 }
      );
    }

    // Verify the driver is approved and active
    const driver = await User.findOne({
      _id: user.userId,
      role: 'driver',
      isActive: true,
      'driverProfile.isApproved': true,
    });

    if (!driver) {
      return NextResponse.json(
        { error: 'Driver not found or not approved' },
        { status: 403 }
      );
    }

    if (action === 'accept') {
      // Check if ride is already assigned to another driver
      if (ride.driverId) {
        return NextResponse.json(
          { error: 'Ride has already been accepted by another driver' },
          { status: 409 }
        );
      }

      // Assign the driver to the ride
      ride.driverId = user.userId;
      ride.status = 'accepted';
      ride.acceptedAt = new Date();
      await ride.save();

      // Update driver's total rides count
      if (driver.driverProfile) {
        driver.driverProfile.totalRides += 1;
        await driver.save();
      }

      // In production, send notification to rider here
      console.log(`Ride ${rideId} accepted by driver ${driver.firstName} ${driver.lastName}`);

      return NextResponse.json({
        message: 'Ride accepted successfully',
        ride: {
          _id: ride._id,
          status: ride.status,
          pickupLocation: ride.pickupLocation,
          dropoffLocation: ride.dropoffLocation,
          estimatedDistance: ride.estimatedDistance,
          estimatedDuration: ride.estimatedDuration,
          finalAmount: ride.finalAmount,
          rideType: ride.rideType,
          paymentMethod: ride.paymentMethod,
          specialInstructions: ride.specialInstructions,
          acceptedAt: ride.acceptedAt,
          rider: ride.riderId,
        },
        driver: {
          _id: driver._id,
          firstName: driver.firstName,
          lastName: driver.lastName,
          phone: driver.phone,
          profileImage: driver.profileImage,
          driverProfile: {
            rating: driver.driverProfile?.rating || 5.0,
            totalRides: driver.driverProfile?.totalRides || 0,
            vehicleModel: driver.driverProfile?.vehicleModel,
            vehicleColor: driver.driverProfile?.vehicleColor,
          },
        },
      });

    } else if (action === 'reject') {
      // Log the rejection (in production, you might want to track this for analytics)
      console.log(`Ride ${rideId} rejected by driver ${driver.firstName} ${driver.lastName}`);

      // In production, you might want to automatically find the next available driver
      // or notify the rider that the driver declined

      return NextResponse.json({
        message: 'Ride request rejected',
        rideId,
      });
    }

  } catch (error) {
    console.error('Driver response error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
