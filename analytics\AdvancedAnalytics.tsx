'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Car, 
  DollarSign, 
  Clock,
  MapPin,
  Zap,
  Leaf,
  Shield,
  Download,
  Filter
} from 'lucide-react';

interface AnalyticsData {
  overview: {
    totalRides: number;
    totalRevenue: number;
    activeUsers: number;
    averageRating: number;
    carbonSaved: number;
    trends: {
      rides: number;
      revenue: number;
      users: number;
      rating: number;
    };
  };
  timeSeriesData: {
    date: string;
    rides: number;
    revenue: number;
    users: number;
    carbonSaved: number;
  }[];
  vehicleDistribution: {
    type: string;
    count: number;
    percentage: number;
    revenue: number;
  }[];
  geographicData: {
    city: string;
    rides: number;
    revenue: number;
    growth: number;
  }[];
  userSegments: {
    segment: string;
    users: number;
    avgSpend: number;
    retention: number;
  }[];
  operationalMetrics: {
    avgWaitTime: number;
    completionRate: number;
    driverUtilization: number;
    customerSatisfaction: number;
  };
}

interface AdvancedAnalyticsProps {
  userRole: 'admin' | 'corporate' | 'driver' | 'rider';
  timeRange: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange: (range: string) => void;
}

export default function AdvancedAnalytics({ 
  userRole, 
  timeRange, 
  onTimeRangeChange 
}: AdvancedAnalyticsProps) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState('rides');
  const [comparisonMode, setComparisonMode] = useState(false);

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange, userRole]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Mock data - replace with actual API call
      const mockData: AnalyticsData = {
        overview: {
          totalRides: 125430,
          totalRevenue: 8750000,
          activeUsers: 45230,
          averageRating: 4.7,
          carbonSaved: 15600,
          trends: {
            rides: 12.5,
            revenue: 18.3,
            users: 8.7,
            rating: 2.1,
          },
        },
        timeSeriesData: generateTimeSeriesData(),
        vehicleDistribution: [
          { type: 'Bike', count: 45230, percentage: 36, revenue: 2250000 },
          { type: 'Scooter', count: 38750, percentage: 31, revenue: 2875000 },
          { type: 'Auto', count: 25680, percentage: 20, revenue: 2100000 },
          { type: 'Car', count: 15770, percentage: 13, revenue: 1525000 },
        ],
        geographicData: [
          { city: 'Mumbai', rides: 35420, revenue: 2850000, growth: 15.2 },
          { city: 'Delhi', rides: 28750, revenue: 2340000, growth: 12.8 },
          { city: 'Bangalore', rides: 24680, revenue: 1980000, growth: 18.5 },
          { city: 'Chennai', rides: 18930, revenue: 1520000, growth: 10.3 },
          { city: 'Hyderabad', rides: 17650, revenue: 1060000, growth: 22.1 },
        ],
        userSegments: [
          { segment: 'Daily Commuters', users: 18500, avgSpend: 850, retention: 92 },
          { segment: 'Occasional Users', users: 15200, avgSpend: 320, retention: 65 },
          { segment: 'Corporate Users', users: 8750, avgSpend: 1250, retention: 88 },
          { segment: 'Weekend Riders', users: 2780, avgSpend: 180, retention: 45 },
        ],
        operationalMetrics: {
          avgWaitTime: 4.2,
          completionRate: 96.8,
          driverUtilization: 78.5,
          customerSatisfaction: 4.7,
        },
      };

      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateTimeSeriesData = () => {
    const data = [];
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 365;
    
    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      data.push({
        date: date.toISOString().split('T')[0],
        rides: Math.floor(Math.random() * 1000) + 500,
        revenue: Math.floor(Math.random() * 50000) + 25000,
        users: Math.floor(Math.random() * 200) + 100,
        carbonSaved: Math.floor(Math.random() * 100) + 50,
      });
    }
    
    return data;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-IN').format(value);
  };

  const getTrendIcon = (trend: number) => {
    return trend > 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  const getTrendColor = (trend: number) => {
    return trend > 0 ? 'text-green-600' : 'text-red-600';
  };

  if (loading || !analyticsData) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const { overview, timeSeriesData, vehicleDistribution, geographicData, userSegments, operationalMetrics } = analyticsData;

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Comprehensive insights and performance metrics</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Select value={timeRange} onValueChange={onTimeRangeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Rides</p>
                <p className="text-3xl font-bold text-gray-900">{formatNumber(overview.totalRides)}</p>
                <div className="flex items-center mt-2">
                  {getTrendIcon(overview.trends.rides)}
                  <span className={`text-sm ml-1 ${getTrendColor(overview.trends.rides)}`}>
                    {Math.abs(overview.trends.rides)}%
                  </span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Car className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-3xl font-bold text-gray-900">{formatCurrency(overview.totalRevenue)}</p>
                <div className="flex items-center mt-2">
                  {getTrendIcon(overview.trends.revenue)}
                  <span className={`text-sm ml-1 ${getTrendColor(overview.trends.revenue)}`}>
                    {Math.abs(overview.trends.revenue)}%
                  </span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-3xl font-bold text-gray-900">{formatNumber(overview.activeUsers)}</p>
                <div className="flex items-center mt-2">
                  {getTrendIcon(overview.trends.users)}
                  <span className={`text-sm ml-1 ${getTrendColor(overview.trends.users)}`}>
                    {Math.abs(overview.trends.users)}%
                  </span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Carbon Saved</p>
                <p className="text-3xl font-bold text-gray-900">{formatNumber(overview.carbonSaved)} kg</p>
                <div className="flex items-center mt-2">
                  <Leaf className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600 ml-1">+25%</span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Leaf className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Time Series Chart */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>Track key metrics over time</CardDescription>
            </div>
            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rides">Rides</SelectItem>
                <SelectItem value="revenue">Revenue</SelectItem>
                <SelectItem value="users">Users</SelectItem>
                <SelectItem value="carbonSaved">Carbon Saved</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [
                  selectedMetric === 'revenue' ? formatCurrency(value as number) : formatNumber(value as number),
                  name
                ]}
              />
              <Area 
                type="monotone" 
                dataKey={selectedMetric} 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                fillOpacity={0.1}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Vehicle Distribution and Geographic Data */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Distribution</CardTitle>
            <CardDescription>Breakdown by vehicle type</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={vehicleDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="count"
                  label={({ type, percentage }) => `${type} ${percentage}%`}
                >
                  {vehicleDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={['#3B82F6', '#10B981', '#F59E0B', '#EF4444'][index]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatNumber(value as number)} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Cities</CardTitle>
            <CardDescription>Performance by geographic location</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {geographicData.map((city, index) => (
                <div key={city.city} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                    </div>
                    <div>
                      <p className="font-medium">{city.city}</p>
                      <p className="text-sm text-gray-500">{formatNumber(city.rides)} rides</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(city.revenue)}</p>
                    <div className="flex items-center">
                      <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
                      <span className="text-sm text-green-600">{city.growth}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Segments and Operational Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>User Segments</CardTitle>
            <CardDescription>Customer segmentation analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {userSegments.map((segment) => (
                <div key={segment.segment} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{segment.segment}</h4>
                    <Badge variant="secondary">{formatNumber(segment.users)} users</Badge>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Avg Spend</p>
                      <p className="font-medium">{formatCurrency(segment.avgSpend)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Retention</p>
                      <p className="font-medium">{segment.retention}%</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Operational Metrics</CardTitle>
            <CardDescription>Key performance indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <span className="font-medium">Avg Wait Time</span>
                </div>
                <span className="text-2xl font-bold">{operationalMetrics.avgWaitTime} min</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 text-green-600" />
                  <span className="font-medium">Completion Rate</span>
                </div>
                <span className="text-2xl font-bold">{operationalMetrics.completionRate}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  <span className="font-medium">Driver Utilization</span>
                </div>
                <span className="text-2xl font-bold">{operationalMetrics.driverUtilization}%</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Users className="h-5 w-5 text-purple-600" />
                  <span className="font-medium">Customer Satisfaction</span>
                </div>
                <span className="text-2xl font-bold">{operationalMetrics.customerSatisfaction}/5</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
