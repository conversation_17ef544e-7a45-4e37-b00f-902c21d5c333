{"name": "mcp-server", "version": "1.0.0", "description": "Model Context Protocol server for AI tool calling", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "mongoose": "^7.5.0", "axios": "^1.5.0", "socket.io": "^4.7.2", "redis": "^4.6.7", "winston": "^3.10.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "node-cron": "^3.0.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["mcp", "ai", "tool-calling", "automation"], "author": "Two Wheeler Sharing Team", "license": "MIT"}