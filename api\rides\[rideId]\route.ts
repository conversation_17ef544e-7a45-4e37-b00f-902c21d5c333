import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Ride } from '@/lib/models/Ride';
import { authenticateRequest } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: { rideId: string } }
) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { rideId } = params;

    if (!rideId) {
      return NextResponse.json(
        { error: 'Ride ID is required' },
        { status: 400 }
      );
    }

    // Find the ride and populate user data
    const ride = await Ride.findById(rideId)
      .populate('riderId', 'firstName lastName phone profileImage')
      .populate('driverId', 'firstName lastName phone profileImage driverProfile');

    if (!ride) {
      return NextResponse.json(
        { error: 'Ride not found' },
        { status: 404 }
      );
    }

    // Check if user has permission to view this ride
    const isRider = user.userId === ride.riderId._id.toString();
    const isDriver = user.userId === ride.driverId?._id.toString();

    if (!isRider && !isDriver) {
      return NextResponse.json(
        { error: 'You are not authorized to view this ride' },
        { status: 403 }
      );
    }

    // Format ride data for response
    const rideData = {
      _id: ride._id,
      status: ride.status,
      pickupLocation: ride.pickupLocation,
      dropoffLocation: ride.dropoffLocation,
      estimatedDistance: ride.estimatedDistance,
      estimatedDuration: ride.estimatedDuration,
      actualDistance: ride.actualDistance,
      actualDuration: ride.actualDuration,
      finalAmount: ride.finalAmount,
      rideType: ride.rideType,
      paymentMethod: ride.paymentMethod,
      paymentStatus: ride.paymentStatus,
      specialInstructions: ride.specialInstructions,
      isScheduled: ride.isScheduled,
      scheduledTime: ride.scheduledTime,
      requestedAt: ride.requestedAt,
      acceptedAt: ride.acceptedAt,
      startedAt: ride.startedAt,
      completedAt: ride.completedAt,
      cancelledAt: ride.cancelledAt,
      cancelledBy: ride.cancelledBy,
      cancellationFee: ride.cancellationFee,
      driverRating: ride.driverRating,
      riderRating: ride.riderRating,
      riderFeedback: ride.riderFeedback,
      driverFeedback: ride.driverFeedback,
      rider: {
        _id: ride.riderId._id,
        firstName: ride.riderId.firstName,
        lastName: ride.riderId.lastName,
        phone: ride.riderId.phone,
        profileImage: ride.riderId.profileImage,
      },
      driver: ride.driverId ? {
        _id: ride.driverId._id,
        firstName: ride.driverId.firstName,
        lastName: ride.driverId.lastName,
        phone: ride.driverId.phone,
        profileImage: ride.driverId.profileImage,
        driverProfile: {
          rating: ride.driverId.driverProfile?.rating || 5.0,
          totalRides: ride.driverId.driverProfile?.totalRides || 0,
          vehicleModel: ride.driverId.driverProfile?.vehicleModel,
          vehicleColor: ride.driverId.driverProfile?.vehicleColor,
        },
      } : null,
    };

    return NextResponse.json({
      ride: rideData,
    });

  } catch (error) {
    console.error('Get ride error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
