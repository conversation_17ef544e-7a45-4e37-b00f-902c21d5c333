import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { 
      email, 
      password, 
      firstName, 
      lastName, 
      phone, 
      role,
      dateOfBirth,
      address 
    } = body;

    // Validation
    if (!email || !password || !firstName || !lastName || !phone || !role) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!['rider', 'driver'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be either rider or driver' },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { phone }]
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email or phone already exists' },
        { status: 409 }
      );
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user object
    const userData: any = {
      email,
      password: hashedPassword,
      firstName,
      lastName,
      phone,
      role,
      dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
      address,
    };

    // Initialize role-specific profiles
    if (role === 'driver') {
      userData.driverProfile = {
        isApproved: false,
        rating: 5.0,
        totalRides: 0,
        documentsUploaded: {
          license: false,
          registration: false,
          insurance: false,
        },
      };
    } else {
      userData.riderProfile = {
        rating: 5.0,
        totalRides: 0,
      };
    }

    // Create user
    const user = new User(userData);
    await user.save();

    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id, 
        email: user.email, 
        role: user.role 
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Remove password from response
    const userResponse = {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      isVerified: user.isVerified,
      rewardPoints: user.rewardPoints,
      createdAt: user.createdAt,
    };

    return NextResponse.json({
      message: 'User registered successfully',
      user: userResponse,
      token,
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
