import ProtectedRoute from '@/components/auth/ProtectedRoute';
import RideBookingForm from '@/components/rides/RideBookingForm';

export default function BookRidePage() {
  return (
    <ProtectedRoute requiredRole="rider">
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <h1 className="text-3xl font-bold text-gray-900">Book a Ride</h1>
              <p className="mt-2 text-gray-600">Find a nearby driver for your journey</p>
            </div>
          </div>
        </div>
        <div className="py-8">
          <RideBookingForm />
        </div>
      </div>
    </ProtectedRoute>
  );
}

export const metadata = {
  title: 'Book a Ride - Two Wheeler Sharing',
  description: 'Book a two-wheeler ride with nearby drivers',
};
