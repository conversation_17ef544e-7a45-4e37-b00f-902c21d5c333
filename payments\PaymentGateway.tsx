'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  CreditCard, 
  Smartphone, 
  Building2, 
  Wallet, 
  Shield, 
  CheckCircle,
  AlertTriangle,
  Clock
} from 'lucide-react';

declare global {
  interface Window {
    Razorpay: any;
  }
}

interface PaymentGatewayProps {
  amount: number;
  currency?: string;
  rideId?: string;
  description?: string;
  onSuccess: (paymentData: any) => void;
  onFailure: (error: any) => void;
  onClose?: () => void;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  description: string;
  enabled: boolean;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: 'card',
    name: 'Credit/Debit Card',
    icon: CreditCard,
    description: 'Visa, Mastercard, RuPay',
    enabled: true,
  },
  {
    id: 'upi',
    name: 'UPI',
    icon: Smartphone,
    description: 'PhonePe, GPay, Paytm',
    enabled: true,
  },
  {
    id: 'netbanking',
    name: 'Net Banking',
    icon: Building2,
    description: 'All major banks',
    enabled: true,
  },
  {
    id: 'wallet',
    name: 'Digital Wallets',
    icon: Wallet,
    description: 'Paytm, PhonePe, Amazon Pay',
    enabled: true,
  },
];

export default function PaymentGateway({
  amount,
  currency = 'INR',
  rideId,
  description,
  onSuccess,
  onFailure,
  onClose,
}: PaymentGatewayProps) {
  const [loading, setLoading] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<string>('upi');
  const [error, setError] = useState<string | null>(null);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpay = () => {
      return new Promise((resolve) => {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          setRazorpayLoaded(true);
          resolve(true);
        };
        script.onerror = () => resolve(false);
        document.body.appendChild(script);
      });
    };

    if (!window.Razorpay) {
      loadRazorpay();
    } else {
      setRazorpayLoaded(true);
    }
  }, []);

  const handlePayment = async () => {
    if (!razorpayLoaded) {
      setError('Payment gateway not loaded. Please try again.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Create payment order
      const orderResponse = await fetch('/api/payments/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          amount,
          currency,
          rideId,
          description,
          notes: {
            paymentMethod: selectedMethod,
          },
        }),
      });

      if (!orderResponse.ok) {
        const errorData = await orderResponse.json();
        throw new Error(errorData.error || 'Failed to create payment order');
      }

      const orderData = await orderResponse.json();

      // Configure Razorpay options
      const options = {
        key: orderData.data.key,
        amount: orderData.data.amount,
        currency: orderData.data.currency,
        name: 'RideShare',
        description: description || 'Payment for ride',
        order_id: orderData.data.orderId,
        prefill: {
          method: selectedMethod,
        },
        theme: {
          color: '#3B82F6',
        },
        modal: {
          ondismiss: () => {
            setLoading(false);
            onClose?.();
          },
        },
        handler: async (response: any) => {
          try {
            // Verify payment
            const verifyResponse = await fetch('/api/payments/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
                payment_method: selectedMethod,
              }),
            });

            if (!verifyResponse.ok) {
              const errorData = await verifyResponse.json();
              throw new Error(errorData.error || 'Payment verification failed');
            }

            const verifyData = await verifyResponse.json();
            setLoading(false);
            onSuccess(verifyData.data);
          } catch (verifyError) {
            console.error('Payment verification error:', verifyError);
            setLoading(false);
            onFailure(verifyError);
          }
        },
      };

      // Open Razorpay checkout
      const razorpay = new window.Razorpay(options);
      razorpay.open();

    } catch (error) {
      console.error('Payment error:', error);
      setError(error instanceof Error ? error.message : 'Payment failed');
      setLoading(false);
      onFailure(error);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-soft">
      <CardHeader className="text-center">
        <div className="mx-auto bg-gradient-primary w-16 h-16 flex items-center justify-center rounded-2xl mb-4">
          <CreditCard className="h-8 w-8 text-white" />
        </div>
        <CardTitle className="text-xl">Secure Payment</CardTitle>
        <CardDescription>
          Complete your payment of ₹{amount.toFixed(2)}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Payment Amount */}
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <div className="text-3xl font-bold text-primary">₹{amount.toFixed(2)}</div>
          <div className="text-sm text-gray-600">{description || 'Payment'}</div>
        </div>

        {/* Payment Methods */}
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">Choose Payment Method</h3>
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              className={`p-3 rounded-lg border-2 cursor-pointer transition-all ${
                selectedMethod === method.id
                  ? 'border-primary bg-primary/5'
                  : 'border-gray-200 hover:border-gray-300'
              } ${!method.enabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => method.enabled && setSelectedMethod(method.id)}
            >
              <div className="flex items-center space-x-3">
                <method.icon className="h-6 w-6 text-primary" />
                <div className="flex-1">
                  <div className="font-medium">{method.name}</div>
                  <div className="text-sm text-gray-500">{method.description}</div>
                </div>
                {selectedMethod === method.id && (
                  <CheckCircle className="h-5 w-5 text-primary" />
                )}
                {!method.enabled && (
                  <Badge variant="secondary">Coming Soon</Badge>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Security Info */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-green-600" />
            <div className="text-sm text-green-800">
              <div className="font-medium">Secure Payment</div>
              <div>256-bit SSL encryption • PCI DSS compliant</div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Payment Button */}
        <div className="space-y-3">
          <Button
            onClick={handlePayment}
            disabled={loading || !razorpayLoaded}
            className="w-full h-12 bg-gradient-primary"
          >
            {loading ? (
              <>
                <LoadingSpinner className="mr-2" />
                Processing Payment...
              </>
            ) : !razorpayLoaded ? (
              <>
                <Clock className="h-5 w-5 mr-2" />
                Loading Payment Gateway...
              </>
            ) : (
              <>
                <CreditCard className="h-5 w-5 mr-2" />
                Pay ₹{amount.toFixed(2)}
              </>
            )}
          </Button>

          {onClose && (
            <Button variant="outline" onClick={onClose} className="w-full">
              Cancel
            </Button>
          )}
        </div>

        {/* Terms */}
        <div className="text-xs text-gray-500 text-center">
          By proceeding, you agree to our{' '}
          <a href="/terms" className="text-primary hover:underline">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="/privacy" className="text-primary hover:underline">
            Privacy Policy
          </a>
        </div>
      </CardContent>
    </Card>
  );
}
