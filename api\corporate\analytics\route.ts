import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { corporateMobilityService } from '@/lib/corporate/corporateMobilityService';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check user permissions (corporate admin or admin only)
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const allowedRoles = ['corporate_admin', 'admin'];
    if (!allowedRoles.includes(user.role)) {
      return NextResponse.json(
        { error: 'Corporate admin access required for analytics' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const timeRange = searchParams.get('timeRange') || '30d';
    const reportType = searchParams.get('reportType') || 'overview';

    // Get corporate analytics
    const analytics = await corporateMobilityService.getCorporateAnalytics(clientId || undefined);

    // Generate additional insights based on report type
    const insights = generateInsights(analytics, reportType);
    const recommendations = generateRecommendations(analytics);
    const benchmarks = generateBenchmarks(analytics);

    const response = {
      success: true,
      data: {
        overview: {
          ...analytics.overview,
          timeRange,
          reportType,
          generatedAt: new Date(),
        },
        bookings: {
          ...analytics.bookings,
          insights: insights.bookings,
        },
        financial: {
          ...analytics.financial,
          insights: insights.financial,
          benchmarks: benchmarks.financial,
        },
        efficiency: {
          ...analytics.efficiency,
          insights: insights.efficiency,
          benchmarks: benchmarks.efficiency,
        },
        recommendations,
        trends: {
          bookingGrowth: calculateBookingGrowth(analytics),
          costOptimization: calculateCostOptimization(analytics),
          utilizationTrends: calculateUtilizationTrends(analytics),
          satisfactionTrends: calculateSatisfactionTrends(analytics),
        },
        comparisons: {
          industryBenchmarks: getIndustryBenchmarks(),
          previousPeriod: getPreviousPeriodComparison(analytics),
          peerComparison: getPeerComparison(analytics),
        },
        forecasts: {
          nextMonthBookings: forecastBookings(analytics),
          nextMonthSpend: forecastSpend(analytics),
          capacityRequirements: forecastCapacity(analytics),
        },
        alerts: generateAlerts(analytics),
      },
      metadata: {
        clientId,
        timeRange,
        reportType,
        generatedBy: decoded.userId,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Corporate analytics API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch corporate analytics',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Helper functions for analytics insights
function generateInsights(analytics: any, reportType: string): any {
  return {
    bookings: [
      analytics.bookings.byType.business_travel > analytics.overview.totalBookings * 0.6 
        ? 'Business travel dominates your booking patterns'
        : 'Diverse booking types indicate varied mobility needs',
      analytics.bookings.byStatus.completed / analytics.overview.totalBookings > 0.9
        ? 'Excellent booking completion rate'
        : 'Room for improvement in booking completion',
    ],
    financial: [
      analytics.financial.savingsFromCorporateRates > analytics.overview.totalRevenue * 0.1
        ? 'Corporate rates are providing significant savings'
        : 'Consider negotiating better corporate rates',
      analytics.overview.averageBookingValue > 500
        ? 'Higher than average booking values - consider cost optimization'
        : 'Cost-effective booking patterns',
    ],
    efficiency: [
      analytics.efficiency.onTimePerformance > 90
        ? 'Excellent on-time performance'
        : 'On-time performance needs improvement',
      analytics.efficiency.customerSatisfaction > 4.5
        ? 'High customer satisfaction scores'
        : 'Focus on improving customer experience',
    ],
  };
}

function generateRecommendations(analytics: any): string[] {
  const recommendations: string[] = [];

  // Booking optimization
  if (analytics.efficiency.utilizationRate < 70) {
    recommendations.push('Increase utilization by promoting ride-sharing among employees');
  }

  // Cost optimization
  if (analytics.overview.averageBookingValue > 600) {
    recommendations.push('Consider implementing booking approval thresholds to control costs');
  }

  // Efficiency improvements
  if (analytics.efficiency.approvalTime > 4) {
    recommendations.push('Streamline approval process to reduce booking lead time');
  }

  // Service quality
  if (analytics.efficiency.customerSatisfaction < 4.0) {
    recommendations.push('Focus on driver training and vehicle quality improvements');
  }

  // Sustainability
  recommendations.push('Promote eco-friendly vehicle options to reduce carbon footprint');

  return recommendations;
}

function generateBenchmarks(analytics: any): any {
  return {
    financial: {
      industryAverageBookingValue: 450,
      industryAverageSavings: 12, // percentage
      yourPerformance: {
        bookingValue: analytics.overview.averageBookingValue,
        savingsPercentage: (analytics.financial.savingsFromCorporateRates / analytics.overview.totalRevenue) * 100,
      },
    },
    efficiency: {
      industryAverageOnTime: 85, // percentage
      industryAverageSatisfaction: 4.2,
      yourPerformance: {
        onTimePerformance: analytics.efficiency.onTimePerformance,
        customerSatisfaction: analytics.efficiency.customerSatisfaction,
      },
    },
  };
}

function calculateBookingGrowth(analytics: any): any {
  // Mock growth calculation
  return {
    monthOverMonth: 15, // percentage
    quarterOverQuarter: 45,
    yearOverYear: 120,
    trend: 'increasing',
  };
}

function calculateCostOptimization(analytics: any): any {
  return {
    potentialSavings: analytics.overview.totalRevenue * 0.08, // 8% potential savings
    optimizationAreas: [
      { area: 'Route optimization', savings: analytics.overview.totalRevenue * 0.03 },
      { area: 'Vehicle type optimization', savings: analytics.overview.totalRevenue * 0.02 },
      { area: 'Booking timing', savings: analytics.overview.totalRevenue * 0.03 },
    ],
  };
}

function calculateUtilizationTrends(analytics: any): any {
  return {
    current: analytics.efficiency.utilizationRate,
    target: 85,
    trend: 'stable',
    peakHours: ['09:00-10:00', '18:00-19:00'],
    lowUtilizationPeriods: ['14:00-16:00'],
  };
}

function calculateSatisfactionTrends(analytics: any): any {
  return {
    current: analytics.efficiency.customerSatisfaction,
    trend: 'improving',
    topCompliments: ['Punctual drivers', 'Clean vehicles', 'Professional service'],
    improvementAreas: ['Wait times', 'Vehicle comfort', 'Route optimization'],
  };
}

function getIndustryBenchmarks(): any {
  return {
    averageBookingValue: 450,
    onTimePerformance: 85,
    customerSatisfaction: 4.2,
    utilizationRate: 75,
    corporateSavings: 12, // percentage
  };
}

function getPreviousPeriodComparison(analytics: any): any {
  return {
    bookings: {
      current: analytics.overview.totalBookings,
      previous: Math.round(analytics.overview.totalBookings * 0.85),
      change: 15, // percentage
    },
    revenue: {
      current: analytics.overview.totalRevenue,
      previous: Math.round(analytics.overview.totalRevenue * 0.88),
      change: 12,
    },
    satisfaction: {
      current: analytics.efficiency.customerSatisfaction,
      previous: analytics.efficiency.customerSatisfaction - 0.2,
      change: 0.2,
    },
  };
}

function getPeerComparison(analytics: any): any {
  return {
    position: 'Top 25%', // Your company's position
    metrics: {
      costEfficiency: 'Above average',
      serviceQuality: 'Excellent',
      utilization: 'Average',
      sustainability: 'Good',
    },
  };
}

function forecastBookings(analytics: any): any {
  const currentMonthly = analytics.overview.totalBookings / 3; // Assuming 3 months of data
  return {
    predicted: Math.round(currentMonthly * 1.15), // 15% growth
    confidence: 85,
    factors: ['Seasonal trends', 'Business growth', 'New employee onboarding'],
  };
}

function forecastSpend(analytics: any): any {
  const currentMonthly = analytics.overview.totalRevenue / 3;
  return {
    predicted: Math.round(currentMonthly * 1.12), // 12% growth
    confidence: 80,
    breakdown: {
      baseGrowth: currentMonthly * 0.08,
      inflationAdjustment: currentMonthly * 0.04,
    },
  };
}

function forecastCapacity(analytics: any): any {
  return {
    peakHourRequirement: Math.round(analytics.overview.totalBookings * 0.15),
    additionalDriversNeeded: 5,
    vehicleTypeDistribution: {
      economy: 60,
      premium: 30,
      luxury: 10,
    },
  };
}

function generateAlerts(analytics: any): any[] {
  const alerts: any[] = [];

  // Cost alerts
  if (analytics.overview.averageBookingValue > 600) {
    alerts.push({
      type: 'cost',
      severity: 'medium',
      message: 'Average booking value is higher than industry benchmark',
      action: 'Review booking policies and consider cost optimization',
    });
  }

  // Efficiency alerts
  if (analytics.efficiency.onTimePerformance < 80) {
    alerts.push({
      type: 'efficiency',
      severity: 'high',
      message: 'On-time performance below acceptable threshold',
      action: 'Investigate delays and improve driver dispatch',
    });
  }

  // Utilization alerts
  if (analytics.efficiency.utilizationRate < 60) {
    alerts.push({
      type: 'utilization',
      severity: 'medium',
      message: 'Low utilization rate detected',
      action: 'Promote ride-sharing and optimize booking patterns',
    });
  }

  // Satisfaction alerts
  if (analytics.efficiency.customerSatisfaction < 4.0) {
    alerts.push({
      type: 'satisfaction',
      severity: 'high',
      message: 'Customer satisfaction below target',
      action: 'Focus on service quality improvements',
    });
  }

  return alerts;
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
