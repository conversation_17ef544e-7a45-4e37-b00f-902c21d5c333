{"name": "Ride Matching Automation", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ride-request", "responseMode": "responseNode"}, "id": "webhook-ride-request", "name": "Ride Request Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://mcp-server:8080/ai/assign-driver", "sendBody": true, "bodyParameters": {"parameters": [{"name": "rideRequest", "value": "={{ $json.rideRequest }}"}, {"name": "availableDrivers", "value": "={{ $json.availableDrivers }}"}]}, "options": {}}, "id": "ai-driver-assignment", "name": "AI Driver Assignment", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 300]}, {"parameters": {"url": "http://mcp-server:8080/tools/execute", "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "assign_task"}, {"name": "parameters", "value": "={{ {\n  \"taskId\": $json.assignment.taskId || $now,\n  \"driverId\": $json.assignment.recommendedDriverId,\n  \"userId\": $('Ride Request Webhook').item.json.rideRequest.userId,\n  \"route\": $('Ride Request Webhook').item.json.rideRequest.route,\n  \"estimatedEarnings\": $json.assignment.estimatedEarnings\n} }}"}]}}, "id": "assign-task", "name": "Assign Task to Driver", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300]}, {"parameters": {"url": "http://mcp-server:8080/tools/execute", "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "notify_driver"}, {"name": "parameters", "value": "={{ {\n  \"driverId\": $('AI Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"message\": \"New ride request assigned! Pickup: \" + $('Ride Request Webhook').item.json.rideRequest.pickup + \" → \" + $('Ride Request Webhook').item.json.rideRequest.destination,\n  \"taskId\": $('Assign Task to Driver').item.json.taskId,\n  \"priority\": \"high\"\n} }}"}]}}, "id": "notify-driver", "name": "Notify Driver", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [900, 300]}, {"parameters": {"url": "http://mcp-server:8080/tools/execute", "sendBody": true, "bodyParameters": {"parameters": [{"name": "tool", "value": "calculate_pricing"}, {"name": "parameters", "value": "={{ {\n  \"distance\": $('Ride Request Webhook').item.json.rideRequest.distance,\n  \"demand\": $('Ride Request Webhook').item.json.rideRequest.demand || \"medium\",\n  \"weather\": $('Ride Request Webhook').item.json.rideRequest.weather,\n  \"timeOfDay\": $('Ride Request Webhook').item.json.rideRequest.timeOfDay\n} }}"}]}}, "id": "calculate-pricing", "name": "Calculate Dynamic Pricing", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 480]}, {"parameters": {"url": "http://app:3000/api/rides", "sendBody": true, "bodyParameters": {"parameters": [{"name": "rideData", "value": "={{ {\n  \"userId\": $('Ride Request Webhook').item.json.rideRequest.userId,\n  \"driverId\": $('AI Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"pickup\": $('Ride Request Webhook').item.json.rideRequest.pickup,\n  \"destination\": $('Ride Request Webhook').item.json.rideRequest.destination,\n  \"distance\": $('Ride Request Webhook').item.json.rideRequest.distance,\n  \"pricing\": $('Calculate Dynamic Pricing').item.json,\n  \"estimatedPickupTime\": $('AI Driver Assignment').item.json.assignment.estimatedPickupTime,\n  \"status\": \"assigned\",\n  \"assignedAt\": $now\n} }}"}]}}, "id": "create-ride-record", "name": "Create Ride Record", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"rideId\": $('Create Ride Record').item.json.rideId,\n  \"driverId\": $('AI Driver Assignment').item.json.assignment.recommendedDriverId,\n  \"estimatedPickupTime\": $('AI Driver Assignment').item.json.assignment.estimatedPickupTime,\n  \"pricing\": $('Calculate Dynamic Pricing').item.json,\n  \"message\": \"Ride successfully assigned to driver\"\n} }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-success", "leftValue": "={{ $('AI Driver Assignment').item.json.success }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "check-assignment-success", "name": "Check Assignment Success", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [680, 480]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": false,\n  \"error\": \"No suitable driver found\",\n  \"message\": \"Please try again later or adjust your pickup location\"\n} }}"}, "id": "no-driver-response", "name": "No Driver Available Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 600]}], "connections": {"Ride Request Webhook": {"main": [[{"node": "AI Driver Assignment", "type": "main", "index": 0}, {"node": "Calculate Dynamic Pricing", "type": "main", "index": 0}]]}, "AI Driver Assignment": {"main": [[{"node": "Check Assignment Success", "type": "main", "index": 0}]]}, "Check Assignment Success": {"main": [[{"node": "Assign Task to Driver", "type": "main", "index": 0}], [{"node": "No Driver Available Response", "type": "main", "index": 0}]]}, "Assign Task to Driver": {"main": [[{"node": "Notify Driver", "type": "main", "index": 0}]]}, "Notify Driver": {"main": [[{"node": "Create Ride Record", "type": "main", "index": 0}]]}, "Create Ride Record": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "ride-matching-workflow", "tags": []}