import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { routeOptimizationService } from '@/lib/ai/routeOptimization';
import { dynamicPricingEngine } from '@/lib/ai/dynamicPricing';
import { etaPredictionService } from '@/lib/ai/etaPrediction';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { pickup, dropoff, preferences, driverLocation, rideType = 'standard' } = body;

    // Validate required fields
    if (!pickup || !dropoff) {
      return NextResponse.json(
        { error: 'Pickup and dropoff locations are required' },
        { status: 400 }
      );
    }

    if (!pickup.latitude || !pickup.longitude || !dropoff.latitude || !dropoff.longitude) {
      return NextResponse.json(
        { error: 'Invalid location coordinates' },
        { status: 400 }
      );
    }

    // Prepare route optimization request
    const routeRequest = {
      pickup: {
        latitude: pickup.latitude,
        longitude: pickup.longitude,
        address: pickup.address,
      },
      dropoff: {
        latitude: dropoff.latitude,
        longitude: dropoff.longitude,
        address: dropoff.address,
      },
      driverLocation: driverLocation ? {
        latitude: driverLocation.latitude,
        longitude: driverLocation.longitude,
      } : undefined,
      preferences: preferences || {},
      timeOfDay: new Date(),
      trafficData: {
        currentConditions: 'moderate' as const,
        incidents: [],
      },
      weatherConditions: {
        condition: 'clear' as const,
        visibility: 10,
        temperature: 25,
      },
    };

    // Get optimized route
    const optimizedRoute = await routeOptimizationService.optimizeRoute(routeRequest);

    // Calculate dynamic pricing
    const pricingRequest = {
      pickup: routeRequest.pickup,
      dropoff: routeRequest.dropoff,
      distance: optimizedRoute.totalDistance,
      estimatedDuration: optimizedRoute.estimatedDuration,
      requestTime: new Date(),
      rideType: rideType as 'standard' | 'premium' | 'shared',
      userId: decoded.userId,
      driverAvailability: {
        nearbyDrivers: 5, // Would come from driver tracking
        averageDistance: 2,
      },
    };

    const pricing = await dynamicPricingEngine.calculatePrice(pricingRequest);

    // Get ETA prediction
    const etaRequest = {
      origin: routeRequest.pickup,
      destination: routeRequest.dropoff,
      driverLocation: routeRequest.driverLocation,
      vehicleType: 'bike' as const, // Would be determined by ride type
      requestTime: new Date(),
    };

    const etaPrediction = await etaPredictionService.predictETA(etaRequest);

    // Combine all results
    const response = {
      success: true,
      data: {
        route: {
          routeId: optimizedRoute.routeId,
          distance: optimizedRoute.totalDistance,
          duration: optimizedRoute.estimatedDuration,
          confidence: optimizedRoute.confidence,
          waypoints: optimizedRoute.waypoints,
          alternativeRoutes: optimizedRoute.alternativeRoutes?.slice(0, 2), // Limit alternatives
          trafficImpact: optimizedRoute.trafficImpact,
          weatherImpact: optimizedRoute.weatherImpact,
        },
        pricing: {
          finalAmount: pricing.finalAmount,
          breakdown: pricing.breakdown,
          surgePricing: pricing.surgePricing,
          demandPricing: pricing.demandPricing,
          weatherPricing: pricing.weatherPricing,
          confidence: pricing.confidence,
          validUntil: pricing.validUntil,
          alternatives: pricing.alternatives?.map(alt => ({
            rideType: alt.breakdown ? 'shared' : 'premium', // Simplified
            amount: alt.finalAmount,
            savings: pricing.finalAmount - alt.finalAmount,
          })),
        },
        eta: {
          estimatedArrival: etaPrediction.estimatedArrival,
          totalDuration: etaPrediction.estimatedDuration,
          breakdown: etaPrediction.breakdown,
          confidence: etaPrediction.confidence,
          alternatives: etaPrediction.alternatives,
        },
        recommendations: {
          bestRoute: optimizedRoute.confidence > 0.8,
          pricingAdvice: pricing.surgePricing.isActive ? 
            'High demand detected. Consider waiting for lower prices.' : 
            'Good time to book!',
          etaReliability: etaPrediction.confidence > 0.8 ? 'high' : 
                          etaPrediction.confidence > 0.6 ? 'medium' : 'low',
        },
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Route optimization API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Route optimization failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
