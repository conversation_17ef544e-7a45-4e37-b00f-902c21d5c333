"""
Optimization utilities for the AI-powered two-wheeler sharing platform
Contains common optimization functions used across the platform
"""

import numpy as np
from typing import List, Dict, Tuple, Optional
import math

def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """
    Calculate the great circle distance between two points on Earth
    
    Args:
        lat1, lon1: Latitude and longitude of first point
        lat2, lon2: Latitude and longitude of second point
    
    Returns:
        Distance in kilometers
    """
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    
    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # Radius of Earth in kilometers
    r = 6371
    
    return c * r

def optimize_route_efficiency(waypoints: List[Tuple[float, float]]) -> List[int]:
    """
    Optimize route efficiency using nearest neighbor algorithm
    
    Args:
        waypoints: List of (latitude, longitude) tuples
    
    Returns:
        Optimized order of waypoint indices
    """
    if len(waypoints) <= 2:
        return list(range(len(waypoints)))
    
    # Start from first waypoint
    unvisited = set(range(1, len(waypoints)))
    route = [0]
    current = 0
    
    while unvisited:
        nearest = min(unvisited, 
                     key=lambda x: calculate_distance(
                         waypoints[current][0], waypoints[current][1],
                         waypoints[x][0], waypoints[x][1]
                     ))
        route.append(nearest)
        unvisited.remove(nearest)
        current = nearest
    
    return route

def calculate_carbon_footprint(distance_km: float, vehicle_type: str = "electric") -> float:
    """
    Calculate carbon footprint for a journey
    
    Args:
        distance_km: Distance in kilometers
        vehicle_type: Type of vehicle (electric, petrol, hybrid)
    
    Returns:
        Carbon footprint in kg CO2
    """
    # Carbon emission factors (kg CO2 per km)
    emission_factors = {
        "electric": 0.02,  # Assuming renewable energy
        "petrol": 0.12,
        "hybrid": 0.08,
        "diesel": 0.15
    }
    
    factor = emission_factors.get(vehicle_type, 0.02)
    return distance_km * factor

def optimize_fleet_distribution(demand_points: List[Dict], 
                               available_vehicles: List[Dict]) -> Dict:
    """
    Optimize fleet distribution based on demand predictions
    
    Args:
        demand_points: List of demand locations with predicted demand
        available_vehicles: List of available vehicles with locations
    
    Returns:
        Optimization results with vehicle assignments
    """
    assignments = {}
    total_efficiency = 0
    
    for vehicle in available_vehicles:
        best_demand_point = None
        best_score = float('inf')
        
        for demand_point in demand_points:
            distance = calculate_distance(
                vehicle['latitude'], vehicle['longitude'],
                demand_point['latitude'], demand_point['longitude']
            )
            
            # Score based on distance and demand level
            score = distance / max(demand_point.get('predicted_demand', 1), 1)
            
            if score < best_score:
                best_score = score
                best_demand_point = demand_point
        
        if best_demand_point:
            assignments[vehicle['id']] = {
                'target_location': best_demand_point,
                'distance_to_move': calculate_distance(
                    vehicle['latitude'], vehicle['longitude'],
                    best_demand_point['latitude'], best_demand_point['longitude']
                ),
                'efficiency_score': 1 / best_score if best_score > 0 else 1
            }
            total_efficiency += assignments[vehicle['id']]['efficiency_score']
    
    return {
        'assignments': assignments,
        'total_efficiency': total_efficiency,
        'average_efficiency': total_efficiency / len(assignments) if assignments else 0
    }

def calculate_surge_pricing(base_price: float, demand_ratio: float, 
                           supply_ratio: float, weather_factor: float = 1.0) -> Dict:
    """
    Calculate surge pricing based on demand/supply dynamics
    
    Args:
        base_price: Base price for the service
        demand_ratio: Current demand vs average (1.0 = average)
        supply_ratio: Current supply vs average (1.0 = average)
        weather_factor: Weather impact factor (1.0 = normal)
    
    Returns:
        Pricing information including surge multiplier
    """
    # Calculate surge multiplier
    demand_supply_ratio = demand_ratio / max(supply_ratio, 0.1)
    surge_multiplier = min(max(demand_supply_ratio * weather_factor, 0.8), 3.0)
    
    # Apply surge pricing
    final_price = base_price * surge_multiplier
    
    return {
        'base_price': base_price,
        'surge_multiplier': surge_multiplier,
        'final_price': final_price,
        'demand_ratio': demand_ratio,
        'supply_ratio': supply_ratio,
        'weather_factor': weather_factor
    }

def optimize_battery_usage(current_charge: float, distance_to_destination: float,
                          charging_stations: List[Dict]) -> Dict:
    """
    Optimize battery usage and charging strategy
    
    Args:
        current_charge: Current battery percentage (0-100)
        distance_to_destination: Distance to destination in km
        charging_stations: List of available charging stations
    
    Returns:
        Optimization strategy for battery usage
    """
    # Estimate battery consumption (simplified model)
    consumption_per_km = 2.0  # Percentage per km
    required_charge = distance_to_destination * consumption_per_km
    
    strategy = {
        'current_charge': current_charge,
        'required_charge': required_charge,
        'sufficient_charge': current_charge >= required_charge,
        'recommended_action': 'proceed'
    }
    
    if not strategy['sufficient_charge']:
        # Find nearest charging station
        if charging_stations:
            nearest_station = min(charging_stations,
                                key=lambda x: calculate_distance(
                                    x['latitude'], x['longitude'],
                                    0, 0  # Would use current location
                                ))
            
            strategy.update({
                'recommended_action': 'charge',
                'nearest_charging_station': nearest_station,
                'charge_needed': required_charge - current_charge + 10  # 10% buffer
            })
        else:
            strategy['recommended_action'] = 'find_alternative'
    
    return strategy

def calculate_eta_with_traffic(distance_km: float, base_speed_kmh: float,
                              traffic_factor: float = 1.0) -> Dict:
    """
    Calculate estimated time of arrival considering traffic
    
    Args:
        distance_km: Distance in kilometers
        base_speed_kmh: Base speed in km/h
        traffic_factor: Traffic impact factor (1.0 = normal, >1.0 = slower)
    
    Returns:
        ETA information
    """
    adjusted_speed = base_speed_kmh / traffic_factor
    eta_hours = distance_km / adjusted_speed
    eta_minutes = eta_hours * 60
    
    return {
        'distance_km': distance_km,
        'base_speed_kmh': base_speed_kmh,
        'adjusted_speed_kmh': adjusted_speed,
        'traffic_factor': traffic_factor,
        'eta_minutes': eta_minutes,
        'eta_hours': eta_hours
    }
