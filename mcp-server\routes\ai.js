const express = require('express');
const router = express.Router();
const axios = require('axios');
const Joi = require('joi');

const OLLAMA_URL = process.env.OLLAMA_URL || 'http://ollama:11434';

// AI-powered driver assignment
router.post('/assign-driver', async (req, res) => {
  const { rideRequest, availableDrivers } = req.body;
  const logger = req.app.get('logger');

  try {
    const prompt = `
You are an AI assistant for a two-wheeler sharing platform. Analyze the following ride request and available drivers to make the optimal assignment.

Ride Request:
- Pickup: ${rideRequest.pickup}
- Destination: ${rideRequest.destination}
- Distance: ${rideRequest.distance}km
- Requested time: ${rideRequest.requestedTime}
- User rating: ${rideRequest.userRating}
- Special requirements: ${rideRequest.specialRequirements || 'None'}

Available Drivers:
${availableDrivers.map(driver => `
- Driver ID: ${driver.id}
- Name: ${driver.name}
- Current location: ${driver.currentLocation}
- Distance from pickup: ${driver.distanceFromPickup}km
- Rating: ${driver.rating}
- Vehicle type: ${driver.vehicleType}
- Earnings today: ₹${driver.earningsToday}
- Rides completed today: ${driver.ridesCompletedToday}
`).join('')}

Consider these factors for optimal assignment:
1. Distance from pickup location (closer is better)
2. Driver rating and reliability
3. Vehicle type suitability
4. Driver's earnings distribution (help lower-earning drivers)
5. Driver availability and workload

Respond with JSON format:
{
  "recommendedDriverId": "driver_id",
  "reasoning": "explanation of choice",
  "confidence": 0.95,
  "alternativeOptions": ["driver_id_2", "driver_id_3"],
  "estimatedPickupTime": "minutes",
  "estimatedEarnings": "amount_in_rupees"
}
`;

    const response = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: 'llama2',
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.3,
        top_p: 0.9
      }
    });

    let aiResponse;
    try {
      // Extract JSON from AI response
      const responseText = response.data.response;
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      aiResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      // Fallback to simple assignment if AI response parsing fails
      logger.warn('AI response parsing failed, using fallback assignment');
      const closestDriver = availableDrivers.reduce((closest, driver) => 
        driver.distanceFromPickup < closest.distanceFromPickup ? driver : closest
      );
      
      aiResponse = {
        recommendedDriverId: closestDriver.id,
        reasoning: "Assigned to closest available driver (fallback)",
        confidence: 0.7,
        alternativeOptions: availableDrivers.slice(1, 3).map(d => d.id),
        estimatedPickupTime: Math.round(closestDriver.distanceFromPickup * 2),
        estimatedEarnings: Math.round(rideRequest.distance * 3)
      };
    }

    logger.info(`AI recommended driver ${aiResponse.recommendedDriverId} for ride request`);

    res.json({
      success: true,
      assignment: aiResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('AI driver assignment error:', error);
    res.status(500).json({ error: error.message });
  }
});

// AI-powered route optimization with real-time traffic
router.post('/optimize-route', async (req, res) => {
  const { startLocation, endLocation, waypoints, preferences } = req.body;
  const logger = req.app.get('logger');

  try {
    // Get real-time traffic data
    const trafficData = await getTrafficData(startLocation, endLocation);

    // Get weather data for route optimization
    const weatherData = await getWeatherData(startLocation);

    const prompt = `
You are an AI route optimization assistant for a two-wheeler sharing platform. Optimize the route considering real-time traffic, safety, and efficiency.

Route Details:
- Start: ${startLocation}
- End: ${endLocation}
- Waypoints: ${waypoints ? waypoints.join(', ') : 'None'}
- User preferences: ${JSON.stringify(preferences)}
- Current traffic: ${JSON.stringify(trafficData)}
- Weather conditions: ${JSON.stringify(weatherData)}

Consider these factors:
1. Real-time traffic conditions and congestion
2. Road safety for two-wheelers in current weather
3. Fuel efficiency and optimal speed
4. Construction zones and road closures
5. Two-wheeler friendly routes

Provide route recommendations in JSON format:
{
  "primaryRoute": {
    "description": "route description with traffic considerations",
    "estimatedTime": "minutes",
    "estimatedDistance": "kilometers",
    "safetyRating": 0.9,
    "trafficLevel": "low/medium/high",
    "fuelEfficiency": "excellent/good/average",
    "weatherSuitability": "excellent/good/poor"
  },
  "alternativeRoutes": [
    {
      "description": "alternative route",
      "estimatedTime": "minutes",
      "estimatedDistance": "kilometers",
      "advantages": ["faster", "safer", "less traffic"],
      "trafficAvoidance": true
    }
  ],
  "trafficAlerts": ["Heavy traffic on Route A", "Construction on Route B"],
  "safetyRecommendations": ["wear helmet", "reduce speed in rain"],
  "weatherConsiderations": "current weather impact on route safety"
}
`;

    const response = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: 'llama2',
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.3,
        top_p: 0.9
      }
    });

    let aiResponse;
    try {
      const responseText = response.data.response;
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      aiResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      // Enhanced fallback with traffic consideration
      const trafficMultiplier = trafficData.congestionLevel === 'high' ? 1.5 :
                               trafficData.congestionLevel === 'medium' ? 1.2 : 1.0;

      aiResponse = {
        primaryRoute: {
          description: `Optimized route from ${startLocation} to ${endLocation} (Traffic: ${trafficData.congestionLevel})`,
          estimatedTime: Math.round(25 * trafficMultiplier).toString(),
          estimatedDistance: "12",
          safetyRating: weatherData.condition === 'rain' ? 0.7 : 0.8,
          trafficLevel: trafficData.congestionLevel,
          fuelEfficiency: trafficData.congestionLevel === 'low' ? 'excellent' : 'good',
          weatherSuitability: weatherData.condition === 'clear' ? 'excellent' : 'good'
        },
        alternativeRoutes: [
          {
            description: "Alternative route avoiding main roads",
            estimatedTime: Math.round(30 * trafficMultiplier).toString(),
            estimatedDistance: "14",
            advantages: ["less traffic", "safer for two-wheelers"],
            trafficAvoidance: true
          }
        ],
        trafficAlerts: trafficData.alerts || [],
        safetyRecommendations: [
          "Wear helmet and protective gear",
          weatherData.condition === 'rain' ? "Reduce speed and increase following distance" : "Follow traffic rules",
          "Use designated two-wheeler lanes where available"
        ],
        weatherConsiderations: `Current weather: ${weatherData.condition}. ${weatherData.condition === 'rain' ? 'Exercise extra caution on wet roads.' : 'Good conditions for riding.'}`
      };
    }

    // Enhance with ML-based route optimization
    const mlOptimization = await optimizeRouteWithML(startLocation, endLocation, trafficData, weatherData);
    if (mlOptimization) {
      aiResponse.mlInsights = mlOptimization;
    }

    logger.info(`AI optimized route from ${startLocation} to ${endLocation} with traffic data`);

    res.json({
      success: true,
      optimization: aiResponse,
      trafficData: trafficData,
      weatherData: weatherData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('AI route optimization error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Helper function to get traffic data
async function getTrafficData(startLocation, endLocation) {
  try {
    // In production, this would call Google Maps Traffic API or HERE API
    const googleMapsApiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (googleMapsApiKey && googleMapsApiKey !== 'sample_key_12345') {
      const response = await axios.get('https://maps.googleapis.com/maps/api/directions/json', {
        params: {
          origin: startLocation,
          destination: endLocation,
          departure_time: 'now',
          traffic_model: 'best_guess',
          key: googleMapsApiKey
        }
      });

      if (response.data.status === 'OK') {
        const route = response.data.routes[0];
        const leg = route.legs[0];

        return {
          duration: leg.duration.text,
          durationInTraffic: leg.duration_in_traffic?.text || leg.duration.text,
          distance: leg.distance.text,
          congestionLevel: calculateCongestionLevel(leg.duration.value, leg.duration_in_traffic?.value),
          alerts: extractTrafficAlerts(route),
          polyline: route.overview_polyline.points
        };
      }
    }

    // Fallback mock traffic data
    const currentHour = new Date().getHours();
    const isPeakHour = (currentHour >= 8 && currentHour <= 10) || (currentHour >= 17 && currentHour <= 20);

    return {
      duration: "25 mins",
      durationInTraffic: isPeakHour ? "35 mins" : "25 mins",
      distance: "12.5 km",
      congestionLevel: isPeakHour ? 'high' : 'medium',
      alerts: isPeakHour ? ["Heavy traffic expected on main routes"] : [],
      polyline: "mock_polyline_data"
    };

  } catch (error) {
    logger.error('Traffic data fetch error:', error);
    return {
      duration: "25 mins",
      durationInTraffic: "30 mins",
      distance: "12 km",
      congestionLevel: 'medium',
      alerts: [],
      polyline: ""
    };
  }
}

// Helper function to get weather data
async function getWeatherData(location) {
  try {
    const apiKey = process.env.OPENWEATHERMAP_API_KEY;

    if (apiKey && apiKey !== 'sample_key_12345') {
      const response = await axios.get('http://api.openweathermap.org/data/2.5/weather', {
        params: {
          q: location,
          appid: apiKey,
          units: 'metric'
        }
      });

      return {
        condition: response.data.weather[0].main.toLowerCase(),
        temperature: response.data.main.temp,
        humidity: response.data.main.humidity,
        windSpeed: response.data.wind.speed,
        visibility: response.data.visibility / 1000 // Convert to km
      };
    }

    // Fallback mock weather data
    return {
      condition: 'clear',
      temperature: 28,
      humidity: 65,
      windSpeed: 5,
      visibility: 10
    };

  } catch (error) {
    logger.error('Weather data fetch error:', error);
    return {
      condition: 'clear',
      temperature: 28,
      humidity: 65,
      windSpeed: 5,
      visibility: 10
    };
  }
}

// Helper function to calculate congestion level
function calculateCongestionLevel(normalDuration, trafficDuration) {
  if (!trafficDuration) return 'medium';

  const ratio = trafficDuration / normalDuration;
  if (ratio > 1.5) return 'high';
  if (ratio > 1.2) return 'medium';
  return 'low';
}

// Helper function to extract traffic alerts
function extractTrafficAlerts(route) {
  const alerts = [];

  // Extract warnings from route
  if (route.warnings && route.warnings.length > 0) {
    alerts.push(...route.warnings);
  }

  // Check for construction or incidents in route steps
  route.legs.forEach(leg => {
    leg.steps.forEach(step => {
      if (step.html_instructions.includes('construction') ||
          step.html_instructions.includes('closure')) {
        alerts.push(`Construction detected: ${step.html_instructions}`);
      }
    });
  });

  return alerts;
}

// Helper function for ML-based route optimization
async function optimizeRouteWithML(startLocation, endLocation, trafficData, weatherData) {
  try {
    // Call ML service for route optimization insights
    const response = await axios.post('http://ml-service:8081/optimize/route', {
      startLocation,
      endLocation,
      trafficData,
      weatherData,
      vehicleType: 'two_wheeler'
    });

    return response.data;
  } catch (error) {
    logger.error('ML route optimization error:', error);
    return null;
  }
}

// AI-powered demand prediction
router.post('/predict-demand', async (req, res) => {
  const { location, timeOfDay, dayOfWeek, weather, historicalData } = req.body;
  const logger = req.app.get('logger');

  try {
    const prompt = `
Analyze demand patterns for two-wheeler sharing service and predict demand levels.

Current Context:
- Location: ${location}
- Time: ${timeOfDay}
- Day: ${dayOfWeek}
- Weather: ${weather}
- Historical data: ${JSON.stringify(historicalData)}

Predict demand and provide insights in JSON format:
{
  "demandLevel": "low/medium/high",
  "confidence": 0.85,
  "peakHours": ["8:00-10:00", "18:00-20:00"],
  "factors": ["office hours", "weather", "events"],
  "recommendations": {
    "pricing": "increase/decrease/maintain",
    "driverIncentives": "offer bonus for availability",
    "userPromotions": "discount during low demand"
  },
  "nextHourPrediction": "medium",
  "suggestedActions": ["deploy more drivers", "send notifications"]
}
`;

    const response = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: 'llama2',
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.3,
        top_p: 0.9
      }
    });

    let aiResponse;
    try {
      const responseText = response.data.response;
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      aiResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      // Fallback demand prediction
      const hour = new Date().getHours();
      const isPeakHour = (hour >= 8 && hour <= 10) || (hour >= 18 && hour <= 20);
      
      aiResponse = {
        demandLevel: isPeakHour ? "high" : "medium",
        confidence: 0.7,
        peakHours: ["8:00-10:00", "18:00-20:00"],
        factors: ["time of day", "typical patterns"],
        recommendations: {
          pricing: isPeakHour ? "increase" : "maintain",
          driverIncentives: "standard rates",
          userPromotions: "none"
        },
        nextHourPrediction: "medium",
        suggestedActions: ["monitor closely"]
      };
    }

    logger.info(`AI predicted ${aiResponse.demandLevel} demand for ${location}`);

    res.json({
      success: true,
      prediction: aiResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('AI demand prediction error:', error);
    res.status(500).json({ error: error.message });
  }
});

// AI chat assistant for users
router.post('/chat', async (req, res) => {
  const { message, userId, context } = req.body;
  const logger = req.app.get('logger');

  try {
    const prompt = `
You are a helpful AI assistant for a two-wheeler sharing platform. Help users with their queries about rides, bookings, payments, and general support.

User message: "${message}"
User context: ${JSON.stringify(context)}

Provide helpful, concise responses. If the query requires specific actions (like booking a ride), guide the user through the process.

Respond in JSON format:
{
  "response": "your helpful response",
  "suggestedActions": ["book_ride", "check_status", "contact_support"],
  "quickReplies": ["Yes", "No", "Tell me more"],
  "requiresHumanSupport": false
}
`;

    const response = await axios.post(`${OLLAMA_URL}/api/generate`, {
      model: 'llama2',
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.6,
        top_p: 0.9
      }
    });

    let aiResponse;
    try {
      const responseText = response.data.response;
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      aiResponse = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      aiResponse = {
        response: "I'm here to help! Could you please rephrase your question?",
        suggestedActions: ["contact_support"],
        quickReplies: ["Book a ride", "Check my rides", "Help"],
        requiresHumanSupport: false
      };
    }

    logger.info(`AI chat response provided for user ${userId}`);

    res.json({
      success: true,
      chat: aiResponse,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('AI chat error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Health check for Ollama service
router.get('/health', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_URL}/api/tags`);
    res.json({
      status: 'healthy',
      ollama: 'connected',
      models: response.data.models || []
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      ollama: 'disconnected',
      error: error.message
    });
  }
});

module.exports = router;
