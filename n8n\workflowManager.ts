import { N8N_CONFIG, WorkflowTrigger, WorkflowData, NotificationWorkflowData, RideWorkflowData, PaymentWorkflowData } from './config';

class N8nWorkflowManager {
  private baseUrl: string;
  private apiKey: string;
  private retryAttempts: number;
  private retryDelay: number;

  constructor() {
    this.baseUrl = N8N_CONFIG.baseUrl;
    this.apiKey = N8N_CONFIG.apiKey;
    this.retryAttempts = N8N_CONFIG.settings.retryAttempts;
    this.retryDelay = N8N_CONFIG.settings.retryDelay;
  }

  /**
   * Trigger a workflow via webhook
   */
  async triggerWorkflow(webhookPath: string, data: WorkflowData): Promise<boolean> {
    const url = `${this.baseUrl}${webhookPath}`;
    
    try {
      const response = await this.makeRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` }),
        },
        body: JSON.stringify({
          ...data,
          timestamp: Date.now(),
        }),
      });

      if (N8N_CONFIG.settings.enableLogging) {
        console.log(`n8n workflow triggered: ${webhookPath}`, { 
          trigger: data.trigger, 
          success: response.ok 
        });
      }

      return response.ok;
    } catch (error) {
      console.error(`Failed to trigger n8n workflow: ${webhookPath}`, error);
      return false;
    }
  }

  /**
   * Trigger ride booking workflow
   */
  async triggerRideBookingFlow(rideData: RideWorkflowData): Promise<boolean> {
    return this.triggerWorkflow(N8N_CONFIG.webhooks.rideRequest, rideData);
  }

  /**
   * Trigger driver notification workflow
   */
  async triggerDriverNotification(notificationData: NotificationWorkflowData): Promise<boolean> {
    return this.triggerWorkflow(N8N_CONFIG.webhooks.driverAssignment, notificationData);
  }

  /**
   * Trigger ride status update workflow
   */
  async triggerStatusUpdate(rideData: RideWorkflowData): Promise<boolean> {
    return this.triggerWorkflow(N8N_CONFIG.webhooks.statusUpdate, rideData);
  }

  /**
   * Trigger payment processing workflow
   */
  async triggerPaymentProcessing(paymentData: PaymentWorkflowData): Promise<boolean> {
    return this.triggerWorkflow(N8N_CONFIG.webhooks.paymentProcessing, paymentData);
  }

  /**
   * Trigger emergency alert workflow
   */
  async triggerEmergencyAlert(emergencyData: WorkflowData): Promise<boolean> {
    return this.triggerWorkflow(N8N_CONFIG.webhooks.emergency, {
      ...emergencyData,
      metadata: {
        ...emergencyData.metadata,
        priority: 'critical',
      },
    });
  }

  /**
   * Send notification through n8n workflow
   */
  async sendNotification(notificationData: NotificationWorkflowData): Promise<boolean> {
    return this.triggerWorkflow(N8N_CONFIG.webhooks.notification, notificationData);
  }

  /**
   * Automated ride assignment workflow
   */
  async automateRideAssignment(rideId: string, availableDrivers: string[]): Promise<boolean> {
    const workflowData: WorkflowData = {
      trigger: WorkflowTrigger.RIDE_REQUESTED,
      timestamp: Date.now(),
      data: {
        rideId,
        availableDrivers,
        assignmentStrategy: 'nearest_first', // or 'rating_based', 'round_robin'
      },
      metadata: {
        rideId,
        priority: 'high',
      },
    };

    return this.triggerWorkflow(N8N_CONFIG.webhooks.driverAssignment, workflowData);
  }

  /**
   * Automated payment processing workflow
   */
  async automatePaymentProcessing(rideId: string, amount: number, paymentMethod: string): Promise<boolean> {
    const paymentData: PaymentWorkflowData = {
      trigger: WorkflowTrigger.PAYMENT_PROCESSED,
      timestamp: Date.now(),
      data: {},
      payment: {
        rideId,
        amount,
        currency: 'INR',
        method: paymentMethod,
        status: 'pending',
      },
      rider: {
        id: '', // Will be populated by the workflow
        email: '',
      },
      metadata: {
        rideId,
        priority: 'high',
      },
    };

    return this.triggerPaymentProcessing(paymentData);
  }

  /**
   * Daily analytics and reporting workflow
   */
  async triggerDailyReports(): Promise<boolean> {
    const reportData: WorkflowData = {
      trigger: WorkflowTrigger.DAILY_REPORT,
      timestamp: Date.now(),
      data: {
        reportType: 'daily_summary',
        date: new Date().toISOString().split('T')[0],
      },
      metadata: {
        priority: 'low',
      },
    };

    return this.triggerWorkflow('/webhook/daily-reports', reportData);
  }

  /**
   * Driver performance monitoring workflow
   */
  async monitorDriverPerformance(driverId: string, metrics: any): Promise<boolean> {
    const performanceData: WorkflowData = {
      trigger: WorkflowTrigger.DRIVER_OFFLINE,
      timestamp: Date.now(),
      data: {
        driverId,
        metrics,
        thresholds: {
          minRating: 4.0,
          maxCancellationRate: 0.1,
          minAcceptanceRate: 0.8,
        },
      },
      metadata: {
        driverId,
        priority: 'medium',
      },
    };

    return this.triggerWorkflow('/webhook/driver-monitoring', performanceData);
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequest(url: string, options: RequestInit, attempt = 1): Promise<Response> {
    try {
      const response = await fetch(url, {
        ...options,
        signal: AbortSignal.timeout(N8N_CONFIG.settings.timeout),
      });

      if (!response.ok && attempt < this.retryAttempts) {
        await this.delay(this.retryDelay);
        return this.makeRequest(url, options, attempt + 1);
      }

      return response;
    } catch (error) {
      if (attempt < this.retryAttempts) {
        await this.delay(this.retryDelay);
        return this.makeRequest(url, options, attempt + 1);
      }
      throw error;
    }
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Health check for n8n instance
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/healthz`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch (error) {
      console.error('n8n health check failed:', error);
      return false;
    }
  }

  /**
   * Get workflow execution status
   */
  async getWorkflowStatus(workflowId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/workflows/${workflowId}/executions`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
      });
      
      if (response.ok) {
        return await response.json();
      }
      return null;
    } catch (error) {
      console.error('Failed to get workflow status:', error);
      return null;
    }
  }
}

// Export singleton instance
export const workflowManager = new N8nWorkflowManager();
export default workflowManager;
