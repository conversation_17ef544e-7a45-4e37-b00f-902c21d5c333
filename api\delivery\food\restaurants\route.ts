import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { foodDeliveryService } from '@/lib/delivery/foodDeliveryService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const latitude = parseFloat(searchParams.get('latitude') || '0');
    const longitude = parseFloat(searchParams.get('longitude') || '0');
    const cuisine = searchParams.get('cuisine')?.split(',').filter(Boolean);
    const priceRange = searchParams.get('priceRange') as 'budget' | 'mid-range' | 'premium' | undefined;
    const rating = parseFloat(searchParams.get('rating') || '0');
    const deliveryTime = parseInt(searchParams.get('deliveryTime') || '0');
    const features = searchParams.get('features')?.split(',').filter(Boolean);
    const search = searchParams.get('search');

    // Validate coordinates
    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: 'Valid latitude and longitude are required' },
        { status: 400 }
      );
    }

    const location = { latitude, longitude };

    let restaurants;

    if (search) {
      // Search restaurants and dishes
      const searchResults = await foodDeliveryService.searchFood(search, location, {
        cuisine,
        priceRange,
      });
      
      restaurants = searchResults.restaurants;
      
      // Include dishes in response for search
      const response = {
        success: true,
        data: {
          restaurants: restaurants.map(restaurant => ({
            id: restaurant.id,
            name: restaurant.name,
            description: restaurant.description,
            cuisine: restaurant.cuisine,
            location: restaurant.location,
            rating: restaurant.rating,
            totalOrders: restaurant.totalOrders,
            averagePreparationTime: restaurant.averagePreparationTime,
            deliveryRadius: restaurant.deliveryRadius,
            minimumOrderValue: restaurant.minimumOrderValue,
            deliveryFee: restaurant.deliveryFee,
            packagingFee: restaurant.packagingFee,
            features: restaurant.features,
            images: restaurant.images,
            tags: restaurant.tags,
            distance: (restaurant as any).distance,
            estimatedDeliveryTime: (restaurant as any).estimatedDeliveryTime,
          })),
          dishes: searchResults.dishes.map(dish => ({
            id: dish.id,
            name: dish.name,
            description: dish.description,
            category: dish.category,
            price: dish.price,
            originalPrice: dish.originalPrice,
            preparationTime: dish.preparationTime,
            isVegetarian: dish.isVegetarian,
            isVegan: dish.isVegan,
            isSpicy: dish.isSpicy,
            images: dish.images,
            tags: dish.tags,
            restaurant: {
              id: dish.restaurant.id,
              name: dish.restaurant.name,
              rating: dish.restaurant.rating,
              deliveryFee: dish.restaurant.deliveryFee,
              minimumOrderValue: dish.restaurant.minimumOrderValue,
            },
          })),
          searchQuery: search,
          resultType: 'search',
        },
        metadata: {
          location,
          filters: { cuisine, priceRange, rating, deliveryTime, features },
          timestamp: Date.now(),
        },
      };

      return NextResponse.json(response);
    } else {
      // Get nearby restaurants
      const filters = {
        cuisine,
        priceRange,
        rating: rating > 0 ? rating : undefined,
        deliveryTime: deliveryTime > 0 ? deliveryTime : undefined,
        features,
      };

      restaurants = await foodDeliveryService.getNearbyRestaurants(location, filters);
    }

    // Group restaurants by categories for better UX
    const categories = {
      popular: restaurants.filter(r => r.tags.includes('popular')).slice(0, 6),
      fastDelivery: restaurants.filter(r => r.tags.includes('fast-delivery')).slice(0, 6),
      vegetarian: restaurants.filter(r => r.features.hasVegetarianOptions).slice(0, 6),
      budget: restaurants.filter(r => r.tags.includes('budget-friendly')).slice(0, 6),
      premium: restaurants.filter(r => r.tags.includes('fine-dining')).slice(0, 6),
    };

    const response = {
      success: true,
      data: {
        restaurants: restaurants.map(restaurant => ({
          id: restaurant.id,
          name: restaurant.name,
          description: restaurant.description,
          cuisine: restaurant.cuisine,
          location: restaurant.location,
          rating: restaurant.rating,
          totalOrders: restaurant.totalOrders,
          averagePreparationTime: restaurant.averagePreparationTime,
          deliveryRadius: restaurant.deliveryRadius,
          minimumOrderValue: restaurant.minimumOrderValue,
          deliveryFee: restaurant.deliveryFee,
          packagingFee: restaurant.packagingFee,
          features: restaurant.features,
          images: restaurant.images,
          tags: restaurant.tags,
          distance: (restaurant as any).distance,
          estimatedDeliveryTime: (restaurant as any).estimatedDeliveryTime,
        })),
        categories: {
          popular: categories.popular.map(r => ({ id: r.id, name: r.name, rating: r.rating, images: r.images })),
          fastDelivery: categories.fastDelivery.map(r => ({ id: r.id, name: r.name, estimatedDeliveryTime: (r as any).estimatedDeliveryTime, images: r.images })),
          vegetarian: categories.vegetarian.map(r => ({ id: r.id, name: r.name, rating: r.rating, images: r.images })),
          budget: categories.budget.map(r => ({ id: r.id, name: r.name, minimumOrderValue: r.minimumOrderValue, images: r.images })),
          premium: categories.premium.map(r => ({ id: r.id, name: r.name, rating: r.rating, images: r.images })),
        },
        summary: {
          total: restaurants.length,
          averageRating: restaurants.length > 0 
            ? Math.round((restaurants.reduce((sum, r) => sum + r.rating, 0) / restaurants.length) * 10) / 10
            : 0,
          averageDeliveryTime: restaurants.length > 0
            ? Math.round(restaurants.reduce((sum, r) => sum + (r as any).estimatedDeliveryTime, 0) / restaurants.length)
            : 0,
          cuisineTypes: [...new Set(restaurants.flatMap(r => r.cuisine))],
        },
        filters: {
          availableCuisines: ['Indian', 'Chinese', 'Italian', 'Mexican', 'Thai', 'Continental', 'Fast Food', 'Pizza', 'Burger'],
          priceRanges: ['budget', 'mid-range', 'premium'],
          features: ['hasVegetarianOptions', 'hasVeganOptions', 'acceptsCashOnDelivery', 'offersContactlessDelivery', 'hasLiveTracking'],
          sortOptions: ['rating', 'deliveryTime', 'distance', 'popularity'],
        },
      },
      metadata: {
        location,
        filters: { cuisine, priceRange, rating, deliveryTime, features },
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get restaurants API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch restaurants',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
