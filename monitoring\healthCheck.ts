// System Health Monitoring Service
import connectDB from '../mongodb';
import { apmService } from './apm';
import { errorTrackingService } from './errorTracking';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  timestamp: number;
  details?: any;
  error?: string;
}

export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  checks: HealthCheckResult[];
  uptime: number;
  version: string;
  environment: string;
}

class HealthMonitoringService {
  private healthChecks: Map<string, () => Promise<HealthCheckResult>> = new Map();
  private lastHealthCheck: SystemHealth | null = null;
  private startTime: number = Date.now();

  constructor() {
    this.registerDefaultHealthChecks();
    this.startPeriodicHealthChecks();
  }

  /**
   * Register default health checks
   */
  private registerDefaultHealthChecks() {
    this.registerHealthCheck('database', this.checkDatabase.bind(this));
    this.registerHealthCheck('memory', this.checkMemory.bind(this));
    this.registerHealthCheck('disk', this.checkDisk.bind(this));
    this.registerHealthCheck('external_apis', this.checkExternalAPIs.bind(this));
    this.registerHealthCheck('payment_gateway', this.checkPaymentGateway.bind(this));
  }

  /**
   * Start periodic health checks
   */
  private startPeriodicHealthChecks() {
    // Run health checks every 30 seconds
    setInterval(async () => {
      try {
        await this.runHealthChecks();
      } catch (error) {
        console.error('Health check failed:', error);
      }
    }, 30000);
  }

  /**
   * Register a custom health check
   */
  registerHealthCheck(name: string, checkFunction: () => Promise<HealthCheckResult>) {
    this.healthChecks.set(name, checkFunction);
  }

  /**
   * Run all health checks
   */
  async runHealthChecks(): Promise<SystemHealth> {
    const checks: HealthCheckResult[] = [];
    
    for (const [name, checkFunction] of this.healthChecks) {
      try {
        const result = await apmService.timeFunction(
          `health_check_${name}`,
          checkFunction
        );
        checks.push(result);
      } catch (error) {
        checks.push({
          service: name,
          status: 'unhealthy',
          responseTime: 0,
          timestamp: Date.now(),
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Determine overall health
    const overallStatus = this.determineOverallHealth(checks);
    
    const systemHealth: SystemHealth = {
      overall: overallStatus,
      timestamp: Date.now(),
      checks,
      uptime: Date.now() - this.startTime,
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    };

    this.lastHealthCheck = systemHealth;

    // Record health metrics
    apmService.recordMetric({
      name: 'system_health_overall',
      value: overallStatus === 'healthy' ? 1 : overallStatus === 'degraded' ? 0.5 : 0,
      unit: 'score',
      timestamp: Date.now(),
      tags: { status: overallStatus },
    });

    // Alert on unhealthy status
    if (overallStatus === 'unhealthy') {
      errorTrackingService.captureMessage(
        `System health is unhealthy: ${checks.filter(c => c.status === 'unhealthy').map(c => c.service).join(', ')}`,
        'error',
        { healthChecks: checks }
      );
    }

    return systemHealth;
  }

  /**
   * Get current system health
   */
  async getCurrentHealth(): Promise<SystemHealth> {
    if (!this.lastHealthCheck || Date.now() - this.lastHealthCheck.timestamp > 60000) {
      // Run fresh health check if last one is older than 1 minute
      return await this.runHealthChecks();
    }
    return this.lastHealthCheck;
  }

  /**
   * Check database connectivity
   */
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      await connectDB();
      
      // Test a simple query
      const mongoose = require('mongoose');
      await mongoose.connection.db.admin().ping();
      
      const responseTime = Date.now() - startTime;
      
      return {
        service: 'database',
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        timestamp: Date.now(),
        details: {
          connectionState: mongoose.connection.readyState,
          host: mongoose.connection.host,
          port: mongoose.connection.port,
        },
      };
    } catch (error) {
      return {
        service: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Database connection failed',
      };
    }
  }

  /**
   * Check memory usage
   */
  private async checkMemory(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const memUsage = process.memoryUsage();
      const totalMemory = memUsage.rss + memUsage.heapUsed + memUsage.external;
      const memoryUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (memoryUsagePercent > 90) {
        status = 'unhealthy';
      } else if (memoryUsagePercent > 75) {
        status = 'degraded';
      }
      
      return {
        service: 'memory',
        status,
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        details: {
          rss: Math.round(memUsage.rss / 1024 / 1024),
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
          external: Math.round(memUsage.external / 1024 / 1024),
          usagePercent: Math.round(memoryUsagePercent),
        },
      };
    } catch (error) {
      return {
        service: 'memory',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Memory check failed',
      };
    }
  }

  /**
   * Check disk usage (simplified for Node.js)
   */
  private async checkDisk(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // For a more comprehensive disk check, you'd use a library like 'node-disk-info'
      // This is a simplified version
      const fs = require('fs').promises;
      const stats = await fs.stat(process.cwd());
      
      return {
        service: 'disk',
        status: 'healthy', // Simplified - always healthy for now
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        details: {
          accessible: true,
          path: process.cwd(),
        },
      };
    } catch (error) {
      return {
        service: 'disk',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Disk check failed',
      };
    }
  }

  /**
   * Check external APIs
   */
  private async checkExternalAPIs(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Check if we can reach external services
      const checks = [];
      
      // Check Google (as a general internet connectivity test)
      try {
        const response = await fetch('https://www.google.com', { 
          method: 'HEAD',
          signal: AbortSignal.timeout(5000)
        });
        checks.push({ service: 'internet', status: response.ok });
      } catch {
        checks.push({ service: 'internet', status: false });
      }

      const failedChecks = checks.filter(c => !c.status);
      const status = failedChecks.length === 0 ? 'healthy' : 
                   failedChecks.length < checks.length ? 'degraded' : 'unhealthy';
      
      return {
        service: 'external_apis',
        status,
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        details: {
          checks,
          failedCount: failedChecks.length,
          totalCount: checks.length,
        },
      };
    } catch (error) {
      return {
        service: 'external_apis',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'External API check failed',
      };
    }
  }

  /**
   * Check payment gateway connectivity
   */
  private async checkPaymentGateway(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // This would check Razorpay API connectivity
      // For now, we'll just check if the credentials are configured
      const hasCredentials = !!(process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET);
      
      return {
        service: 'payment_gateway',
        status: hasCredentials ? 'healthy' : 'degraded',
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        details: {
          configured: hasCredentials,
          provider: 'razorpay',
        },
      };
    } catch (error) {
      return {
        service: 'payment_gateway',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: Date.now(),
        error: error instanceof Error ? error.message : 'Payment gateway check failed',
      };
    }
  }

  /**
   * Determine overall system health based on individual checks
   */
  private determineOverallHealth(checks: HealthCheckResult[]): 'healthy' | 'degraded' | 'unhealthy' {
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;
    const degradedCount = checks.filter(c => c.status === 'degraded').length;
    
    if (unhealthyCount > 0) {
      return 'unhealthy';
    } else if (degradedCount > 0) {
      return 'degraded';
    } else {
      return 'healthy';
    }
  }

  /**
   * Get health check history
   */
  getHealthHistory(): SystemHealth | null {
    return this.lastHealthCheck;
  }

  /**
   * Get uptime in seconds
   */
  getUptime(): number {
    return Math.floor((Date.now() - this.startTime) / 1000);
  }
}

// Export singleton instance
export const healthMonitoringService = new HealthMonitoringService();
export default healthMonitoringService;
