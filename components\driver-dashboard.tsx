"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Bell, MapPin, Clock, Star, Wallet, TrendingUp, Navigation } from "lucide-react"
import { io, Socket } from "socket.io-client"

interface Task {
  id: string
  userId: string
  pickup: string
  destination: string
  distance: number
  estimatedEarnings: number
  estimatedPickupTime: number
  priority: 'low' | 'medium' | 'high'
  status: 'assigned' | 'accepted' | 'in_progress' | 'completed'
  assignedAt: string
  userRating: number
}

interface DriverStats {
  todayEarnings: number
  todayRides: number
  weeklyEarnings: number
  rating: number
  totalPoints: number
  level: string
  streak: number
}

export function DriverDashboard({ driverId }: { driverId: string }) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [tasks, setTasks] = useState<Task[]>([])
  const [notifications, setNotifications] = useState<any[]>([])
  const [stats, setStats] = useState<DriverStats>({
    todayEarnings: 0,
    todayRides: 0,
    weeklyEarnings: 0,
    rating: 4.8,
    totalPoints: 1250,
    level: 'Gold',
    streak: 5
  })
  const [isOnline, setIsOnline] = useState(false)

  useEffect(() => {
    // Initialize Socket.io connection
    const newSocket = io(process.env.NEXT_PUBLIC_MCP_SERVER_URL || 'http://localhost:8080')
    setSocket(newSocket)

    // Join driver room for real-time updates
    newSocket.emit('join_driver', driverId)

    // Listen for task assignments
    newSocket.on('task_assigned', (task: Task) => {
      setTasks(prev => [task, ...prev])
      // Show notification
      if ('Notification' in window) {
        new Notification('New Ride Request!', {
          body: `Pickup: ${task.pickup} → ${task.destination}`,
          icon: '/icon-192x192.png'
        })
      }
    })

    // Listen for notifications
    newSocket.on('notification', (notification) => {
      setNotifications(prev => [notification, ...prev.slice(0, 9)])
    })

    // Listen for points updates
    newSocket.on('points_awarded', (pointsData) => {
      setStats(prev => ({
        ...prev,
        totalPoints: pointsData.totalPoints,
        level: pointsData.level
      }))
    })

    return () => {
      newSocket.disconnect()
    }
  }, [driverId])

  const acceptTask = async (taskId: string) => {
    try {
      const response = await fetch('/api/tasks/accept', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ taskId, driverId })
      })

      if (response.ok) {
        setTasks(prev => prev.map(task => 
          task.id === taskId ? { ...task, status: 'accepted' } : task
        ))
      }
    } catch (error) {
      console.error('Error accepting task:', error)
    }
  }

  const startRide = async (taskId: string) => {
    try {
      const response = await fetch('/api/tasks/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ taskId, driverId })
      })

      if (response.ok) {
        setTasks(prev => prev.map(task => 
          task.id === taskId ? { ...task, status: 'in_progress' } : task
        ))
      }
    } catch (error) {
      console.error('Error starting ride:', error)
    }
  }

  const completeRide = async (taskId: string) => {
    try {
      const response = await fetch('/api/tasks/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ taskId, driverId })
      })

      if (response.ok) {
        setTasks(prev => prev.filter(task => task.id !== taskId))
        // Trigger ride completion workflow
        await fetch('/webhook/ride-completed', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            rideData: {
              rideId: taskId,
              driverId,
              driverEarnings: tasks.find(t => t.id === taskId)?.estimatedEarnings || 0,
              rating: 4.8 // This would come from user feedback
            }
          })
        })
      }
    } catch (error) {
      console.error('Error completing ride:', error)
    }
  }

  const toggleOnlineStatus = () => {
    setIsOnline(!isOnline)
    // Update driver availability in backend
    fetch('/api/drivers/availability', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ driverId, isOnline: !isOnline })
    })
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'assigned': return 'bg-blue-500'
      case 'accepted': return 'bg-yellow-500'
      case 'in_progress': return 'bg-green-500'
      case 'completed': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Driver Dashboard</h1>
        <div className="flex items-center gap-4">
          <Button
            onClick={toggleOnlineStatus}
            variant={isOnline ? "default" : "outline"}
            className={isOnline ? "bg-green-600 hover:bg-green-700" : ""}
          >
            {isOnline ? "Online" : "Offline"}
          </Button>
          <div className="relative">
            <Bell className="h-6 w-6" />
            {notifications.length > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center">
                {notifications.length}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Earnings</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.todayEarnings}</div>
            <p className="text-xs text-muted-foreground">
              {stats.todayRides} rides completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rating}</div>
            <p className="text-xs text-muted-foreground">
              Excellent service
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reward Points</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPoints}</div>
            <p className="text-xs text-muted-foreground">
              {stats.level} Level • {stats.streak} day streak
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Progress</CardTitle>
            <Navigation className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.weeklyEarnings}</div>
            <Progress value={65} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              65% of weekly goal
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tasks">Active Tasks</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-4">
          {tasks.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Active Tasks</h3>
                <p className="text-muted-foreground text-center">
                  {isOnline ? "You're online and ready to receive ride requests!" : "Go online to start receiving ride requests"}
                </p>
              </CardContent>
            </Card>
          ) : (
            tasks.map((task) => (
              <Card key={task.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">Ride Request</CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority} priority
                        </Badge>
                        <Badge variant="outline" className={getStatusColor(task.status)}>
                          {task.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">₹{task.estimatedEarnings}</div>
                      <div className="text-sm text-muted-foreground">{task.distance}km</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-green-600" />
                      <span className="font-medium">Pickup:</span>
                      <span>{task.pickup}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-red-600" />
                      <span className="font-medium">Destination:</span>
                      <span>{task.destination}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">ETA to pickup:</span>
                      <span>{task.estimatedPickupTime} minutes</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium">User rating:</span>
                      <span>{task.userRating}/5.0</span>
                    </div>

                    <div className="flex gap-2 pt-4">
                      {task.status === 'assigned' && (
                        <>
                          <Button onClick={() => acceptTask(task.id)} className="flex-1">
                            Accept Ride
                          </Button>
                          <Button variant="outline" className="flex-1">
                            Decline
                          </Button>
                        </>
                      )}
                      {task.status === 'accepted' && (
                        <Button onClick={() => startRide(task.id)} className="w-full">
                          Start Ride
                        </Button>
                      )}
                      {task.status === 'in_progress' && (
                        <Button onClick={() => completeRide(task.id)} className="w-full">
                          Complete Ride
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          {notifications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Bell className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Notifications</h3>
                <p className="text-muted-foreground">You're all caught up!</p>
              </CardContent>
            </Card>
          ) : (
            notifications.map((notification, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-semibold">{notification.title || notification.message}</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        {new Date(notification.timestamp).toLocaleString()}
                      </p>
                    </div>
                    <Badge variant={notification.priority === 'high' ? 'destructive' : 'secondary'}>
                      {notification.priority}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>Weekly Earnings Goal</span>
                    <span>₹{stats.weeklyEarnings} / ₹2000</span>
                  </div>
                  <Progress value={(stats.weeklyEarnings / 2000) * 100} />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>Customer Satisfaction</span>
                    <span>{stats.rating}/5.0</span>
                  </div>
                  <Progress value={(stats.rating / 5) * 100} />
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{stats.streak}</div>
                    <div className="text-sm text-muted-foreground">Day Streak</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{stats.totalPoints}</div>
                    <div className="text-sm text-muted-foreground">Total Points</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
