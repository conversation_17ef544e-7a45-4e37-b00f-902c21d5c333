'use client';

import React, { ReactNode, useEffect, useState, useContext } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  title?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  showCloseButton?: boolean;
  className?: string;
  overlayClassName?: string;
}

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'destructive' | 'warning';
  loading?: boolean;
}

interface AlertDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  actionText?: string;
}

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  children: ReactNode;
  title?: string;
  position?: 'left' | 'right' | 'top' | 'bottom';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

interface ToastProps {
  id: string;
  title?: string;
  description: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Modal Component
export function Modal({
  isOpen,
  onClose,
  children,
  title,
  description,
  size = 'md',
  closeOnOverlayClick = true,
  closeOnEscape = true,
  showCloseButton = true,
  className,
  overlayClassName,
}: ModalProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (!closeOnEscape) return;

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose, closeOnEscape]);

  if (!mounted || !isOpen) return null;

  const sizes = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4',
  };

  const modalContent = (
    <div className={cn('fixed inset-0 z-50 flex items-center justify-center p-4', overlayClassName)}>
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={closeOnOverlayClick ? onClose : undefined}
      />
      
      {/* Modal */}
      <div
        className={cn(
          'relative bg-white rounded-xl shadow-2xl w-full animate-in fade-in-0 zoom-in-95 duration-300',
          sizes[size],
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              {title && <h2 className="text-xl font-semibold text-gray-900">{title}</h2>}
              {description && <p className="text-sm text-gray-600 mt-1">{description}</p>}
            </div>
            {showCloseButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-8 w-8 p-0 hover:bg-gray-100"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        )}
        
        {/* Content */}
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
}

// Confirm Dialog
export function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'default',
  loading = false,
}: ConfirmDialogProps) {
  const variants = {
    default: 'bg-blue-600 hover:bg-blue-700',
    destructive: 'bg-red-600 hover:bg-red-700',
    warning: 'bg-yellow-600 hover:bg-yellow-700',
  };

  const icons = {
    default: <Info className="h-6 w-6 text-blue-600" />,
    destructive: <AlertTriangle className="h-6 w-6 text-red-600" />,
    warning: <AlertCircle className="h-6 w-6 text-yellow-600" />,
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      showCloseButton={false}
    >
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {icons[variant]}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
          <p className="text-sm text-gray-600 mb-6">{description}</p>
          
          <div className="flex space-x-3 justify-end">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              {cancelText}
            </Button>
            <Button
              onClick={onConfirm}
              disabled={loading}
              className={variants[variant]}
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {confirmText}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}

// Alert Dialog
export function AlertDialog({
  isOpen,
  onClose,
  title,
  description,
  type = 'info',
  actionText = 'OK',
}: AlertDialogProps) {
  const types = {
    info: { icon: <Info className="h-6 w-6 text-blue-600" />, color: 'text-blue-600' },
    success: { icon: <CheckCircle className="h-6 w-6 text-green-600" />, color: 'text-green-600' },
    warning: { icon: <AlertTriangle className="h-6 w-6 text-yellow-600" />, color: 'text-yellow-600' },
    error: { icon: <AlertCircle className="h-6 w-6 text-red-600" />, color: 'text-red-600' },
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      showCloseButton={false}
    >
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {types[type].icon}
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
          <p className="text-sm text-gray-600 mb-6">{description}</p>
          
          <div className="flex justify-end">
            <Button onClick={onClose}>
              {actionText}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}

// Drawer Component
export function Drawer({
  isOpen,
  onClose,
  children,
  title,
  position = 'right',
  size = 'md',
  className,
}: DrawerProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!mounted || !isOpen) return null;

  const positions = {
    left: 'left-0 top-0 h-full animate-in slide-in-from-left duration-300',
    right: 'right-0 top-0 h-full animate-in slide-in-from-right duration-300',
    top: 'top-0 left-0 w-full animate-in slide-in-from-top duration-300',
    bottom: 'bottom-0 left-0 w-full animate-in slide-in-from-bottom duration-300',
  };

  const sizes = {
    sm: position === 'left' || position === 'right' ? 'w-80' : 'h-80',
    md: position === 'left' || position === 'right' ? 'w-96' : 'h-96',
    lg: position === 'left' || position === 'right' ? 'w-[32rem]' : 'h-[32rem]',
  };

  const drawerContent = (
    <div className="fixed inset-0 z-50">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div
        className={cn(
          'absolute bg-white shadow-2xl',
          positions[position],
          sizes[size],
          className
        )}
      >
        {/* Header */}
        {title && (
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0 hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {/* Content */}
        <div className="p-6 overflow-y-auto h-full">
          {children}
        </div>
      </div>
    </div>
  );

  return createPortal(drawerContent, document.body);
}

// Toast Context and Hook
const ToastContext = React.createContext<{
  toasts: ToastProps[];
  addToast: (toast: Omit<ToastProps, 'id'>) => void;
  removeToast: (id: string) => void;
}>({
  toasts: [],
  addToast: () => {},
  removeToast: () => {},
});

export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const addToast = (toast: Omit<ToastProps, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = { ...toast, id };
    
    setToasts(prev => [...prev, newToast]);
    
    // Auto remove after duration
    setTimeout(() => {
      removeToast(id);
    }, toast.duration || 5000);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </ToastContext.Provider>
  );
}

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within ToastProvider');
  }
  return context;
}

// Toast Container
function ToastContainer({ 
  toasts, 
  onRemove 
}: { 
  toasts: ToastProps[]; 
  onRemove: (id: string) => void; 
}) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const toastContent = (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} onRemove={onRemove} />
      ))}
    </div>
  );

  return createPortal(toastContent, document.body);
}

// Individual Toast Component
function Toast({ 
  id, 
  title, 
  description, 
  type = 'info', 
  action, 
  onRemove 
}: ToastProps & { onRemove: (id: string) => void }) {
  const types = {
    info: { icon: <Info className="h-5 w-5 text-blue-600" />, bg: 'bg-blue-50 border-blue-200' },
    success: { icon: <CheckCircle className="h-5 w-5 text-green-600" />, bg: 'bg-green-50 border-green-200' },
    warning: { icon: <AlertTriangle className="h-5 w-5 text-yellow-600" />, bg: 'bg-yellow-50 border-yellow-200' },
    error: { icon: <AlertCircle className="h-5 w-5 text-red-600" />, bg: 'bg-red-50 border-red-200' },
  };

  return (
    <div className={cn(
      'max-w-sm w-full bg-white border rounded-lg shadow-lg p-4 animate-in slide-in-from-right duration-300',
      types[type].bg
    )}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {types[type].icon}
        </div>
        <div className="flex-1 min-w-0">
          {title && <p className="text-sm font-medium text-gray-900">{title}</p>}
          <p className="text-sm text-gray-600">{description}</p>
          {action && (
            <Button
              variant="ghost"
              size="sm"
              onClick={action.onClick}
              className="mt-2 h-8 px-2 text-xs"
            >
              {action.label}
            </Button>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onRemove(id)}
          className="h-6 w-6 p-0 hover:bg-gray-100"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}
