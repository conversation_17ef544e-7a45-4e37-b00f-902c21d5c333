'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  Phone, 
  MapPin, 
  Shield, 
  Users,
  Camera,
  Mic,
  Share2,
  Clock,
  Heart,
  Zap,
  Navigation,
  Volume2,
  Bell,
  UserCheck,
  Car,
  MessageCircle
} from 'lucide-react';

interface EmergencySafetyViewProps {
  tripId?: string;
  isActive: boolean;
  userLocation: { lat: number; lng: number };
}

export default function EmergencySafetyView({ 
  tripId, 
  isActive, 
  userLocation 
}: EmergencySafetyViewProps) {
  const [emergencyActive, setEmergencyActive] = useState(false);
  const [countdown, setCountdown] = useState(10);
  const [isRecording, setIsRecording] = useState(false);
  const [emergencyContacts, setEmergencyContacts] = useState([
    { name: 'Emergency Services', number: '112', type: 'police' },
    { name: 'Mom', number: '+91 98765 43210', type: 'family' },
    { name: 'Dad', number: '+91 98765 43211', type: 'family' },
    { name: 'Best Friend', number: '+91 98765 43212', type: 'friend' }
  ]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (emergencyActive && countdown > 0) {
      interval = setInterval(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else if (countdown === 0) {
      // Auto-trigger emergency services
      handleEmergencyCall('112');
    }
    return () => clearInterval(interval);
  }, [emergencyActive, countdown]);

  const handleEmergencyActivation = () => {
    setEmergencyActive(true);
    setCountdown(10);
    // Start location sharing
    // Send alerts to emergency contacts
    // Begin audio/video recording
  };

  const handleEmergencyCancel = () => {
    setEmergencyActive(false);
    setCountdown(10);
  };

  const handleEmergencyCall = (number: string) => {
    // Initiate emergency call
    console.log(`Calling ${number}`);
  };

  const handleQuickAlert = (type: string) => {
    // Send quick alert with predefined message
    console.log(`Sending ${type} alert`);
  };

  const safetyFeatures = [
    {
      id: 'sos',
      title: 'SOS Emergency',
      description: 'Instant alert to emergency services',
      icon: <AlertTriangle className="h-6 w-6 text-red-600" />,
      color: 'red'
    },
    {
      id: 'share-trip',
      title: 'Share Trip',
      description: 'Live location sharing with contacts',
      icon: <Share2 className="h-6 w-6 text-blue-600" />,
      color: 'blue'
    },
    {
      id: 'fake-call',
      title: 'Fake Call',
      description: 'Simulate incoming call for safety',
      icon: <Phone className="h-6 w-6 text-green-600" />,
      color: 'green'
    },
    {
      id: 'record',
      title: 'Record Evidence',
      description: 'Audio/video recording for safety',
      icon: <Camera className="h-6 w-6 text-purple-600" />,
      color: 'purple'
    }
  ];

  const quickAlerts = [
    { type: 'breakdown', label: 'Vehicle Breakdown', icon: '🔧' },
    { type: 'accident', label: 'Accident', icon: '🚨' },
    { type: 'harassment', label: 'Harassment', icon: '⚠️' },
    { type: 'lost', label: 'Lost/Confused', icon: '🗺️' },
    { type: 'medical', label: 'Medical Emergency', icon: '🏥' },
    { type: 'suspicious', label: 'Suspicious Activity', icon: '👁️' }
  ];

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Emergency Header */}
      <div className={`p-4 ${emergencyActive ? 'bg-red-600' : 'bg-gray-900'} text-white`}>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold">Safety Center</h1>
            <p className="text-sm opacity-90">
              {emergencyActive ? 'Emergency Active' : 'You are protected'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="h-6 w-6" />
            {isActive && (
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            )}
          </div>
        </div>
      </div>

      {/* Emergency Countdown */}
      {emergencyActive && (
        <Alert className="m-4 border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <div className="flex items-center justify-between">
              <span>Emergency services will be called in {countdown}s</span>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={handleEmergencyCancel}
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                Cancel
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Emergency Button */}
      <div className="p-6">
        <div className="text-center mb-6">
          <Button
            size="lg"
            className={`w-32 h-32 rounded-full text-white font-bold text-lg ${
              emergencyActive 
                ? 'bg-red-600 hover:bg-red-700 animate-pulse' 
                : 'bg-red-500 hover:bg-red-600'
            }`}
            onClick={emergencyActive ? handleEmergencyCancel : handleEmergencyActivation}
          >
            {emergencyActive ? (
              <div className="text-center">
                <div className="text-2xl">{countdown}</div>
                <div className="text-xs">CANCEL</div>
              </div>
            ) : (
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 mx-auto mb-1" />
                <div>SOS</div>
              </div>
            )}
          </Button>
          <p className="text-sm text-gray-600 mt-3">
            {emergencyActive 
              ? 'Tap to cancel emergency alert' 
              : 'Hold for 3 seconds to activate emergency'
            }
          </p>
        </div>

        {/* Safety Features Grid */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          {safetyFeatures.map((feature) => (
            <Card 
              key={feature.id} 
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleQuickAlert(feature.id)}
            >
              <CardContent className="p-4 text-center">
                <div className="mb-2">{feature.icon}</div>
                <h3 className="font-medium text-sm">{feature.title}</h3>
                <p className="text-xs text-gray-600 mt-1">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Alerts */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center">
              <Zap className="h-4 w-4 mr-2 text-yellow-600" />
              Quick Alerts
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-2">
              {quickAlerts.map((alert) => (
                <Button
                  key={alert.type}
                  variant="outline"
                  size="sm"
                  className="h-auto p-2 flex-col"
                  onClick={() => handleQuickAlert(alert.type)}
                >
                  <span className="text-lg mb-1">{alert.icon}</span>
                  <span className="text-xs">{alert.label}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contacts */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center">
              <Phone className="h-4 w-4 mr-2 text-blue-600" />
              Emergency Contacts
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0 space-y-2">
            {emergencyContacts.map((contact, index) => (
              <div 
                key={index} 
                className="flex items-center justify-between p-2 border rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    contact.type === 'police' ? 'bg-red-100' :
                    contact.type === 'family' ? 'bg-blue-100' : 'bg-green-100'
                  }`}>
                    {contact.type === 'police' ? '🚔' : 
                     contact.type === 'family' ? '👨‍👩‍👧‍👦' : '👥'}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{contact.name}</div>
                    <div className="text-xs text-gray-600">{contact.number}</div>
                  </div>
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleEmergencyCall(contact.number)}
                >
                  <Phone className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Current Trip Safety */}
        {tripId && (
          <Card className="mb-6">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center">
                <Car className="h-4 w-4 mr-2 text-green-600" />
                Current Trip Safety
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Live tracking</span>
                <Badge className="bg-green-100 text-green-800">Active</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Emergency contacts notified</span>
                <Badge className="bg-blue-100 text-blue-800">3 contacts</Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Driver verification</span>
                <div className="flex items-center">
                  <UserCheck className="h-4 w-4 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">Verified</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 mt-3">
                <Button size="sm" variant="outline">
                  <MessageCircle className="h-3 w-3 mr-1" />
                  Chat Driver
                </Button>
                <Button size="sm" variant="outline">
                  <Share2 className="h-3 w-3 mr-1" />
                  Share Trip
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Safety Tips */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center">
              <Heart className="h-4 w-4 mr-2 text-pink-600" />
              Safety Tips
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2 text-sm text-gray-700">
              <div className="flex items-start space-x-2">
                <div className="w-1 h-1 bg-blue-500 rounded-full mt-2"></div>
                <span>Always share your trip details with trusted contacts</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1 h-1 bg-blue-500 rounded-full mt-2"></div>
                <span>Verify driver details before starting your trip</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1 h-1 bg-blue-500 rounded-full mt-2"></div>
                <span>Trust your instincts - if something feels wrong, act</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1 h-1 bg-blue-500 rounded-full mt-2"></div>
                <span>Keep your phone charged and accessible</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recording Status */}
        {isRecording && (
          <div className="fixed bottom-4 left-4 right-4">
            <Alert className="border-red-200 bg-red-50">
              <Mic className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <div className="flex items-center justify-between">
                  <span>Recording audio for safety</span>
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse mr-2"></div>
                    <span className="text-xs">REC</span>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          </div>
        )}
      </div>
    </div>
  );
}
