'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Star, 
  Award, 
  Car, 
  CreditCard,
  LogOut,
  Settings,
  Shield,
  TrendingUp
} from 'lucide-react';

export default function UserProfile() {
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(false);

  if (!user) {
    return null;
  }

  const handleLogout = async () => {
    setLoading(true);
    logout();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getRoleColor = (role: string) => {
    return role === 'driver' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800';
  };

  const getVerificationStatus = (isVerified: boolean) => {
    return isVerified ? (
      <Badge className="bg-green-100 text-green-800">
        <Shield className="w-3 h-3 mr-1" />
        Verified
      </Badge>
    ) : (
      <Badge variant="outline" className="text-orange-600 border-orange-600">
        <Shield className="w-3 h-3 mr-1" />
        Pending Verification
      </Badge>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Profile Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={user.profileImage} alt={user.fullName} />
                <AvatarFallback className="text-lg">
                  {getInitials(user.firstName, user.lastName)}
                </AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-2xl">{user.fullName}</CardTitle>
                <CardDescription className="flex items-center space-x-2 mt-2">
                  <Badge className={getRoleColor(user.role)}>
                    {user.role === 'driver' ? 'Driver' : 'Rider'}
                  </Badge>
                  {getVerificationStatus(user.isVerified)}
                </CardDescription>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleLogout}
                disabled={loading}
              >
                <LogOut className="w-4 h-4 mr-2" />
                {loading ? 'Signing out...' : 'Sign Out'}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Mail className="w-4 h-4 text-gray-500" />
              <span>{user.email}</span>
            </div>
            <div className="flex items-center space-x-3">
              <Phone className="w-4 h-4 text-gray-500" />
              <span>{user.phone}</span>
            </div>
          </CardContent>
        </Card>

        {/* Rewards & Points */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="w-5 h-5 mr-2" />
              Rewards & Points
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{user.rewardPoints}</div>
              <div className="text-sm text-gray-500">Reward Points</div>
            </div>
          </CardContent>
        </Card>

        {/* Role-specific Information */}
        {user.role === 'driver' && user.driverProfile && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Car className="w-5 h-5 mr-2" />
                Driver Profile
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Star className="w-5 h-5 text-yellow-500 mr-1" />
                    <span className="text-2xl font-bold">{user.driverProfile.rating}</span>
                  </div>
                  <div className="text-sm text-gray-500">Rating</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.driverProfile.totalRides}</div>
                  <div className="text-sm text-gray-500">Total Rides</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{user.totalEarnings?.toLocaleString() || 0}
                  </div>
                  <div className="text-sm text-gray-500">Total Earnings</div>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <h4 className="font-medium">Vehicle Information</h4>
                {user.driverProfile.vehicleModel && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Model:</span>
                    <span>{user.driverProfile.vehicleModel}</span>
                  </div>
                )}
                {user.driverProfile.vehicleColor && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Color:</span>
                    <span>{user.driverProfile.vehicleColor}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge className={user.driverProfile.isApproved ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'}>
                    {user.driverProfile.isApproved ? 'Approved' : 'Pending Approval'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {user.role === 'rider' && user.riderProfile && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Rider Profile
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Star className="w-5 h-5 text-yellow-500 mr-1" />
                    <span className="text-2xl font-bold">{user.riderProfile.rating}</span>
                  </div>
                  <div className="text-sm text-gray-500">Rating</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{user.riderProfile.totalRides}</div>
                  <div className="text-sm text-gray-500">Total Rides</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{user.totalSpent?.toLocaleString() || 0}
                  </div>
                  <div className="text-sm text-gray-500">Total Spent</div>
                </div>
              </div>
              
              {user.riderProfile.preferredPaymentMethod && (
                <>
                  <Separator className="my-4" />
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 flex items-center">
                      <CreditCard className="w-4 h-4 mr-2" />
                      Preferred Payment:
                    </span>
                    <Badge variant="outline">
                      {user.riderProfile.preferredPaymentMethod.toUpperCase()}
                    </Badge>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
