"""
Unit tests for optimization utilities
Generated automatically by development workflow
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.optimization_utils import *

class TestOptimizationUtils(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.sample_waypoints = [
            (19.0760, 72.8777),  # Mumbai coordinates
            (19.1136, 72.8697),  # <PERSON><PERSON><PERSON>
            (19.0596, 72.8656),  # BKC
            (19.0330, 72.8570)   # Worli
        ]
        
        self.sample_vehicles = [
            {
                'id': 'vehicle_001',
                'latitude': 19.0760,
                'longitude': 72.8777
            },
            {
                'id': 'vehicle_002', 
                'latitude': 19.1136,
                'longitude': 72.8697
            }
        ]
        
        self.sample_demand_points = [
            {
                'latitude': 19.0596,
                'longitude': 72.8656,
                'predicted_demand': 25
            },
            {
                'latitude': 19.0330,
                'longitude': 72.8570,
                'predicted_demand': 15
            }
        ]
    
    def tearDown(self):
        """Clean up after each test method."""
        pass

    def test_calculate_distance(self):
        """Test calculate_distance function."""
        # Test distance between Mumbai and Andheri (approximately 15-20 km)
        distance = calculate_distance(19.0760, 72.8777, 19.1136, 72.8697)
        self.assertGreater(distance, 10)
        self.assertLess(distance, 25)
        
        # Test same point distance
        distance_same = calculate_distance(19.0760, 72.8777, 19.0760, 72.8777)
        self.assertAlmostEqual(distance_same, 0, places=2)

    def test_optimize_route_efficiency(self):
        """Test optimize_route_efficiency function."""
        # Test with sample waypoints
        optimized_route = optimize_route_efficiency(self.sample_waypoints)
        
        # Should return all waypoint indices
        self.assertEqual(len(optimized_route), len(self.sample_waypoints))
        self.assertEqual(set(optimized_route), set(range(len(self.sample_waypoints))))
        
        # Should start with index 0
        self.assertEqual(optimized_route[0], 0)
        
        # Test with empty list
        empty_route = optimize_route_efficiency([])
        self.assertEqual(empty_route, [])
        
        # Test with single waypoint
        single_route = optimize_route_efficiency([(19.0760, 72.8777)])
        self.assertEqual(single_route, [0])

    def test_calculate_carbon_footprint(self):
        """Test calculate_carbon_footprint function."""
        # Test electric vehicle
        carbon_electric = calculate_carbon_footprint(100, "electric")
        self.assertEqual(carbon_electric, 2.0)  # 100 km * 0.02
        
        # Test petrol vehicle
        carbon_petrol = calculate_carbon_footprint(100, "petrol")
        self.assertEqual(carbon_petrol, 12.0)  # 100 km * 0.12
        
        # Test unknown vehicle type (should default to electric)
        carbon_unknown = calculate_carbon_footprint(100, "unknown")
        self.assertEqual(carbon_unknown, 2.0)
        
        # Test zero distance
        carbon_zero = calculate_carbon_footprint(0, "petrol")
        self.assertEqual(carbon_zero, 0)

    def test_optimize_fleet_distribution(self):
        """Test optimize_fleet_distribution function."""
        result = optimize_fleet_distribution(self.sample_demand_points, self.sample_vehicles)
        
        # Should return assignments for all vehicles
        self.assertEqual(len(result['assignments']), len(self.sample_vehicles))
        
        # Should have required keys
        self.assertIn('assignments', result)
        self.assertIn('total_efficiency', result)
        self.assertIn('average_efficiency', result)
        
        # Each assignment should have required fields
        for vehicle_id, assignment in result['assignments'].items():
            self.assertIn('target_location', assignment)
            self.assertIn('distance_to_move', assignment)
            self.assertIn('efficiency_score', assignment)
        
        # Test with empty inputs
        empty_result = optimize_fleet_distribution([], [])
        self.assertEqual(empty_result['assignments'], {})
        self.assertEqual(empty_result['total_efficiency'], 0)
        self.assertEqual(empty_result['average_efficiency'], 0)

    def test_calculate_surge_pricing(self):
        """Test calculate_surge_pricing function."""
        # Test normal conditions
        pricing = calculate_surge_pricing(100, 1.0, 1.0, 1.0)
        self.assertEqual(pricing['base_price'], 100)
        self.assertEqual(pricing['surge_multiplier'], 1.0)
        self.assertEqual(pricing['final_price'], 100)
        
        # Test high demand, low supply
        high_demand_pricing = calculate_surge_pricing(100, 2.0, 0.5, 1.0)
        self.assertGreater(high_demand_pricing['surge_multiplier'], 1.0)
        self.assertGreater(high_demand_pricing['final_price'], 100)
        
        # Test surge multiplier limits
        extreme_pricing = calculate_surge_pricing(100, 10.0, 0.1, 2.0)
        self.assertLessEqual(extreme_pricing['surge_multiplier'], 3.0)
        
        low_demand_pricing = calculate_surge_pricing(100, 0.1, 2.0, 1.0)
        self.assertGreaterEqual(low_demand_pricing['surge_multiplier'], 0.8)

    def test_optimize_battery_usage(self):
        """Test optimize_battery_usage function."""
        charging_stations = [
            {'latitude': 19.0760, 'longitude': 72.8777, 'id': 'station_001'}
        ]
        
        # Test sufficient charge
        sufficient_result = optimize_battery_usage(80, 30, charging_stations)
        self.assertTrue(sufficient_result['sufficient_charge'])
        self.assertEqual(sufficient_result['recommended_action'], 'proceed')
        
        # Test insufficient charge
        insufficient_result = optimize_battery_usage(20, 30, charging_stations)
        self.assertFalse(insufficient_result['sufficient_charge'])
        self.assertEqual(insufficient_result['recommended_action'], 'charge')
        self.assertIn('nearest_charging_station', insufficient_result)
        
        # Test no charging stations available
        no_stations_result = optimize_battery_usage(20, 30, [])
        self.assertEqual(no_stations_result['recommended_action'], 'find_alternative')

    def test_calculate_eta_with_traffic(self):
        """Test calculate_eta_with_traffic function."""
        # Test normal traffic
        eta_normal = calculate_eta_with_traffic(30, 60, 1.0)
        self.assertEqual(eta_normal['eta_hours'], 0.5)
        self.assertEqual(eta_normal['eta_minutes'], 30)
        
        # Test heavy traffic
        eta_heavy = calculate_eta_with_traffic(30, 60, 2.0)
        self.assertEqual(eta_heavy['eta_hours'], 1.0)
        self.assertEqual(eta_heavy['eta_minutes'], 60)
        self.assertEqual(eta_heavy['adjusted_speed_kmh'], 30)
        
        # Test light traffic
        eta_light = calculate_eta_with_traffic(30, 60, 0.5)
        self.assertEqual(eta_light['eta_hours'], 0.25)
        self.assertEqual(eta_light['eta_minutes'], 15)
        self.assertEqual(eta_light['adjusted_speed_kmh'], 120)

    def test_edge_cases(self):
        """Test edge cases and error conditions."""
        # Test calculate_distance with extreme coordinates
        distance_extreme = calculate_distance(-90, -180, 90, 180)
        self.assertGreater(distance_extreme, 0)
        
        # Test optimize_route_efficiency with two points
        two_point_route = optimize_route_efficiency([(0, 0), (1, 1)])
        self.assertEqual(two_point_route, [0, 1])
        
        # Test calculate_carbon_footprint with negative distance
        carbon_negative = calculate_carbon_footprint(-10, "electric")
        self.assertEqual(carbon_negative, -0.2)  # Should handle negative values
        
        # Test calculate_surge_pricing with zero supply
        zero_supply_pricing = calculate_surge_pricing(100, 1.0, 0, 1.0)
        self.assertGreater(zero_supply_pricing['surge_multiplier'], 1.0)

    def test_performance(self):
        """Test performance with larger datasets."""
        # Test with many waypoints
        large_waypoints = [(i, i) for i in range(100)]
        start_time = time.time()
        optimize_route_efficiency(large_waypoints)
        end_time = time.time()
        
        # Should complete within reasonable time (less than 1 second)
        self.assertLess(end_time - start_time, 1.0)
        
        # Test with many vehicles and demand points
        many_vehicles = [{'id': f'v_{i}', 'latitude': i, 'longitude': i} for i in range(50)]
        many_demands = [{'latitude': i+0.5, 'longitude': i+0.5, 'predicted_demand': 10} for i in range(50)]
        
        start_time = time.time()
        optimize_fleet_distribution(many_demands, many_vehicles)
        end_time = time.time()
        
        # Should complete within reasonable time
        self.assertLess(end_time - start_time, 2.0)

if __name__ == '__main__':
    import time
    unittest.main()
