'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Leaf, 
  TreePine, 
  Droplets, 
  Wind, 
  Sun,
  Award,
  Target,
  TrendingUp,
  Globe,
  Heart,
  Zap,
  Recycle,
  Users,
  Calendar,
  MapPin,
  Star
} from 'lucide-react';

interface SustainabilityDashboardProps {
  userId: string;
}

export default function SustainabilityDashboard({ userId }: SustainabilityDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [carbonGoal, setCarbonGoal] = useState(50);

  const sustainabilityData = {
    overview: {
      carbonSaved: 45.8,
      treesEquivalent: 2.1,
      waterSaved: 1250,
      airQualityImproved: 15.2,
      rank: 156,
      totalUsers: 45230
    },
    monthlyProgress: {
      current: 45.8,
      goal: 50,
      percentage: 92
    },
    breakdown: {
      transportation: [
        { mode: 'Bike', trips: 15, carbon: 0, percentage: 45 },
        { mode: 'Scooter', trips: 8, carbon: 2.1, percentage: 24 },
        { mode: 'Auto', trips: 5, carbon: 3.8, percentage: 15 },
        { mode: 'Car', trips: 3, carbon: 4.2, percentage: 9 },
        { mode: 'Public', trips: 2, carbon: 1.5, percentage: 6 }
      ],
      timeline: [
        { date: '2024-01-01', carbon: 12.5, trees: 0.6 },
        { date: '2024-01-08', carbon: 25.2, trees: 1.2 },
        { date: '2024-01-15', carbon: 35.8, trees: 1.7 },
        { date: '2024-01-22', carbon: 42.1, trees: 2.0 },
        { date: '2024-01-29', carbon: 45.8, trees: 2.1 }
      ]
    },
    achievements: [
      { id: 1, title: 'Eco Warrior', description: 'Saved 50kg CO₂', icon: '🌱', unlocked: true, date: '2024-01-15' },
      { id: 2, title: 'Tree Hugger', description: 'Equivalent to 5 trees planted', icon: '🌳', unlocked: false, progress: 42 },
      { id: 3, title: 'Clean Air Champion', description: '100 bike rides', icon: '💨', unlocked: false, progress: 75 },
      { id: 4, title: 'Carbon Neutral', description: 'Offset all emissions', icon: '⚖️', unlocked: false, progress: 20 }
    ],
    offsetOptions: [
      {
        id: 'trees-india',
        name: 'Plant Trees in India',
        description: 'Support reforestation in the Western Ghats',
        costPerKg: 50,
        impact: 'Biodiversity conservation',
        certification: 'Gold Standard',
        image: '🌳',
        location: 'Karnataka, India'
      },
      {
        id: 'solar-rajasthan',
        name: 'Solar Energy Project',
        description: 'Clean energy generation in Rajasthan',
        costPerKg: 75,
        impact: 'Renewable energy',
        certification: 'VCS',
        image: '☀️',
        location: 'Rajasthan, India'
      },
      {
        id: 'biogas-rural',
        name: 'Rural Biogas Initiative',
        description: 'Clean cooking fuel for rural communities',
        costPerKg: 60,
        impact: 'Community development',
        certification: 'CDM',
        image: '🔥',
        location: 'Rural India'
      }
    ],
    leaderboard: [
      { rank: 1, name: 'Priya S.', carbon: 125.5, badge: '🏆' },
      { rank: 2, name: 'Arjun K.', carbon: 118.2, badge: '🥈' },
      { rank: 3, name: 'Meera R.', carbon: 112.8, badge: '🥉' },
      { rank: 4, name: 'Rohit M.', carbon: 98.5, badge: '⭐' },
      { rank: 5, name: 'You', carbon: 45.8, badge: '🌱' }
    ]
  };

  const calculateOffsetCost = (carbonAmount: number, option: any) => {
    return Math.round(carbonAmount * option.costPerKg);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center justify-center">
          <Leaf className="h-8 w-8 mr-3 text-green-600" />
          Sustainability Dashboard
        </h1>
        <p className="text-gray-600">Track your environmental impact and make a difference</p>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6 text-center">
            <Leaf className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-700">{sustainabilityData.overview.carbonSaved} kg</div>
            <div className="text-sm text-green-600">CO₂ Saved</div>
            <div className="flex items-center justify-center mt-2">
              <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
              <span className="text-xs text-green-600">+15% this month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6 text-center">
            <TreePine className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-700">{sustainabilityData.overview.treesEquivalent}</div>
            <div className="text-sm text-blue-600">Trees Equivalent</div>
            <div className="text-xs text-blue-500 mt-1">Based on 20-year growth</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-cyan-50 to-cyan-100 border-cyan-200">
          <CardContent className="p-6 text-center">
            <Droplets className="h-8 w-8 text-cyan-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-cyan-700">{sustainabilityData.overview.waterSaved}L</div>
            <div className="text-sm text-cyan-600">Water Saved</div>
            <div className="text-xs text-cyan-500 mt-1">Reduced fuel production</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6 text-center">
            <Wind className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-700">{sustainabilityData.overview.airQualityImproved}%</div>
            <div className="text-sm text-purple-600">Air Quality Impact</div>
            <div className="text-xs text-purple-500 mt-1">Local improvement</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="progress" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
          <TabsTrigger value="offset">Offset</TabsTrigger>
          <TabsTrigger value="community">Community</TabsTrigger>
        </TabsList>

        {/* Progress Tab */}
        <TabsContent value="progress" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-green-600" />
                  Monthly Goal Progress
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {sustainabilityData.monthlyProgress.current} kg
                  </div>
                  <div className="text-sm text-gray-600">
                    of {sustainabilityData.monthlyProgress.goal} kg goal
                  </div>
                </div>
                
                <Progress 
                  value={sustainabilityData.monthlyProgress.percentage} 
                  className="h-3"
                />
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    {sustainabilityData.monthlyProgress.percentage}% complete
                  </span>
                  <span className="text-green-600">
                    {sustainabilityData.monthlyProgress.goal - sustainabilityData.monthlyProgress.current} kg to go
                  </span>
                </div>

                <div className="bg-green-50 p-3 rounded-lg">
                  <p className="text-sm text-green-700">
                    🎉 You're on track to exceed your goal! Keep up the great work.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-yellow-600" />
                  Achievements
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {sustainabilityData.achievements.map((achievement) => (
                  <div 
                    key={achievement.id} 
                    className={`flex items-center space-x-3 p-3 rounded-lg ${
                      achievement.unlocked 
                        ? 'bg-yellow-50 border border-yellow-200' 
                        : 'bg-gray-50 border border-gray-200'
                    }`}
                  >
                    <div className="text-2xl">{achievement.icon}</div>
                    <div className="flex-1">
                      <div className="font-medium">{achievement.title}</div>
                      <div className="text-sm text-gray-600">{achievement.description}</div>
                      {!achievement.unlocked && achievement.progress && (
                        <div className="mt-1">
                          <Progress value={achievement.progress} className="h-1" />
                          <div className="text-xs text-gray-500 mt-1">
                            {achievement.progress}% complete
                          </div>
                        </div>
                      )}
                    </div>
                    {achievement.unlocked && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        Unlocked
                      </Badge>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Breakdown Tab */}
        <TabsContent value="breakdown" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Transportation Mode Impact</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {sustainabilityData.breakdown.transportation.map((mode, index) => (
                  <div key={mode.mode} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 rounded-full" style={{
                        backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6'][index]
                      }}></div>
                      <div>
                        <div className="font-medium">{mode.mode}</div>
                        <div className="text-sm text-gray-600">{mode.trips} trips</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{mode.carbon} kg CO₂</div>
                      <div className="text-sm text-gray-600">{mode.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Weekly Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sustainabilityData.breakdown.timeline.map((week, index) => (
                  <div key={week.date} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium">Week {index + 1}</div>
                      <div className="text-sm text-gray-600">
                        {new Date(week.date).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-600">{week.carbon} kg CO₂</div>
                      <div className="text-sm text-gray-600">{week.trees} trees equiv.</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Offset Tab */}
        <TabsContent value="offset" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Recycle className="h-5 w-5 mr-2 text-green-600" />
                Carbon Offset Options
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                <div className="text-center">
                  <div className="text-lg font-medium text-blue-800">
                    Remaining Carbon Footprint
                  </div>
                  <div className="text-2xl font-bold text-blue-600">12.5 kg CO₂</div>
                  <div className="text-sm text-blue-600">
                    Offset this to become carbon neutral
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                {sustainabilityData.offsetOptions.map((option) => (
                  <Card key={option.id} className="border hover:border-green-300 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="text-3xl">{option.image}</div>
                          <div>
                            <h3 className="font-medium">{option.name}</h3>
                            <p className="text-sm text-gray-600">{option.description}</p>
                            <div className="flex items-center space-x-2 mt-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              <span className="text-xs text-gray-500">{option.location}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-green-600">
                            ₹{calculateOffsetCost(12.5, option)}
                          </div>
                          <div className="text-xs text-gray-600">
                            ₹{option.costPerKg}/kg CO₂
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Badge variant="outline" className="text-xs">
                            {option.certification}
                          </Badge>
                          <span className="text-xs text-gray-600">{option.impact}</span>
                        </div>
                        <Button size="sm" className="bg-green-600 hover:bg-green-700">
                          Offset Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Community Tab */}
        <TabsContent value="community" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2 text-purple-600" />
                  Leaderboard
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sustainabilityData.leaderboard.map((user) => (
                    <div 
                      key={user.rank} 
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        user.name === 'You' 
                          ? 'bg-blue-50 border border-blue-200' 
                          : 'bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center border">
                          <span className="text-sm font-medium">#{user.rank}</span>
                        </div>
                        <div>
                          <div className="font-medium flex items-center">
                            {user.name}
                            <span className="ml-2">{user.badge}</span>
                          </div>
                          <div className="text-sm text-gray-600">{user.carbon} kg CO₂ saved</div>
                        </div>
                      </div>
                      {user.name === 'You' && (
                        <Badge className="bg-blue-100 text-blue-800">You</Badge>
                      )}
                    </div>
                  ))}
                </div>

                <div className="mt-4 p-3 bg-green-50 rounded-lg text-center">
                  <div className="text-sm text-green-700">
                    🎯 You're ranked #{sustainabilityData.overview.rank} out of {sustainabilityData.overview.totalUsers.toLocaleString()} users!
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Globe className="h-5 w-5 mr-2 text-blue-600" />
                  Community Impact
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-gray-900">2,450 kg</div>
                  <div className="text-sm text-gray-600">Total CO₂ saved this month</div>
                  <div className="text-xs text-green-600 mt-1">by our community</div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Trees planted equivalent</span>
                    <span className="font-medium">112 trees</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Water saved</span>
                    <span className="font-medium">45,000 L</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Active eco-warriors</span>
                    <span className="font-medium">1,250 users</span>
                  </div>
                </div>

                <div className="bg-yellow-50 p-3 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Star className="h-4 w-4 text-yellow-600 mr-2" />
                    <span className="text-sm font-medium text-yellow-800">Challenge</span>
                  </div>
                  <p className="text-sm text-gray-700">
                    Join the "Car-Free Week" challenge and win exclusive rewards!
                  </p>
                  <Button size="sm" className="mt-2 bg-yellow-600 hover:bg-yellow-700">
                    Join Challenge
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
