/**
 * AR Navigation Component
 * Provides augmented reality navigation features for the two-wheeler sharing platform
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useFeatureFlag } from '@/utils/feature-flags';
import { calculateDistance, calculateETAWithTraffic, Location } from '@/utils/ride-optimization';
import { Button } from '@/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/ui/card';
import { Badge } from '@/ui/badge';
import { Alert, AlertDescription } from '@/ui/alert';

interface ARNavigationProps {
  origin: Location;
  destination: Location;
  driverLocation?: Location;
  onNavigationStart?: () => void;
  onNavigationEnd?: () => void;
  className?: string;
}

interface ARMarker {
  id: string;
  position: Location;
  type: 'driver' | 'pickup' | 'destination' | 'waypoint';
  label: string;
  distance: number;
}

interface NavigationState {
  isActive: boolean;
  currentStep: number;
  totalSteps: number;
  estimatedTimeRemaining: number;
  distanceRemaining: number;
}

export function ARNavigation({
  origin,
  destination,
  driverLocation,
  onNavigationStart,
  onNavigationEnd,
  className = ''
}: ARNavigationProps) {
  const isAREnabled = useFeatureFlag('ar_navigation');
  const [isARSupported, setIsARSupported] = useState(false);
  const [isARActive, setIsARActive] = useState(false);
  const [navigationState, setNavigationState] = useState<NavigationState>({
    isActive: false,
    currentStep: 0,
    totalSteps: 0,
    estimatedTimeRemaining: 0,
    distanceRemaining: 0
  });
  const [arMarkers, setARMarkers] = useState<ARMarker[]>([]);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Check AR support on component mount
  useEffect(() => {
    checkARSupport();
  }, []);

  // Update AR markers when locations change
  useEffect(() => {
    if (isARActive) {
      updateARMarkers();
    }
  }, [origin, destination, driverLocation, isARActive]);

  /**
   * Check if AR is supported by the device
   */
  const checkARSupport = async () => {
    try {
      // Check for WebXR support
      if ('xr' in navigator) {
        const xr = (navigator as any).xr;
        const isSupported = await xr.isSessionSupported('immersive-ar');
        setIsARSupported(isSupported);
      } else {
        // Fallback to camera access for basic AR
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: 'environment' }
        });
        setIsARSupported(true);
        stream.getTracks().forEach(track => track.stop());
      }
    } catch (error) {
      console.warn('AR not supported:', error);
      setIsARSupported(false);
    }
  };

  /**
   * Start AR navigation session
   */
  const startARNavigation = async () => {
    try {
      setError(null);
      
      // Request camera permission
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }

      setIsARActive(true);
      setNavigationState({
        isActive: true,
        currentStep: 1,
        totalSteps: 5,
        estimatedTimeRemaining: calculateETAWithTraffic(origin, destination).eta,
        distanceRemaining: calculateDistance(origin, destination)
      });

      updateARMarkers();
      onNavigationStart?.();

      // Start AR rendering loop
      startARRenderLoop();

    } catch (error) {
      setError('Failed to start AR navigation. Please check camera permissions.');
      console.error('AR navigation error:', error);
    }
  };

  /**
   * Stop AR navigation session
   */
  const stopARNavigation = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }

    setIsARActive(false);
    setNavigationState({
      isActive: false,
      currentStep: 0,
      totalSteps: 0,
      estimatedTimeRemaining: 0,
      distanceRemaining: 0
    });

    onNavigationEnd?.();
  };

  /**
   * Update AR markers based on current locations
   */
  const updateARMarkers = () => {
    const markers: ARMarker[] = [];

    // Add driver marker if available
    if (driverLocation) {
      markers.push({
        id: 'driver',
        position: driverLocation,
        type: 'driver',
        label: 'Your Driver',
        distance: calculateDistance(origin, driverLocation)
      });
    }

    // Add pickup marker
    markers.push({
      id: 'pickup',
      position: origin,
      type: 'pickup',
      label: 'Pickup Location',
      distance: 0
    });

    // Add destination marker
    markers.push({
      id: 'destination',
      position: destination,
      type: 'destination',
      label: 'Destination',
      distance: calculateDistance(origin, destination)
    });

    setARMarkers(markers);
  };

  /**
   * Start AR rendering loop
   */
  const startARRenderLoop = () => {
    const renderFrame = () => {
      if (!isARActive || !canvasRef.current || !videoRef.current) return;

      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw video feed
      ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

      // Draw AR overlays
      drawAROverlays(ctx);

      // Continue rendering
      if (isARActive) {
        requestAnimationFrame(renderFrame);
      }
    };

    requestAnimationFrame(renderFrame);
  };

  /**
   * Draw AR overlays on canvas
   */
  const drawAROverlays = (ctx: CanvasRenderingContext2D) => {
    const canvas = ctx.canvas;

    // Draw navigation arrow (simplified)
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2, canvas.height - 100);
    ctx.lineTo(canvas.width / 2, canvas.height - 200);
    ctx.lineTo(canvas.width / 2 + 20, canvas.height - 180);
    ctx.moveTo(canvas.width / 2, canvas.height - 200);
    ctx.lineTo(canvas.width / 2 - 20, canvas.height - 180);
    ctx.stroke();

    // Draw distance and ETA
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, 10, 200, 80);
    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.fillText(`Distance: ${navigationState.distanceRemaining.toFixed(1)} km`, 20, 35);
    ctx.fillText(`ETA: ${Math.round(navigationState.estimatedTimeRemaining)} min`, 20, 60);

    // Draw AR markers
    arMarkers.forEach(marker => {
      drawARMarker(ctx, marker);
    });
  };

  /**
   * Draw individual AR marker
   */
  const drawARMarker = (ctx: CanvasRenderingContext2D, marker: ARMarker) => {
    // Simplified marker rendering - in real implementation would use 3D positioning
    const x = Math.random() * ctx.canvas.width; // Placeholder positioning
    const y = Math.random() * ctx.canvas.height;

    // Draw marker circle
    ctx.fillStyle = getMarkerColor(marker.type);
    ctx.beginPath();
    ctx.arc(x, y, 10, 0, 2 * Math.PI);
    ctx.fill();

    // Draw label
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    ctx.fillText(marker.label, x + 15, y + 5);
  };

  /**
   * Get color for marker type
   */
  const getMarkerColor = (type: ARMarker['type']): string => {
    switch (type) {
      case 'driver': return '#3b82f6';
      case 'pickup': return '#10b981';
      case 'destination': return '#ef4444';
      case 'waypoint': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  // Don't render if AR feature is disabled
  if (!isAREnabled) {
    return null;
  }

  return (
    <div className={`ar-navigation ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            AR Navigation
            <Badge variant={isARSupported ? 'default' : 'secondary'}>
              {isARSupported ? 'Supported' : 'Not Supported'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {!isARSupported && (
            <Alert className="mb-4">
              <AlertDescription>
                AR navigation is not supported on this device. Please use standard navigation.
              </AlertDescription>
            </Alert>
          )}

          {!isARActive ? (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Experience immersive navigation with augmented reality overlays showing your driver location, 
                pickup point, and destination in real-time.
              </p>
              
              <Button 
                onClick={startARNavigation}
                disabled={!isARSupported}
                className="w-full"
              >
                Start AR Navigation
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* AR View */}
              <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  className="absolute inset-0 w-full h-full object-cover"
                  playsInline
                  muted
                />
                <canvas
                  ref={canvasRef}
                  className="absolute inset-0 w-full h-full"
                  width={640}
                  height={480}
                />
              </div>

              {/* Navigation Controls */}
              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <p>Step {navigationState.currentStep} of {navigationState.totalSteps}</p>
                  <p className="text-muted-foreground">
                    {Math.round(navigationState.estimatedTimeRemaining)} min remaining
                  </p>
                </div>
                
                <Button 
                  onClick={stopARNavigation}
                  variant="outline"
                >
                  Stop AR
                </Button>
              </div>

              {/* AR Markers List */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Visible Markers</h4>
                {arMarkers.map(marker => (
                  <div key={marker.id} className="flex items-center justify-between text-sm">
                    <span className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: getMarkerColor(marker.type) }}
                      />
                      {marker.label}
                    </span>
                    <span className="text-muted-foreground">
                      {marker.distance.toFixed(1)} km
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
