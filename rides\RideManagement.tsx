'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MapPin, 
  Clock, 
  Star, 
  Phone, 
  Navigation, 
  CheckCircle, 
  XCircle,
  PlayCircle,
  StopCircle,
  Timer,
  CreditCard
} from 'lucide-react';

interface Ride {
  _id: string;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  pickupLocation: {
    address: string;
    landmark?: string;
  };
  dropoffLocation: {
    address: string;
    landmark?: string;
  };
  estimatedDistance: number;
  estimatedDuration: number;
  finalAmount: number;
  rideType: string;
  paymentMethod: string;
  requestedAt: string;
  acceptedAt?: string;
  startedAt?: string;
  completedAt?: string;
  rider?: {
    firstName: string;
    lastName: string;
    phone: string;
    profileImage?: string;
  };
  driver?: {
    firstName: string;
    lastName: string;
    phone: string;
    profileImage?: string;
    driverProfile: {
      rating: number;
      vehicleModel?: string;
      vehicleColor?: string;
    };
  };
}

export default function RideManagement() {
  const { user, token } = useAuth();
  const [rides, setRides] = useState<Ride[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  // Fetch user's rides
  const fetchRides = async () => {
    try {
      const response = await fetch('/api/rides/my-rides', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRides(data.rides || []);
      } else {
        setError('Failed to fetch rides');
      }
    } catch (error) {
      setError('Network error while fetching rides');
    } finally {
      setLoading(false);
    }
  };

  // Update ride status
  const updateRideStatus = async (rideId: string, status: string) => {
    try {
      const response = await fetch('/api/rides/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ rideId, status }),
      });

      if (response.ok) {
        fetchRides(); // Refresh rides
      } else {
        const data = await response.json();
        setError(data.error || 'Failed to update ride status');
      }
    } catch (error) {
      setError('Network error while updating ride');
    }
  };

  useEffect(() => {
    fetchRides();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Timer className="w-4 h-4" />;
      case 'accepted': return <CheckCircle className="w-4 h-4" />;
      case 'in_progress': return <PlayCircle className="w-4 h-4" />;
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const activeRides = rides.filter(ride => 
    ['pending', 'accepted', 'in_progress'].includes(ride.status)
  );
  const completedRides = rides.filter(ride => 
    ['completed', 'cancelled'].includes(ride.status)
  );

  const RideCard = ({ ride }: { ride: Ride }) => (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg flex items-center">
              {getStatusIcon(ride.status)}
              <span className="ml-2">
                {ride.pickupLocation.address.split(',')[0]} → {ride.dropoffLocation.address.split(',')[0]}
              </span>
            </CardTitle>
            <CardDescription className="mt-1">
              {new Date(ride.requestedAt).toLocaleDateString()} • {ride.estimatedDistance} km • ₹{ride.finalAmount}
            </CardDescription>
          </div>
          <Badge className={getStatusColor(ride.status)}>
            {ride.status.replace('_', ' ').toUpperCase()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Route Information */}
        <div className="space-y-2">
          <div className="flex items-start space-x-3">
            <MapPin className="w-4 h-4 text-green-500 mt-1" />
            <div>
              <p className="font-medium">{ride.pickupLocation.address}</p>
              {ride.pickupLocation.landmark && (
                <p className="text-sm text-gray-500">{ride.pickupLocation.landmark}</p>
              )}
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <MapPin className="w-4 h-4 text-red-500 mt-1" />
            <div>
              <p className="font-medium">{ride.dropoffLocation.address}</p>
              {ride.dropoffLocation.landmark && (
                <p className="text-sm text-gray-500">{ride.dropoffLocation.landmark}</p>
              )}
            </div>
          </div>
        </div>

        {/* Rider/Driver Information */}
        {user?.role === 'driver' && ride.rider && (
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <Avatar>
              <AvatarImage src={ride.rider.profileImage} />
              <AvatarFallback>
                {ride.rider.firstName.charAt(0)}{ride.rider.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <p className="font-medium">{ride.rider.firstName} {ride.rider.lastName}</p>
              <p className="text-sm text-gray-500">Rider</p>
            </div>
            <Button variant="outline" size="sm">
              <Phone className="w-4 h-4 mr-1" />
              Call
            </Button>
          </div>
        )}

        {user?.role === 'rider' && ride.driver && (
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <Avatar>
              <AvatarImage src={ride.driver.profileImage} />
              <AvatarFallback>
                {ride.driver.firstName.charAt(0)}{ride.driver.lastName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <p className="font-medium">{ride.driver.firstName} {ride.driver.lastName}</p>
              <div className="flex items-center space-x-2">
                <Star className="w-3 h-3 text-yellow-500" />
                <span className="text-sm">{ride.driver.driverProfile.rating}</span>
                {ride.driver.driverProfile.vehicleModel && (
                  <span className="text-sm text-gray-500">
                    • {ride.driver.driverProfile.vehicleModel}
                  </span>
                )}
              </div>
            </div>
            <Button variant="outline" size="sm">
              <Phone className="w-4 h-4 mr-1" />
              Call
            </Button>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2">
          {user?.role === 'driver' && ride.status === 'accepted' && (
            <Button 
              onClick={() => updateRideStatus(ride._id, 'in_progress')}
              className="flex-1"
            >
              <PlayCircle className="w-4 h-4 mr-2" />
              Start Ride
            </Button>
          )}

          {user?.role === 'driver' && ride.status === 'in_progress' && (
            <Button 
              onClick={() => updateRideStatus(ride._id, 'completed')}
              className="flex-1"
            >
              <StopCircle className="w-4 h-4 mr-2" />
              Complete Ride
            </Button>
          )}

          {ride.status === 'accepted' && (
            <Button 
              variant="outline"
              onClick={() => updateRideStatus(ride._id, 'cancelled')}
            >
              <XCircle className="w-4 h-4 mr-2" />
              Cancel
            </Button>
          )}

          {ride.status === 'in_progress' && (
            <Button variant="outline" size="sm">
              <Navigation className="w-4 h-4 mr-2" />
              Track
            </Button>
          )}
        </div>

        {/* Payment Information */}
        <div className="flex items-center justify-between text-sm text-gray-600 pt-2 border-t">
          <div className="flex items-center space-x-1">
            <CreditCard className="w-4 h-4" />
            <span>{ride.paymentMethod.toUpperCase()}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>~{ride.estimatedDuration} min</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold">My Rides</h2>
        <p className="text-gray-600">Manage your ride bookings and history</p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active">
            Active Rides ({activeRides.length})
          </TabsTrigger>
          <TabsTrigger value="history">
            Ride History ({completedRides.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="mt-6">
          {activeRides.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">No active rides</p>
                {user?.role === 'rider' && (
                  <Button className="mt-4">Book a Ride</Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div>
              {activeRides.map(ride => (
                <RideCard key={ride._id} ride={ride} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          {completedRides.length === 0 ? (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-gray-500">No ride history</p>
              </CardContent>
            </Card>
          ) : (
            <div>
              {completedRides.map(ride => (
                <RideCard key={ride._id} ride={ride} />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
