// AI-Powered Demand Forecasting Service
import { apmService } from '../monitoring/apm';
import { Location } from './routeOptimization';

export interface DemandForecast {
  location: Location;
  timeWindow: {
    start: Date;
    end: Date;
    duration: number; // in minutes
  };
  predictedDemand: number;
  confidence: number;
  factors: {
    historical: number;
    seasonal: number;
    weather: number;
    events: number;
    trends: number;
  };
  recommendations: {
    driverPositioning: Location[];
    incentiveZones: Array<{
      location: Location;
      radius: number;
      incentiveAmount: number;
    }>;
    surgeAreas: Array<{
      location: Location;
      radius: number;
      expectedMultiplier: number;
    }>;
  };
}

export interface HistoricalPattern {
  hourOfDay: number;
  dayOfWeek: number;
  averageDemand: number;
  peakDemand: number;
  variance: number;
  seasonalFactor: number;
}

export interface ExternalEvent {
  id: string;
  name: string;
  type: 'concert' | 'sports' | 'conference' | 'festival' | 'holiday';
  location: Location;
  startTime: Date;
  endTime: Date;
  expectedAttendance: number;
  impactRadius: number; // in km
  demandMultiplier: number;
}

export interface WeatherImpact {
  condition: string;
  temperature: number;
  precipitation: number;
  windSpeed: number;
  visibility: number;
  demandImpact: number; // -1 to 1 scale
  durationImpact: number; // multiplier for ride duration
}

class DemandForecastingService {
  private mlModelEndpoint: string;
  private cache: Map<string, DemandForecast> = new Map();
  private cacheExpiry: number = 15 * 60 * 1000; // 15 minutes

  constructor() {
    this.mlModelEndpoint = process.env.DEMAND_ML_ENDPOINT || 'http://localhost:8002';
  }

  /**
   * Forecast demand for specific location and time window
   */
  async forecastDemand(
    location: Location,
    timeWindow: { start: Date; end: Date },
    options?: {
      includeRecommendations?: boolean;
      granularity?: 'hourly' | '15min' | '30min';
    }
  ): Promise<DemandForecast> {
    const timer = apmService.startTimer('demand_forecasting');
    
    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(location, timeWindow);
      const cachedForecast = this.getCachedForecast(cacheKey);
      if (cachedForecast) {
        timer.end(true);
        return cachedForecast;
      }

      // Gather input data
      const historicalData = await this.getHistoricalData(location, timeWindow);
      const externalEvents = await this.getExternalEvents(location, timeWindow);
      const weatherData = await this.getWeatherForecast(location, timeWindow);
      const trendData = await this.getTrendData(location);

      // Generate ML features
      const features = this.extractForecastingFeatures(
        location,
        timeWindow,
        historicalData,
        externalEvents,
        weatherData,
        trendData
      );

      // Get ML prediction
      let prediction;
      try {
        prediction = await this.callForecastingML(features);
      } catch (error) {
        console.error('ML forecasting failed:', error);
        prediction = this.calculateRuleBasedForecast(features);
      }

      // Generate recommendations
      const recommendations = options?.includeRecommendations 
        ? await this.generateRecommendations(location, prediction, features)
        : { driverPositioning: [], incentiveZones: [], surgeAreas: [] };

      const forecast: DemandForecast = {
        location,
        timeWindow: {
          ...timeWindow,
          duration: (timeWindow.end.getTime() - timeWindow.start.getTime()) / (1000 * 60),
        },
        predictedDemand: prediction.demand,
        confidence: prediction.confidence,
        factors: prediction.factors,
        recommendations,
      };

      // Cache the result
      this.cacheForecast(cacheKey, forecast);

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'demand_forecasted',
        value: prediction.demand,
        timestamp: Date.now(),
        properties: {
          confidence: prediction.confidence,
          location: `${location.latitude},${location.longitude}`,
        },
      });

      timer.end(true);
      return forecast;

    } catch (error) {
      timer.end(false);
      console.error('Demand forecasting failed:', error);
      throw error;
    }
  }

  /**
   * Forecast demand for multiple locations
   */
  async forecastMultipleLocations(
    locations: Location[],
    timeWindow: { start: Date; end: Date }
  ): Promise<DemandForecast[]> {
    const forecasts = await Promise.allSettled(
      locations.map(location => this.forecastDemand(location, timeWindow))
    );

    return forecasts
      .filter((result): result is PromiseFulfilledResult<DemandForecast> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  }

  /**
   * Get real-time demand adjustments
   */
  async getRealTimeDemandAdjustment(location: Location): Promise<{
    currentDemand: number;
    adjustment: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }> {
    try {
      // Get current ride requests in the area
      const currentRequests = await this.getCurrentRideRequests(location);
      
      // Get driver availability
      const driverAvailability = await this.getDriverAvailability(location);
      
      // Calculate demand-supply ratio
      const demandSupplyRatio = currentRequests / Math.max(driverAvailability, 1);
      
      // Determine trend based on recent data
      const recentTrend = await this.getRecentDemandTrend(location);
      
      return {
        currentDemand: currentRequests,
        adjustment: this.calculateDemandAdjustment(demandSupplyRatio),
        trend: recentTrend,
      };
      
    } catch (error) {
      console.error('Real-time demand adjustment failed:', error);
      return {
        currentDemand: 0,
        adjustment: 1.0,
        trend: 'stable',
      };
    }
  }

  /**
   * Extract features for ML model
   */
  private extractForecastingFeatures(
    location: Location,
    timeWindow: { start: Date; end: Date },
    historicalData: HistoricalPattern[],
    externalEvents: ExternalEvent[],
    weatherData: WeatherImpact,
    trendData: any
  ) {
    const startTime = timeWindow.start;
    
    // Calculate historical averages
    const historicalAverage = historicalData.reduce((sum, pattern) => 
      sum + pattern.averageDemand, 0) / Math.max(historicalData.length, 1);
    
    const seasonalFactor = historicalData.reduce((sum, pattern) => 
      sum + pattern.seasonalFactor, 0) / Math.max(historicalData.length, 1);

    // Calculate event impact
    const eventImpact = externalEvents.reduce((sum, event) => {
      const distance = this.calculateDistance(location, event.location);
      const impactFactor = Math.max(0, 1 - (distance / event.impactRadius));
      return sum + (event.demandMultiplier * impactFactor);
    }, 0);

    return {
      // Temporal features
      hourOfDay: startTime.getHours(),
      dayOfWeek: startTime.getDay(),
      dayOfMonth: startTime.getDate(),
      month: startTime.getMonth(),
      isWeekend: startTime.getDay() === 0 || startTime.getDay() === 6,
      isHoliday: this.isHoliday(startTime),
      
      // Location features
      latitude: location.latitude,
      longitude: location.longitude,
      locationDensity: this.getLocationDensity(location),
      businessDistrict: this.isBusinessDistrict(location),
      residentialArea: this.isResidentialArea(location),
      entertainmentZone: this.isEntertainmentZone(location),
      
      // Historical features
      historicalAverage,
      seasonalFactor,
      weeklyTrend: trendData.weeklyTrend || 0,
      monthlyTrend: trendData.monthlyTrend || 0,
      
      // External factors
      weatherImpact: weatherData.demandImpact,
      temperature: weatherData.temperature,
      precipitation: weatherData.precipitation,
      eventImpact,
      eventCount: externalEvents.length,
      
      // Market features
      competitorActivity: trendData.competitorActivity || 0.5,
      marketShare: trendData.marketShare || 0.3,
      
      // Time window features
      timeWindowDuration: (timeWindow.end.getTime() - timeWindow.start.getTime()) / (1000 * 60),
      isRushHour: this.isRushHour(startTime),
      isPeakDay: this.isPeakDay(startTime),
    };
  }

  /**
   * Call ML model for demand forecasting
   */
  private async callForecastingML(features: any) {
    const response = await fetch(`${this.mlModelEndpoint}/forecast-demand`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ML_API_KEY}`,
      },
      body: JSON.stringify({ features }),
    });

    if (response.ok) {
      return await response.json();
    }
    
    throw new Error('ML forecasting API failed');
  }

  /**
   * Rule-based forecasting fallback
   */
  private calculateRuleBasedForecast(features: any) {
    let baseDemand = features.historicalAverage || 10;
    
    // Apply time-based adjustments
    if (features.isRushHour) baseDemand *= 1.5;
    if (features.isWeekend) baseDemand *= 0.8;
    if (features.isHoliday) baseDemand *= 0.6;
    
    // Apply location-based adjustments
    if (features.businessDistrict) baseDemand *= 1.3;
    if (features.entertainmentZone) baseDemand *= 1.2;
    
    // Apply weather adjustments
    baseDemand *= (1 + features.weatherImpact);
    
    // Apply event adjustments
    baseDemand *= (1 + features.eventImpact);
    
    // Apply seasonal adjustments
    baseDemand *= features.seasonalFactor;

    return {
      demand: Math.max(0, Math.round(baseDemand)),
      confidence: 0.6, // Lower confidence for rule-based
      factors: {
        historical: 0.4,
        seasonal: 0.2,
        weather: 0.15,
        events: 0.15,
        trends: 0.1,
      },
    };
  }

  /**
   * Generate recommendations based on forecast
   */
  private async generateRecommendations(
    location: Location,
    prediction: any,
    features: any
  ) {
    const recommendations = {
      driverPositioning: [] as Location[],
      incentiveZones: [] as Array<{
        location: Location;
        radius: number;
        incentiveAmount: number;
      }>,
      surgeAreas: [] as Array<{
        location: Location;
        radius: number;
        expectedMultiplier: number;
      }>,
    };

    // High demand areas - recommend driver positioning
    if (prediction.demand > features.historicalAverage * 1.3) {
      recommendations.driverPositioning = await this.getOptimalDriverPositions(location, prediction.demand);
    }

    // Low supply areas - recommend incentives
    const currentSupply = await this.getDriverAvailability(location);
    if (prediction.demand > currentSupply * 1.5) {
      recommendations.incentiveZones.push({
        location,
        radius: 2, // 2km radius
        incentiveAmount: Math.min(50, prediction.demand * 0.5), // Dynamic incentive
      });
    }

    // Very high demand - recommend surge pricing
    if (prediction.demand > features.historicalAverage * 2) {
      const surgeMultiplier = Math.min(3.0, 1 + (prediction.demand / features.historicalAverage - 1) * 0.5);
      recommendations.surgeAreas.push({
        location,
        radius: 1.5, // 1.5km radius
        expectedMultiplier: surgeMultiplier,
      });
    }

    return recommendations;
  }

  /**
   * Utility methods
   */
  private calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private isRushHour(time: Date): boolean {
    const hour = time.getHours();
    return (hour >= 7 && hour <= 10) || (hour >= 17 && hour <= 20);
  }

  private isPeakDay(time: Date): boolean {
    const day = time.getDay();
    return day >= 1 && day <= 5; // Monday to Friday
  }

  private isHoliday(date: Date): boolean {
    // Would integrate with holiday API
    return false;
  }

  private getLocationDensity(location: Location): number {
    // Would calculate based on POI density, population, etc.
    return 0.7;
  }

  private isBusinessDistrict(location: Location): boolean {
    // Would check against business district boundaries
    return false;
  }

  private isResidentialArea(location: Location): boolean {
    // Would check against residential area data
    return true;
  }

  private isEntertainmentZone(location: Location): boolean {
    // Would check against entertainment venue data
    return false;
  }

  private calculateDemandAdjustment(demandSupplyRatio: number): number {
    if (demandSupplyRatio >= 3.0) return 2.0;
    if (demandSupplyRatio >= 2.0) return 1.5;
    if (demandSupplyRatio >= 1.5) return 1.2;
    if (demandSupplyRatio <= 0.5) return 0.8;
    return 1.0;
  }

  private generateCacheKey(location: Location, timeWindow: { start: Date; end: Date }): string {
    const key = `${location.latitude},${location.longitude}-${timeWindow.start.getTime()}-${timeWindow.end.getTime()}`;
    return Buffer.from(key).toString('base64');
  }

  private getCachedForecast(key: string): DemandForecast | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timeWindow.start.getTime() < this.cacheExpiry) {
      return cached;
    }
    return null;
  }

  private cacheForecast(key: string, forecast: DemandForecast): void {
    this.cache.set(key, forecast);
    
    // Clean up old cache entries
    if (this.cache.size > 200) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  // Placeholder methods for external data sources
  private async getHistoricalData(location: Location, timeWindow: { start: Date; end: Date }): Promise<HistoricalPattern[]> {
    // Would query historical ride data
    return [
      {
        hourOfDay: timeWindow.start.getHours(),
        dayOfWeek: timeWindow.start.getDay(),
        averageDemand: 15,
        peakDemand: 25,
        variance: 5,
        seasonalFactor: 1.1,
      },
    ];
  }

  private async getExternalEvents(location: Location, timeWindow: { start: Date; end: Date }): Promise<ExternalEvent[]> {
    // Would query events API
    return [];
  }

  private async getWeatherForecast(location: Location, timeWindow: { start: Date; end: Date }): Promise<WeatherImpact> {
    // Would query weather API
    return {
      condition: 'clear',
      temperature: 25,
      precipitation: 0,
      windSpeed: 5,
      visibility: 10,
      demandImpact: 0,
      durationImpact: 1.0,
    };
  }

  private async getTrendData(location: Location): Promise<any> {
    // Would analyze recent trends
    return {
      weeklyTrend: 0.05,
      monthlyTrend: 0.1,
      competitorActivity: 0.5,
      marketShare: 0.3,
    };
  }

  private async getCurrentRideRequests(location: Location): Promise<number> {
    // Would query current ride requests in area
    return 5;
  }

  private async getDriverAvailability(location: Location): Promise<number> {
    // Would query available drivers in area
    return 8;
  }

  private async getRecentDemandTrend(location: Location): Promise<'increasing' | 'decreasing' | 'stable'> {
    // Would analyze recent demand patterns
    return 'stable';
  }

  private async getOptimalDriverPositions(location: Location, demand: number): Promise<Location[]> {
    // Would calculate optimal positioning based on demand hotspots
    return [
      {
        latitude: location.latitude + 0.01,
        longitude: location.longitude + 0.01,
      },
      {
        latitude: location.latitude - 0.01,
        longitude: location.longitude - 0.01,
      },
    ];
  }
}

// Export singleton instance
export const demandForecastingService = new DemandForecastingService();
export default demandForecastingService;
