// AI-Enhanced ETA Prediction Service
import { apmService } from '../monitoring/apm';
import { Location } from './routeOptimization';

export interface ETARequest {
  origin: Location;
  destination: Location;
  driverLocation?: Location;
  vehicleType: 'bike' | 'scooter' | 'car';
  requestTime: Date;
  rideId?: string;
  driverId?: string;
}

export interface ETAPrediction {
  estimatedArrival: Date;
  estimatedDuration: number; // in minutes
  confidence: number; // 0-1 scale
  factors: {
    distance: number;
    traffic: number;
    weather: number;
    driverBehavior: number;
    historical: number;
  };
  breakdown: {
    pickupETA: number; // minutes to reach pickup
    rideETA: number; // minutes for the ride
    totalETA: number; // total minutes
  };
  alternatives: Array<{
    route: string;
    eta: number;
    confidence: number;
  }>;
  realTimeUpdates: {
    lastUpdated: Date;
    nextUpdate: Date;
    updateFrequency: number; // seconds
  };
}

export interface TrafficCondition {
  segmentId: string;
  location: Location;
  congestionLevel: number; // 0-1 scale
  averageSpeed: number; // km/h
  incidents: Array<{
    type: 'accident' | 'construction' | 'closure';
    severity: 'low' | 'medium' | 'high';
    estimatedClearTime?: Date;
  }>;
  historicalPattern: {
    typicalSpeed: number;
    variance: number;
  };
}

export interface DriverBehaviorProfile {
  driverId: string;
  averageSpeed: number;
  accelerationPattern: 'aggressive' | 'moderate' | 'conservative';
  routePreference: 'fastest' | 'shortest' | 'familiar';
  punctualityScore: number; // 0-1 scale
  experienceLevel: 'novice' | 'experienced' | 'expert';
  localKnowledge: number; // 0-1 scale for area familiarity
}

class ETAPredictionService {
  private mlModelEndpoint: string;
  private cache: Map<string, ETAPrediction> = new Map();
  private cacheExpiry: number = 2 * 60 * 1000; // 2 minutes
  private activeTracking: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.mlModelEndpoint = process.env.ETA_ML_ENDPOINT || 'http://localhost:8003';
  }

  /**
   * Predict ETA for a ride request
   */
  async predictETA(request: ETARequest): Promise<ETAPrediction> {
    const timer = apmService.startTimer('eta_prediction');
    
    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(request);
      const cachedETA = this.getCachedETA(cacheKey);
      if (cachedETA) {
        timer.end(true);
        return cachedETA;
      }

      // Gather input data
      const trafficData = await this.getTrafficConditions(request.origin, request.destination);
      const weatherData = await this.getWeatherConditions(request.origin, request.requestTime);
      const driverProfile = request.driverId ? await this.getDriverProfile(request.driverId) : null;
      const historicalData = await this.getHistoricalETAData(request);

      // Extract features for ML model
      const features = this.extractETAFeatures(request, trafficData, weatherData, driverProfile, historicalData);

      // Get ML prediction
      let prediction;
      try {
        prediction = await this.callETAML(features);
      } catch (error) {
        console.error('ML ETA prediction failed:', error);
        prediction = this.calculateRuleBasedETA(features, request);
      }

      // Calculate breakdown
      const breakdown = this.calculateETABreakdown(request, prediction, trafficData);

      // Generate alternatives
      const alternatives = await this.generateETAAlternatives(request, features);

      const etaPrediction: ETAPrediction = {
        estimatedArrival: new Date(request.requestTime.getTime() + (prediction.totalMinutes * 60 * 1000)),
        estimatedDuration: prediction.totalMinutes,
        confidence: prediction.confidence,
        factors: prediction.factors,
        breakdown,
        alternatives,
        realTimeUpdates: {
          lastUpdated: new Date(),
          nextUpdate: new Date(Date.now() + 30000), // 30 seconds
          updateFrequency: 30,
        },
      };

      // Cache the result
      this.cacheETA(cacheKey, etaPrediction);

      // Start real-time tracking if ride is active
      if (request.rideId) {
        this.startRealTimeTracking(request.rideId, request);
      }

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'eta_predicted',
        value: prediction.totalMinutes,
        timestamp: Date.now(),
        properties: {
          confidence: prediction.confidence,
          vehicleType: request.vehicleType,
        },
      });

      timer.end(true);
      return etaPrediction;

    } catch (error) {
      timer.end(false);
      console.error('ETA prediction failed:', error);
      throw error;
    }
  }

  /**
   * Update ETA in real-time for active rides
   */
  async updateRealTimeETA(rideId: string, currentLocation: Location): Promise<ETAPrediction> {
    try {
      // Get original request from tracking
      const originalRequest = await this.getOriginalRequest(rideId);
      if (!originalRequest) {
        throw new Error('Original request not found for ride');
      }

      // Update request with current location
      const updatedRequest: ETARequest = {
        ...originalRequest,
        origin: currentLocation,
        requestTime: new Date(),
      };

      // Get fresh prediction
      const updatedETA = await this.predictETA(updatedRequest);

      // Notify subscribers of ETA update
      await this.notifyETAUpdate(rideId, updatedETA);

      return updatedETA;

    } catch (error) {
      console.error('Real-time ETA update failed:', error);
      throw error;
    }
  }

  /**
   * Extract features for ML model
   */
  private extractETAFeatures(
    request: ETARequest,
    trafficData: TrafficCondition[],
    weatherData: any,
    driverProfile: DriverBehaviorProfile | null,
    historicalData: any
  ) {
    const distance = this.calculateDistance(request.origin, request.destination);
    const currentTime = request.requestTime;

    // Calculate average traffic conditions
    const avgCongestion = trafficData.reduce((sum, segment) => sum + segment.congestionLevel, 0) / Math.max(trafficData.length, 1);
    const avgSpeed = trafficData.reduce((sum, segment) => sum + segment.averageSpeed, 0) / Math.max(trafficData.length, 1);

    return {
      // Basic route features
      distance,
      straightLineDistance: distance * 0.8, // Approximate
      routeComplexity: this.calculateRouteComplexity(request.origin, request.destination),
      
      // Temporal features
      hourOfDay: currentTime.getHours(),
      dayOfWeek: currentTime.getDay(),
      isWeekend: currentTime.getDay() === 0 || currentTime.getDay() === 6,
      isRushHour: this.isRushHour(currentTime),
      
      // Traffic features
      avgCongestionLevel: avgCongestion,
      avgTrafficSpeed: avgSpeed,
      incidentCount: trafficData.reduce((sum, segment) => sum + segment.incidents.length, 0),
      trafficVariance: this.calculateTrafficVariance(trafficData),
      
      // Weather features
      weatherCondition: weatherData.condition || 'clear',
      temperature: weatherData.temperature || 25,
      precipitation: weatherData.precipitation || 0,
      visibility: weatherData.visibility || 10,
      windSpeed: weatherData.windSpeed || 0,
      
      // Vehicle features
      vehicleType: request.vehicleType,
      vehicleSpeedFactor: this.getVehicleSpeedFactor(request.vehicleType),
      
      // Driver features
      driverExperience: driverProfile?.experienceLevel || 'experienced',
      driverPunctuality: driverProfile?.punctualityScore || 0.8,
      driverLocalKnowledge: driverProfile?.localKnowledge || 0.7,
      driverSpeedPattern: driverProfile?.averageSpeed || 25,
      
      // Historical features
      historicalAvgETA: historicalData.averageETA || distance / 25 * 60, // fallback calculation
      historicalVariance: historicalData.variance || 5,
      similarRouteETA: historicalData.similarRoutes || [],
      
      // Location features
      originDensity: this.getLocationDensity(request.origin),
      destinationDensity: this.getLocationDensity(request.destination),
      businessDistrict: this.isBusinessDistrict(request.origin) || this.isBusinessDistrict(request.destination),
      
      // Time-sensitive features
      timeToPickup: request.driverLocation ? 
        this.calculateDistance(request.driverLocation, request.origin) / 25 * 60 : 5, // default 5 min
    };
  }

  /**
   * Call ML model for ETA prediction
   */
  private async callETAML(features: any) {
    const response = await fetch(`${this.mlModelEndpoint}/predict-eta`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ML_API_KEY}`,
      },
      body: JSON.stringify({ features }),
    });

    if (response.ok) {
      return await response.json();
    }
    
    throw new Error('ML ETA prediction API failed');
  }

  /**
   * Rule-based ETA calculation fallback
   */
  private calculateRuleBasedETA(features: any, request: ETARequest) {
    let baseTime = features.distance / features.avgTrafficSpeed * 60; // minutes
    
    // Apply traffic adjustments
    if (features.avgCongestionLevel > 0.7) baseTime *= 1.4;
    else if (features.avgCongestionLevel > 0.5) baseTime *= 1.2;
    else if (features.avgCongestionLevel > 0.3) baseTime *= 1.1;
    
    // Apply weather adjustments
    if (features.precipitation > 5) baseTime *= 1.3;
    else if (features.precipitation > 0) baseTime *= 1.1;
    
    if (features.visibility < 5) baseTime *= 1.2;
    
    // Apply time-of-day adjustments
    if (features.isRushHour) baseTime *= 1.3;
    if (features.isWeekend) baseTime *= 0.9;
    
    // Apply vehicle type adjustments
    const vehicleFactors = {
      bike: 0.8, // Faster in traffic
      scooter: 0.9,
      car: 1.0,
    };
    baseTime *= vehicleFactors[request.vehicleType] || 1.0;
    
    // Apply driver experience adjustments
    if (features.driverExperience === 'expert') baseTime *= 0.9;
    else if (features.driverExperience === 'novice') baseTime *= 1.1;
    
    // Add pickup time
    const totalTime = baseTime + features.timeToPickup;

    return {
      totalMinutes: Math.round(totalTime),
      confidence: 0.7, // Lower confidence for rule-based
      factors: {
        distance: 0.4,
        traffic: 0.3,
        weather: 0.1,
        driverBehavior: 0.1,
        historical: 0.1,
      },
    };
  }

  /**
   * Calculate ETA breakdown
   */
  private calculateETABreakdown(
    request: ETARequest,
    prediction: any,
    trafficData: TrafficCondition[]
  ) {
    const pickupTime = request.driverLocation ? 
      this.calculateDistance(request.driverLocation, request.origin) / 25 * 60 : 5;
    
    const rideTime = prediction.totalMinutes - pickupTime;
    
    return {
      pickupETA: Math.round(pickupTime),
      rideETA: Math.round(rideTime),
      totalETA: Math.round(prediction.totalMinutes),
    };
  }

  /**
   * Generate alternative ETA estimates
   */
  private async generateETAAlternatives(request: ETARequest, features: any) {
    const alternatives = [];
    
    // Optimistic estimate (best case scenario)
    const optimisticTime = features.distance / 30 * 60; // 30 km/h average
    alternatives.push({
      route: 'optimistic',
      eta: Math.round(optimisticTime),
      confidence: 0.2,
    });
    
    // Conservative estimate (worst case scenario)
    const conservativeTime = features.distance / 15 * 60; // 15 km/h average
    alternatives.push({
      route: 'conservative',
      eta: Math.round(conservativeTime),
      confidence: 0.8,
    });
    
    // Historical average
    alternatives.push({
      route: 'historical',
      eta: Math.round(features.historicalAvgETA),
      confidence: 0.6,
    });
    
    return alternatives;
  }

  /**
   * Start real-time tracking for active ride
   */
  private startRealTimeTracking(rideId: string, request: ETARequest) {
    // Clear existing tracking
    if (this.activeTracking.has(rideId)) {
      clearInterval(this.activeTracking.get(rideId)!);
    }

    // Start new tracking interval
    const trackingInterval = setInterval(async () => {
      try {
        // Get current driver location (would come from GPS tracking)
        const currentLocation = await this.getCurrentDriverLocation(rideId);
        if (currentLocation) {
          await this.updateRealTimeETA(rideId, currentLocation);
        }
      } catch (error) {
        console.error('Real-time tracking update failed:', error);
      }
    }, 30000); // Update every 30 seconds

    this.activeTracking.set(rideId, trackingInterval);
  }

  /**
   * Stop real-time tracking
   */
  stopRealTimeTracking(rideId: string) {
    if (this.activeTracking.has(rideId)) {
      clearInterval(this.activeTracking.get(rideId)!);
      this.activeTracking.delete(rideId);
    }
  }

  /**
   * Utility methods
   */
  private calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private isRushHour(time: Date): boolean {
    const hour = time.getHours();
    return (hour >= 7 && hour <= 10) || (hour >= 17 && hour <= 20);
  }

  private calculateRouteComplexity(origin: Location, destination: Location): number {
    // Simplified complexity calculation
    const distance = this.calculateDistance(origin, destination);
    return Math.min(1.0, distance / 20); // Normalize to 0-1 scale
  }

  private calculateTrafficVariance(trafficData: TrafficCondition[]): number {
    if (trafficData.length === 0) return 0;
    
    const speeds = trafficData.map(segment => segment.averageSpeed);
    const avgSpeed = speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
    const variance = speeds.reduce((sum, speed) => sum + Math.pow(speed - avgSpeed, 2), 0) / speeds.length;
    
    return Math.sqrt(variance);
  }

  private getVehicleSpeedFactor(vehicleType: string): number {
    const factors = {
      bike: 1.2, // Can navigate traffic better
      scooter: 1.1,
      car: 1.0,
    };
    return factors[vehicleType] || 1.0;
  }

  private getLocationDensity(location: Location): number {
    // Would calculate based on POI density, population, etc.
    return 0.7;
  }

  private isBusinessDistrict(location: Location): boolean {
    // Would check against business district boundaries
    return false;
  }

  private generateCacheKey(request: ETARequest): string {
    const key = `${request.origin.latitude},${request.origin.longitude}-${request.destination.latitude},${request.destination.longitude}-${request.vehicleType}-${Math.floor(Date.now() / 120000)}`;
    return Buffer.from(key).toString('base64');
  }

  private getCachedETA(key: string): ETAPrediction | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.realTimeUpdates.lastUpdated.getTime() < this.cacheExpiry) {
      return cached;
    }
    return null;
  }

  private cacheETA(key: string, eta: ETAPrediction): void {
    this.cache.set(key, eta);
    
    // Clean up old cache entries
    if (this.cache.size > 300) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  // Placeholder methods for external data sources
  private async getTrafficConditions(origin: Location, destination: Location): Promise<TrafficCondition[]> {
    // Would integrate with traffic APIs
    return [
      {
        segmentId: 'segment_1',
        location: origin,
        congestionLevel: 0.6,
        averageSpeed: 25,
        incidents: [],
        historicalPattern: { typicalSpeed: 30, variance: 5 },
      },
    ];
  }

  private async getWeatherConditions(location: Location, time: Date): Promise<any> {
    // Would integrate with weather APIs
    return {
      condition: 'clear',
      temperature: 25,
      precipitation: 0,
      visibility: 10,
      windSpeed: 5,
    };
  }

  private async getDriverProfile(driverId: string): Promise<DriverBehaviorProfile | null> {
    // Would query driver behavior data
    return {
      driverId,
      averageSpeed: 28,
      accelerationPattern: 'moderate',
      routePreference: 'fastest',
      punctualityScore: 0.85,
      experienceLevel: 'experienced',
      localKnowledge: 0.8,
    };
  }

  private async getHistoricalETAData(request: ETARequest): Promise<any> {
    // Would query historical ETA data
    return {
      averageETA: 20,
      variance: 5,
      similarRoutes: [],
    };
  }

  private async getOriginalRequest(rideId: string): Promise<ETARequest | null> {
    // Would retrieve original request from database
    return null;
  }

  private async getCurrentDriverLocation(rideId: string): Promise<Location | null> {
    // Would get current GPS location from driver tracking
    return null;
  }

  private async notifyETAUpdate(rideId: string, eta: ETAPrediction): Promise<void> {
    // Would send real-time updates via WebSocket
    console.log(`ETA updated for ride ${rideId}: ${eta.estimatedDuration} minutes`);
  }
}

// Export singleton instance
export const etaPredictionService = new ETAPredictionService();
export default etaPredictionService;
