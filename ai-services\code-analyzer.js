/**
 * AI-Powered Code Analysis Service
 * Integrates with Ollama for intelligent code analysis and suggestions
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

class CodeAnalyzer {
  constructor(ollamaUrl = 'http://localhost:11434') {
    this.ollamaUrl = ollamaUrl;
    this.model = 'codellama:7b-instruct';
    this.maxTokens = 4096;
  }

  /**
   * Initialize Ollama model if not already available
   */
  async initializeModel() {
    try {
      // Check if model is available
      const response = await axios.get(`${this.ollamaUrl}/api/tags`);
      const models = response.data.models || [];
      
      const hasCodeLlama = models.some(model => 
        model.name.includes('codellama') || model.name.includes('code')
      );

      if (!hasCodeLlama) {
        console.log('🤖 Pulling CodeLlama model for code analysis...');
        await this.pullModel();
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Ollama model:', error.message);
      return false;
    }
  }

  /**
   * Pull CodeLlama model from Ollama
   */
  async pullModel() {
    try {
      const response = await axios.post(`${this.ollamaUrl}/api/pull`, {
        name: this.model
      });
      console.log('✅ CodeLlama model pulled successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to pull model:', error.message);
      throw error;
    }
  }

  /**
   * Analyze code file for patterns, issues, and suggestions
   */
  async analyzeCode(filePath, codeContent) {
    const prompt = `
Analyze this ${path.extname(filePath)} code and provide:
1. Code quality assessment (1-10 scale)
2. Potential issues or bugs
3. Performance optimization suggestions
4. Security concerns
5. Refactoring recommendations
6. Best practices violations

Code to analyze:
\`\`\`${path.extname(filePath).slice(1)}
${codeContent}
\`\`\`

Provide response in JSON format:
{
  "quality_score": number,
  "issues": [{"type": "string", "description": "string", "line": number, "severity": "low|medium|high"}],
  "optimizations": [{"description": "string", "impact": "low|medium|high"}],
  "security": [{"issue": "string", "recommendation": "string", "severity": "low|medium|high"}],
  "refactoring": [{"suggestion": "string", "benefit": "string"}],
  "best_practices": [{"violation": "string", "fix": "string"}]
}`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.1,
          top_p: 0.9,
          max_tokens: this.maxTokens
        }
      });

      const analysis = this.parseAnalysisResponse(response.data.response);
      return {
        file: filePath,
        timestamp: new Date().toISOString(),
        analysis
      };
    } catch (error) {
      console.error('❌ Code analysis failed:', error.message);
      throw error;
    }
  }

  /**
   * Generate comprehensive tests for React components
   */
  async generateTests(componentPath, componentCode) {
    const prompt = `
Generate comprehensive Jest tests for this React component. Include:
1. Component rendering tests
2. Props validation tests
3. Event handler tests
4. Edge case tests
5. Accessibility tests
6. Performance tests

Component code:
\`\`\`jsx
${componentCode}
\`\`\`

Generate complete test file with imports, describe blocks, and test cases.
Use @testing-library/react and jest-dom matchers.
Include setup and teardown if needed.
Test file should be production-ready.`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.2,
          top_p: 0.9,
          max_tokens: this.maxTokens
        }
      });

      return {
        component: componentPath,
        testFile: componentPath.replace(/\.(tsx?|jsx?)$/, '.test.$1'),
        testCode: this.extractCodeFromResponse(response.data.response),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Test generation failed:', error.message);
      throw error;
    }
  }

  /**
   * Analyze git diff and generate PR description
   */
  async generatePRDescription(gitDiff, branchName) {
    const prompt = `
Analyze this git diff and generate a comprehensive PR description:

Branch: ${branchName}
Git Diff:
\`\`\`diff
${gitDiff}
\`\`\`

Generate PR description with:
1. Clear title
2. Summary of changes
3. Impact analysis
4. Testing recommendations
5. Breaking changes (if any)
6. Checklist items

Format as markdown for GitHub PR.`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.3,
          top_p: 0.9,
          max_tokens: this.maxTokens
        }
      });

      return {
        title: this.extractTitleFromResponse(response.data.response),
        description: response.data.response,
        branch: branchName,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ PR description generation failed:', error.message);
      throw error;
    }
  }

  /**
   * Suggest code refactoring improvements
   */
  async suggestRefactoring(codeContent, filePath) {
    const prompt = `
Analyze this code and suggest specific refactoring improvements:

File: ${filePath}
Code:
\`\`\`${path.extname(filePath).slice(1)}
${codeContent}
\`\`\`

Provide:
1. Extract function opportunities
2. Remove code duplication
3. Improve naming conventions
4. Simplify complex logic
5. Apply design patterns
6. Improve error handling

For each suggestion, provide:
- Current code snippet
- Improved code snippet
- Explanation of benefits
- Estimated effort (low/medium/high)`;

    try {
      const response = await axios.post(`${this.ollamaUrl}/api/generate`, {
        model: this.model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.2,
          top_p: 0.9,
          max_tokens: this.maxTokens
        }
      });

      return {
        file: filePath,
        suggestions: this.parseRefactoringSuggestions(response.data.response),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Refactoring suggestion failed:', error.message);
      throw error;
    }
  }

  /**
   * Parse analysis response and extract JSON
   */
  parseAnalysisResponse(response) {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback: create structured response from text
      return this.createFallbackAnalysis(response);
    } catch (error) {
      console.error('❌ Failed to parse analysis response:', error.message);
      return this.createFallbackAnalysis(response);
    }
  }

  /**
   * Extract code from AI response
   */
  extractCodeFromResponse(response) {
    const codeMatch = response.match(/```[\w]*\n([\s\S]*?)\n```/);
    return codeMatch ? codeMatch[1] : response;
  }

  /**
   * Extract title from PR description response
   */
  extractTitleFromResponse(response) {
    const titleMatch = response.match(/^#\s*(.+)$/m);
    return titleMatch ? titleMatch[1] : 'AI-Generated PR';
  }

  /**
   * Create fallback analysis structure
   */
  createFallbackAnalysis(response) {
    return {
      quality_score: 7,
      issues: [],
      optimizations: [],
      security: [],
      refactoring: [],
      best_practices: [],
      raw_response: response
    };
  }

  /**
   * Parse refactoring suggestions from response
   */
  parseRefactoringSuggestions(response) {
    // Simple parsing - can be enhanced with more sophisticated NLP
    const suggestions = [];
    const sections = response.split(/\d+\./);
    
    sections.forEach((section, index) => {
      if (section.trim() && index > 0) {
        suggestions.push({
          id: index,
          description: section.trim(),
          effort: 'medium' // Default effort level
        });
      }
    });

    return suggestions;
  }

  /**
   * Health check for Ollama service
   */
  async healthCheck() {
    try {
      const response = await axios.get(`${this.ollamaUrl}/api/tags`);
      return {
        status: 'healthy',
        models: response.data.models?.length || 0,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = CodeAnalyzer;
