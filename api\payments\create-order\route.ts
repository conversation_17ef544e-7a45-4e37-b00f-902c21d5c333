import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { RazorpayService } from '@/lib/services/razorpayService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { amount, currency, rideId, description, notes } = body;

    // Validate required fields
    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    // Convert amount to paise (Razorpay expects amount in paise)
    const amountInPaise = Math.round(amount * 100);

    // Create payment order
    const orderResult = await RazorpayService.createOrder({
      amount: amountInPaise,
      currency: currency || 'INR',
      userId: decoded.userId,
      rideId,
      description: description || (rideId ? 'Ride payment' : 'Wallet top-up'),
      notes: notes || {},
    });

    return NextResponse.json({
      success: true,
      message: 'Payment order created successfully',
      data: orderResult,
    });

  } catch (error) {
    console.error('Create order error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create payment order',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
