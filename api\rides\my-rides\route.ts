import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Ride } from '@/lib/models/Ride';
import { authenticateRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '20');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;

    // Build query based on user role
    let query: any = {};
    
    if (user.role === 'rider') {
      query.riderId = user.userId;
    } else if (user.role === 'driver') {
      query.driverId = user.userId;
    }

    // Filter by status if provided
    if (status) {
      query.status = status;
    }

    // Fetch rides with pagination
    const rides = await Ride.find(query)
      .populate('riderId', 'firstName lastName phone profileImage')
      .populate('driverId', 'firstName lastName phone profileImage driverProfile')
      .sort({ requestedAt: -1 })
      .limit(limit)
      .skip(skip);

    // Get total count for pagination
    const totalRides = await Ride.countDocuments(query);
    const totalPages = Math.ceil(totalRides / limit);

    // Format rides for response
    const formattedRides = rides.map(ride => ({
      _id: ride._id,
      status: ride.status,
      pickupLocation: ride.pickupLocation,
      dropoffLocation: ride.dropoffLocation,
      estimatedDistance: ride.estimatedDistance,
      estimatedDuration: ride.estimatedDuration,
      actualDistance: ride.actualDistance,
      actualDuration: ride.actualDuration,
      finalAmount: ride.finalAmount,
      rideType: ride.rideType,
      paymentMethod: ride.paymentMethod,
      paymentStatus: ride.paymentStatus,
      specialInstructions: ride.specialInstructions,
      requestedAt: ride.requestedAt,
      acceptedAt: ride.acceptedAt,
      startedAt: ride.startedAt,
      completedAt: ride.completedAt,
      cancelledAt: ride.cancelledAt,
      cancelledBy: ride.cancelledBy,
      cancellationFee: ride.cancellationFee,
      driverRating: ride.driverRating,
      riderRating: ride.riderRating,
      riderFeedback: ride.riderFeedback,
      driverFeedback: ride.driverFeedback,
      rider: ride.riderId ? {
        _id: ride.riderId._id,
        firstName: ride.riderId.firstName,
        lastName: ride.riderId.lastName,
        phone: ride.riderId.phone,
        profileImage: ride.riderId.profileImage,
      } : null,
      driver: ride.driverId ? {
        _id: ride.driverId._id,
        firstName: ride.driverId.firstName,
        lastName: ride.driverId.lastName,
        phone: ride.driverId.phone,
        profileImage: ride.driverId.profileImage,
        driverProfile: {
          rating: ride.driverId.driverProfile?.rating || 5.0,
          totalRides: ride.driverId.driverProfile?.totalRides || 0,
          vehicleModel: ride.driverId.driverProfile?.vehicleModel,
          vehicleColor: ride.driverId.driverProfile?.vehicleColor,
        },
      } : null,
    }));

    // Calculate statistics
    const stats = {
      totalRides,
      activeRides: await Ride.countDocuments({
        ...query,
        status: { $in: ['pending', 'accepted', 'in_progress'] }
      }),
      completedRides: await Ride.countDocuments({
        ...query,
        status: 'completed'
      }),
      cancelledRides: await Ride.countDocuments({
        ...query,
        status: 'cancelled'
      }),
    };

    // Add role-specific stats
    if (user.role === 'rider') {
      const totalSpent = await Ride.aggregate([
        { $match: { riderId: user.userId, status: 'completed' } },
        { $group: { _id: null, total: { $sum: '$finalAmount' } } }
      ]);
      stats.totalSpent = totalSpent[0]?.total || 0;
    } else if (user.role === 'driver') {
      const totalEarnings = await Ride.aggregate([
        { $match: { driverId: user.userId, status: 'completed' } },
        { $group: { _id: null, total: { $sum: { $multiply: ['$finalAmount', 0.8] } } } }
      ]);
      stats.totalEarnings = totalEarnings[0]?.total || 0;
    }

    return NextResponse.json({
      rides: formattedRides,
      pagination: {
        currentPage: page,
        totalPages,
        totalRides,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      },
      stats,
    });

  } catch (error) {
    console.error('Get my rides error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
