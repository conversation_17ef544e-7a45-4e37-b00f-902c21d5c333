'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useWebSocket } from '@/lib/hooks/useWebSocket';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MapPin, 
  Navigation, 
  AlertTriangle, 
  Wifi, 
  WifiOff,
  Target,
  Clock
} from 'lucide-react';

interface LocationTrackerProps {
  rideId?: string;
  isActive?: boolean;
  showMap?: boolean;
  onLocationUpdate?: (location: GeolocationPosition) => void;
}

interface LocationState {
  latitude: number;
  longitude: number;
  accuracy: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export default function LocationTracker({ 
  rideId, 
  isActive = false, 
  showMap = true,
  onLocationUpdate 
}: LocationTrackerProps) {
  const { user } = useAuth();
  const { updateDriverLocation, isConnected } = useWebSocket();
  
  const [currentLocation, setCurrentLocation] = useState<LocationState | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [error, setError] = useState<string>('');
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  
  const watchIdRef = useRef<number | null>(null);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check geolocation permission status
  useEffect(() => {
    if ('permissions' in navigator) {
      navigator.permissions.query({ name: 'geolocation' }).then((result) => {
        setPermissionStatus(result.state);
        
        result.addEventListener('change', () => {
          setPermissionStatus(result.state);
        });
      });
    }
  }, []);

  // Start/stop tracking based on isActive prop
  useEffect(() => {
    if (isActive && user?.role === 'driver') {
      startTracking();
    } else {
      stopTracking();
    }

    return () => stopTracking();
  }, [isActive, user?.role]);

  const startTracking = async () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    setError('');
    setIsTracking(true);

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 5000 // Cache position for 5 seconds
    };

    // Get initial position
    navigator.geolocation.getCurrentPosition(
      (position) => {
        handleLocationUpdate(position);
      },
      (error) => {
        handleLocationError(error);
      },
      options
    );

    // Start watching position
    watchIdRef.current = navigator.geolocation.watchPosition(
      (position) => {
        handleLocationUpdate(position);
      },
      (error) => {
        handleLocationError(error);
      },
      options
    );

    // Set up periodic updates for drivers (every 10 seconds)
    if (user?.role === 'driver') {
      updateIntervalRef.current = setInterval(() => {
        if (currentLocation && isConnected) {
          updateDriverLocation({
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            heading: currentLocation.heading,
            speed: currentLocation.speed,
            accuracy: currentLocation.accuracy,
            timestamp: Date.now()
          });
        }
      }, 10000);
    }
  };

  const stopTracking = () => {
    setIsTracking(false);
    
    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
    }

    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
    }
  };

  const handleLocationUpdate = (position: GeolocationPosition) => {
    const locationData: LocationState = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      heading: position.coords.heading || undefined,
      speed: position.coords.speed || undefined,
      timestamp: position.timestamp
    };

    setCurrentLocation(locationData);
    setLastUpdateTime(new Date());
    setError('');

    // Call external callback if provided
    onLocationUpdate?.(position);

    // Send to WebSocket for drivers
    if (user?.role === 'driver' && isConnected) {
      updateDriverLocation({
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        heading: locationData.heading,
        speed: locationData.speed,
        accuracy: locationData.accuracy,
        timestamp: Date.now()
      });
    }
  };

  const handleLocationError = (error: GeolocationPositionError) => {
    let errorMessage = 'Location access failed';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'Location access denied by user';
        setPermissionStatus('denied');
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'Location information unavailable';
        break;
      case error.TIMEOUT:
        errorMessage = 'Location request timed out';
        break;
    }
    
    setError(errorMessage);
    setIsTracking(false);
  };

  const requestLocationPermission = async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000
        });
      });
      
      setPermissionStatus('granted');
      handleLocationUpdate(position);
    } catch (error) {
      handleLocationError(error as GeolocationPositionError);
    }
  };

  const formatAccuracy = (accuracy: number): string => {
    if (accuracy < 10) return 'High';
    if (accuracy < 50) return 'Medium';
    return 'Low';
  };

  const formatSpeed = (speed?: number): string => {
    if (!speed) return 'Stationary';
    const kmh = speed * 3.6; // Convert m/s to km/h
    return `${kmh.toFixed(1)} km/h`;
  };

  const getLocationStatusColor = () => {
    if (!isTracking) return 'bg-gray-100 text-gray-800';
    if (error) return 'bg-red-100 text-red-800';
    if (currentLocation && currentLocation.accuracy < 10) return 'bg-green-100 text-green-800';
    if (currentLocation && currentLocation.accuracy < 50) return 'bg-yellow-100 text-yellow-800';
    return 'bg-orange-100 text-orange-800';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              Location Tracking
            </CardTitle>
            <CardDescription>
              {user?.role === 'driver' ? 'Share your location with riders' : 'Track your ride location'}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getLocationStatusColor()}>
              {isTracking ? (
                <>
                  <Target className="w-3 h-3 mr-1" />
                  Active
                </>
              ) : (
                'Inactive'
              )}
            </Badge>
            <Badge variant="outline" className={isConnected ? 'text-green-600' : 'text-red-600'}>
              {isConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {permissionStatus === 'denied' && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Location access is required for tracking. Please enable location permissions in your browser settings.
            </AlertDescription>
          </Alert>
        )}

        {permissionStatus === 'prompt' && (
          <div className="text-center">
            <Button onClick={requestLocationPermission}>
              <Navigation className="w-4 h-4 mr-2" />
              Enable Location Access
            </Button>
          </div>
        )}

        {currentLocation && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Coordinates</p>
                <p className="text-xs text-gray-600">
                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Accuracy</p>
                <p className="text-xs text-gray-600">
                  {formatAccuracy(currentLocation.accuracy)} ({currentLocation.accuracy.toFixed(0)}m)
                </p>
              </div>
            </div>

            {currentLocation.speed !== undefined && (
              <div className="space-y-1">
                <p className="text-sm font-medium">Speed</p>
                <p className="text-xs text-gray-600">{formatSpeed(currentLocation.speed)}</p>
              </div>
            )}

            {lastUpdateTime && (
              <div className="flex items-center space-x-1 text-xs text-gray-500">
                <Clock className="w-3 h-3" />
                <span>Last updated: {lastUpdateTime.toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        )}

        {user?.role === 'driver' && (
          <div className="flex space-x-2">
            {!isTracking ? (
              <Button onClick={startTracking} className="flex-1">
                <Target className="w-4 h-4 mr-2" />
                Start Tracking
              </Button>
            ) : (
              <Button onClick={stopTracking} variant="outline" className="flex-1">
                <Target className="w-4 h-4 mr-2" />
                Stop Tracking
              </Button>
            )}
          </div>
        )}

        {showMap && currentLocation && (
          <div className="mt-4">
            <div className="bg-gray-100 rounded-lg p-4 text-center">
              <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600">
                Map integration would be displayed here
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Integrate with Google Maps, Mapbox, or similar service
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
