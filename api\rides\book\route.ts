import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import connectDB from '@/lib/mongodb';
import { Ride } from '@/lib/models/Ride';
import { User } from '@/lib/models/User';
import { workflowManager } from '@/lib/n8n/workflowManager';
import { WorkflowTrigger } from '@/lib/n8n/config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Helper function to calculate fare
function calculateFare(distance: number, duration: number, rideType: string = 'standard') {
  const baseFare = rideType === 'premium' ? 50 : 30;
  const distanceRate = rideType === 'premium' ? 15 : 10; // per km
  const timeRate = rideType === 'premium' ? 2 : 1.5; // per minute
  
  const distanceFare = distance * distanceRate;
  const timeFare = duration * timeRate;
  const totalFare = baseFare + distanceFare + timeFare;
  
  return {
    baseFare,
    distanceFare,
    timeFare,
    totalFare,
  };
}

// Helper function to verify JWT token
function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  
  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch {
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Verify authentication
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is a rider
    const user = await User.findById(decoded.userId);
    if (!user || user.role !== 'rider') {
      return NextResponse.json(
        { error: 'Only riders can book rides' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      pickupLocation,
      dropoffLocation,
      estimatedDistance,
      estimatedDuration,
      rideType = 'standard',
      paymentMethod,
      specialInstructions,
      isScheduled = false,
      scheduledTime,
    } = body;

    // Validation
    if (!pickupLocation || !dropoffLocation || !estimatedDistance || !estimatedDuration || !paymentMethod) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    if (!pickupLocation.address || !pickupLocation.coordinates || 
        !dropoffLocation.address || !dropoffLocation.coordinates) {
      return NextResponse.json(
        { error: 'Invalid location data' },
        { status: 400 }
      );
    }

    if (!['cash', 'card', 'wallet', 'upi'].includes(paymentMethod)) {
      return NextResponse.json(
        { error: 'Invalid payment method' },
        { status: 400 }
      );
    }

    if (!['standard', 'premium', 'shared'].includes(rideType)) {
      return NextResponse.json(
        { error: 'Invalid ride type' },
        { status: 400 }
      );
    }

    // Calculate fare
    const fareDetails = calculateFare(estimatedDistance, estimatedDuration, rideType);
    
    // Apply any discounts (can be enhanced later)
    const discountAmount = 0;
    const finalAmount = fareDetails.totalFare - discountAmount;

    // Create ride object
    const rideData = {
      riderId: decoded.userId,
      pickupLocation: {
        address: pickupLocation.address,
        coordinates: {
          latitude: pickupLocation.coordinates.latitude,
          longitude: pickupLocation.coordinates.longitude,
        },
        landmark: pickupLocation.landmark,
      },
      dropoffLocation: {
        address: dropoffLocation.address,
        coordinates: {
          latitude: dropoffLocation.coordinates.latitude,
          longitude: dropoffLocation.coordinates.longitude,
        },
        landmark: dropoffLocation.landmark,
      },
      estimatedDistance,
      estimatedDuration,
      baseFare: fareDetails.baseFare,
      distanceFare: fareDetails.distanceFare,
      timeFare: fareDetails.timeFare,
      totalFare: fareDetails.totalFare,
      discountAmount,
      finalAmount,
      rideType,
      paymentMethod,
      specialInstructions,
      isScheduled,
      scheduledTime: isScheduled && scheduledTime ? new Date(scheduledTime) : undefined,
      status: 'pending',
    };

    // Create the ride
    const ride = new Ride(rideData);
    await ride.save();

    // Populate rider information for response
    await ride.populate('riderId', 'firstName lastName phone profileImage');

    // Trigger n8n workflow for automated driver assignment
    try {
      await workflowManager.triggerRideBookingFlow({
        trigger: WorkflowTrigger.RIDE_REQUESTED,
        timestamp: Date.now(),
        data: {},
        ride: {
          id: ride._id.toString(),
          riderId: ride.riderId._id.toString(),
          status: ride.status,
          pickupLocation: ride.pickupLocation,
          dropoffLocation: ride.dropoffLocation,
          estimatedDistance: ride.estimatedDistance,
          estimatedDuration: ride.estimatedDuration,
          finalAmount: ride.finalAmount,
        },
        metadata: {
          rideId: ride._id.toString(),
          userId: ride.riderId._id.toString(),
          priority: 'high',
        },
      });
    } catch (workflowError) {
      console.error('n8n workflow trigger failed:', workflowError);
      // Continue with the response even if workflow fails
    }

    return NextResponse.json({
      message: 'Ride booked successfully',
      ride: {
        _id: ride._id,
        status: ride.status,
        pickupLocation: ride.pickupLocation,
        dropoffLocation: ride.dropoffLocation,
        estimatedDistance: ride.estimatedDistance,
        estimatedDuration: ride.estimatedDuration,
        finalAmount: ride.finalAmount,
        rideType: ride.rideType,
        paymentMethod: ride.paymentMethod,
        isScheduled: ride.isScheduled,
        scheduledTime: ride.scheduledTime,
        requestedAt: ride.requestedAt,
        rider: ride.riderId,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Ride booking error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
