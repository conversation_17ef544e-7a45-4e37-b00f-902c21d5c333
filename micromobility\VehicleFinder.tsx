'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  MapPin, 
  Battery, 
  Clock, 
  DollarSign, 
  Zap, 
  Bike,
  Car,
  Navigation,
  Star,
  AlertCircle,
  CheckCircle,
  Timer,
  Wallet
} from 'lucide-react';

interface VehicleFinderProps {
  userLocation?: { latitude: number; longitude: number };
  onVehicleSelected?: (vehicle: any) => void;
  preferredType?: 'bike' | 'scooter' | 'e_bike' | 'e_scooter';
}

interface Vehicle {
  id: string;
  type: string;
  model: string;
  manufacturer: string;
  location: { latitude: number; longitude: number };
  distance: number;
  walkingTime: number;
  battery: {
    level: number;
    estimatedRange: number;
  };
  pricing: {
    unlockFee: number;
    perMinuteRate: number;
    perKmRate: number;
    maxDailyRate: number;
    multiplier: number;
    factors: any;
  };
  features: {
    gpsEnabled: boolean;
    lights: boolean;
    horn: boolean;
  };
  zone: {
    id: string;
    name: string;
    type: string;
  };
  lastRide?: {
    rating: number;
    distance: number;
  };
}

const vehicleIcons = {
  bike: Bike,
  scooter: Car,
  e_bike: Bike,
  e_scooter: Car,
};

const vehicleColors = {
  bike: 'text-blue-600',
  scooter: 'text-green-600',
  e_bike: 'text-purple-600',
  e_scooter: 'text-orange-600',
};

export default function VehicleFinder({ 
  userLocation, 
  onVehicleSelected, 
  preferredType = 'e_scooter' 
}: VehicleFinderProps) {
  const [vehicles, setVehicles] = useState<Vehicle[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<string | null>(null);
  const [reserving, setReserving] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    vehicleType: preferredType,
    maxDistance: 500,
    minBattery: 20,
  });
  const [error, setError] = useState<string | null>(null);
  const [summary, setSummary] = useState<any>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);

  // Default location (Bangalore city center)
  const defaultLocation = userLocation || { latitude: 12.9716, longitude: 77.5946 };

  const findVehicles = async () => {
    setLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const params = new URLSearchParams({
        latitude: defaultLocation.latitude.toString(),
        longitude: defaultLocation.longitude.toString(),
        vehicleType: filters.vehicleType,
        maxDistance: filters.maxDistance.toString(),
        minBattery: filters.minBattery.toString(),
      });

      const response = await fetch(`/api/micromobility/vehicles?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to find vehicles');
      }

      const data = await response.json();
      
      if (data.success) {
        setVehicles(data.data.vehicles);
        setSummary(data.data.summary);
        setRecommendations(data.data.recommendations);
      } else {
        throw new Error(data.error || 'Failed to find vehicles');
      }

    } catch (error) {
      console.error('Vehicle search failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to find vehicles');
    } finally {
      setLoading(false);
    }
  };

  const reserveVehicle = async (vehicleId: string) => {
    setReserving(vehicleId);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/micromobility/vehicles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          action: 'reserve',
          vehicleId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reserve vehicle');
      }

      const data = await response.json();
      
      if (data.success) {
        const vehicle = vehicles.find(v => v.id === vehicleId);
        if (vehicle && onVehicleSelected) {
          onVehicleSelected({
            ...vehicle,
            reservation: data.data,
          });
        }
      } else {
        throw new Error(data.error || 'Failed to reserve vehicle');
      }

    } catch (error) {
      console.error('Vehicle reservation failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to reserve vehicle');
    } finally {
      setReserving(null);
    }
  };

  useEffect(() => {
    findVehicles();
  }, [filters]);

  const formatDistance = (meters: number): string => {
    return meters < 1000 ? `${meters}m` : `${(meters / 1000).toFixed(1)}km`;
  };

  const formatWalkingTime = (seconds: number): string => {
    const minutes = Math.round(seconds / 60);
    return `${minutes} min walk`;
  };

  const getBatteryColor = (level: number): string => {
    if (level > 60) return 'text-green-600';
    if (level > 30) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPricingBadgeColor = (multiplier: number): string => {
    if (multiplier < 1) return 'bg-green-100 text-green-800';
    if (multiplier > 1.2) return 'bg-red-100 text-red-800';
    return 'bg-blue-100 text-blue-800';
  };

  const getPricingText = (multiplier: number): string => {
    if (multiplier < 1) return 'Low Demand';
    if (multiplier > 1.2) return 'High Demand';
    return 'Standard';
  };

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Navigation className="h-5 w-5 mr-2" />
            Find Nearby Vehicles
          </CardTitle>
          <CardDescription>
            Discover bikes and scooters available for rent near you
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Vehicle Type Filter */}
          <div className="flex flex-wrap gap-2">
            {['bike', 'e_bike', 'scooter', 'e_scooter'].map((type) => (
              <Button
                key={type}
                variant={filters.vehicleType === type ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilters(prev => ({ ...prev, vehicleType: type as any }))}
              >
                {type.replace('_', '-').toUpperCase()}
              </Button>
            ))}
          </div>

          {/* Distance and Battery Filters */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Max Distance</label>
              <select
                value={filters.maxDistance}
                onChange={(e) => setFilters(prev => ({ ...prev, maxDistance: parseInt(e.target.value) }))}
                className="w-full mt-1 p-2 border rounded-md"
              >
                <option value={200}>200m</option>
                <option value={500}>500m</option>
                <option value={1000}>1km</option>
                <option value={2000}>2km</option>
              </select>
            </div>
            <div>
              <label className="text-sm font-medium">Min Battery</label>
              <select
                value={filters.minBattery}
                onChange={(e) => setFilters(prev => ({ ...prev, minBattery: parseInt(e.target.value) }))}
                className="w-full mt-1 p-2 border rounded-md"
              >
                <option value={10}>10%</option>
                <option value={20}>20%</option>
                <option value={30}>30%</option>
                <option value={50}>50%</option>
              </select>
            </div>
          </div>

          <Button onClick={findVehicles} disabled={loading} className="w-full">
            {loading ? (
              <>
                <LoadingSpinner className="mr-2" />
                Searching...
              </>
            ) : (
              <>
                <Navigation className="h-4 w-4 mr-2" />
                Find Vehicles
              </>
            )}
          </Button>

          {error && (
            <div className="flex items-center text-red-600 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              {error}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary */}
      {summary && (
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{summary.total}</div>
                <div className="text-sm text-gray-600">Available</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{formatDistance(summary.averageDistance)}</div>
                <div className="text-sm text-gray-600">Avg Distance</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{summary.averageBattery}%</div>
                <div className="text-sm text-gray-600">Avg Battery</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">
                  ₹{summary.priceRange?.minUnlockFee || 0}+
                </div>
                <div className="text-sm text-gray-600">From</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <h4 className="font-medium text-blue-900 mb-2">Recommendations</h4>
            <ul className="space-y-1">
              {recommendations.map((rec, index) => (
                <li key={index} className="flex items-center text-sm text-blue-800">
                  <CheckCircle className="h-4 w-4 mr-2 text-blue-600" />
                  {rec}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Vehicle List */}
      <div className="space-y-4">
        {vehicles.map((vehicle) => {
          const VehicleIcon = vehicleIcons[vehicle.type] || Bike;
          const vehicleColor = vehicleColors[vehicle.type] || 'text-gray-600';
          
          return (
            <Card 
              key={vehicle.id} 
              className={`cursor-pointer transition-all ${
                selectedVehicle === vehicle.id 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'hover:border-gray-300'
              }`}
              onClick={() => setSelectedVehicle(vehicle.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-full bg-gray-100 ${vehicleColor}`}>
                      <VehicleIcon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-medium">{vehicle.model}</h3>
                        <Badge variant="outline">{vehicle.manufacturer}</Badge>
                        <Badge 
                          className={getPricingBadgeColor(vehicle.pricing.multiplier)}
                        >
                          {getPricingText(vehicle.pricing.multiplier)}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{formatDistance(vehicle.distance)}</span>
                        </div>
                        <div className="flex items-center">
                          <Timer className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{formatWalkingTime(vehicle.walkingTime)}</span>
                        </div>
                        <div className="flex items-center">
                          <Battery className={`h-4 w-4 mr-1 ${getBatteryColor(vehicle.battery.level)}`} />
                          <span>{vehicle.battery.level}% ({vehicle.battery.estimatedRange}km)</span>
                        </div>
                        <div className="flex items-center">
                          <Wallet className="h-4 w-4 mr-1 text-gray-500" />
                          <span>₹{vehicle.pricing.unlockFee} + ₹{vehicle.pricing.perMinuteRate}/min</span>
                        </div>
                      </div>

                      {vehicle.lastRide && (
                        <div className="flex items-center mt-2 text-sm text-gray-600">
                          <Star className="h-4 w-4 mr-1 text-yellow-500" />
                          <span>{vehicle.lastRide.rating}/5 rating</span>
                        </div>
                      )}

                      <div className="flex items-center mt-2 space-x-2">
                        {vehicle.features.lights && (
                          <Badge variant="secondary" className="text-xs">Lights</Badge>
                        )}
                        {vehicle.features.horn && (
                          <Badge variant="secondary" className="text-xs">Horn</Badge>
                        )}
                        {vehicle.features.gpsEnabled && (
                          <Badge variant="secondary" className="text-xs">GPS</Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <Button
                      onClick={(e) => {
                        e.stopPropagation();
                        reserveVehicle(vehicle.id);
                      }}
                      disabled={reserving === vehicle.id}
                      size="sm"
                    >
                      {reserving === vehicle.id ? (
                        <>
                          <LoadingSpinner className="mr-2" />
                          Reserving...
                        </>
                      ) : (
                        <>
                          <Zap className="h-4 w-4 mr-2" />
                          Reserve
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {vehicles.length === 0 && !loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <Navigation className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No vehicles found</h3>
            <p className="text-gray-600 mb-4">
              Try expanding your search radius or changing vehicle type preferences.
            </p>
            <Button onClick={findVehicles} variant="outline">
              <Navigation className="h-4 w-4 mr-2" />
              Search Again
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
