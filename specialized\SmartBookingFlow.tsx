'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Leaf, 
  Star,
  Zap,
  Users,
  Calendar,
  Route,
  Sparkles,
  Brain,
  TrendingUp,
  Shield,
  Heart
} from 'lucide-react';

interface SmartBookingFlowProps {
  userPreferences: any;
  onBookingComplete: (booking: any) => void;
}

export default function SmartBookingFlow({ userPreferences, onBookingComplete }: SmartBookingFlowProps) {
  const [step, setStep] = useState(1);
  const [bookingData, setBookingData] = useState({
    from: '',
    to: '',
    when: 'now',
    passengers: 1,
    preferences: {
      prioritizeTime: true,
      prioritizeCost: false,
      prioritizeComfort: false,
      prioritizeEco: true,
      shareRide: false,
      accessibilityNeeds: false
    }
  });
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [smartOptions, setSmartOptions] = useState([]);

  useEffect(() => {
    // Simulate AI-powered suggestions based on user behavior
    const suggestions = [
      {
        type: 'route',
        title: 'Fastest Route Available',
        description: 'Based on current traffic, this route is 15% faster',
        icon: <Zap className="h-4 w-4 text-yellow-500" />,
        savings: '5 minutes'
      },
      {
        type: 'eco',
        title: 'Eco-Friendly Option',
        description: 'Choose bike to save 2.5kg CO₂',
        icon: <Leaf className="h-4 w-4 text-green-500" />,
        savings: '2.5kg CO₂'
      },
      {
        type: 'cost',
        title: 'Budget Saver',
        description: 'Share ride and save 40%',
        icon: <DollarSign className="h-4 w-4 text-blue-500" />,
        savings: '₹35'
      }
    ];
    setAiSuggestions(suggestions);

    // Generate smart options based on preferences
    const options = [
      {
        id: 'smart-eco',
        type: 'bike',
        title: 'Smart Eco Choice',
        subtitle: 'AI Recommended',
        price: 45,
        time: 18,
        carbon: 0,
        comfort: 3,
        features: ['Zero emissions', 'Exercise benefit', 'Traffic-free routes'],
        aiScore: 95,
        badge: 'Best for Environment'
      },
      {
        id: 'smart-balanced',
        type: 'scooter',
        title: 'Smart Balanced',
        subtitle: 'Popular Choice',
        price: 65,
        time: 15,
        carbon: 1.2,
        comfort: 4,
        features: ['Good speed', 'Moderate cost', 'Weather protection'],
        aiScore: 88,
        badge: 'Most Popular'
      },
      {
        id: 'smart-comfort',
        type: 'car',
        title: 'Smart Comfort',
        subtitle: 'Premium Experience',
        price: 120,
        time: 12,
        carbon: 3.5,
        comfort: 5,
        features: ['AC comfort', 'Door-to-door', 'Luggage space'],
        aiScore: 75,
        badge: 'Most Comfortable'
      }
    ];
    setSmartOptions(options);
  }, [bookingData]);

  const handleNext = () => {
    if (step < 4) setStep(step + 1);
  };

  const handlePrevious = () => {
    if (step > 1) setStep(step - 1);
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-2 mb-6">
      {[1, 2, 3, 4].map((stepNum) => (
        <div key={stepNum} className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            step >= stepNum 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-200 text-gray-600'
          }`}>
            {stepNum}
          </div>
          {stepNum < 4 && (
            <div className={`w-8 h-1 mx-2 ${
              step > stepNum ? 'bg-blue-600' : 'bg-gray-200'
            }`} />
          )}
        </div>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MapPin className="h-5 w-5 mr-2 text-blue-600" />
          Where are you going?
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">From</label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input 
              placeholder="Current location" 
              className="pl-10"
              value={bookingData.from}
              onChange={(e) => setBookingData({...bookingData, from: e.target.value})}
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-2">To</label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input 
              placeholder="Where to?" 
              className="pl-10"
              value={bookingData.to}
              onChange={(e) => setBookingData({...bookingData, to: e.target.value})}
            />
          </div>
        </div>

        {/* Quick Destinations */}
        <div>
          <p className="text-sm font-medium mb-2">Quick destinations</p>
          <div className="grid grid-cols-2 gap-2">
            {['🏠 Home', '🏢 Office', '✈️ Airport', '🏥 Hospital'].map((dest) => (
              <Button 
                key={dest} 
                variant="outline" 
                size="sm" 
                className="justify-start"
                onClick={() => setBookingData({...bookingData, to: dest})}
              >
                {dest}
              </Button>
            ))}
          </div>
        </div>

        {/* AI Suggestions */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Brain className="h-4 w-4 text-blue-600 mr-2" />
            <span className="text-sm font-medium text-blue-800">AI Suggestions</span>
          </div>
          <div className="space-y-2">
            {aiSuggestions.slice(0, 2).map((suggestion, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex items-center">
                  {suggestion.icon}
                  <span className="ml-2 text-gray-700">{suggestion.title}</span>
                </div>
                <Badge variant="outline" className="text-xs">{suggestion.savings}</Badge>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderStep2 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2 text-blue-600" />
          When do you need it?
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Button 
            variant={bookingData.when === 'now' ? 'default' : 'outline'}
            onClick={() => setBookingData({...bookingData, when: 'now'})}
            className="h-16 flex-col"
          >
            <Zap className="h-5 w-5 mb-1" />
            <span>Now</span>
          </Button>
          <Button 
            variant={bookingData.when === 'later' ? 'default' : 'outline'}
            onClick={() => setBookingData({...bookingData, when: 'later'})}
            className="h-16 flex-col"
          >
            <Calendar className="h-5 w-5 mb-1" />
            <span>Schedule</span>
          </Button>
        </div>

        {bookingData.when === 'later' && (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Date</label>
              <Input type="date" />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Time</label>
              <Input type="time" />
            </div>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium mb-2">Number of passengers</label>
          <div className="flex items-center space-x-4">
            <Users className="h-4 w-4 text-gray-400" />
            <Slider
              value={[bookingData.passengers]}
              onValueChange={(value) => setBookingData({...bookingData, passengers: value[0]})}
              max={4}
              min={1}
              step={1}
              className="flex-1"
            />
            <span className="text-sm font-medium w-8">{bookingData.passengers}</span>
          </div>
        </div>

        {/* Smart Time Suggestions */}
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Sparkles className="h-4 w-4 text-green-600 mr-2" />
            <span className="text-sm font-medium text-green-800">Smart Timing</span>
          </div>
          <p className="text-sm text-gray-700">
            Leave 10 minutes earlier to avoid traffic and save ₹15
          </p>
        </div>
      </CardContent>
    </Card>
  );

  const renderStep3 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Heart className="h-5 w-5 mr-2 text-blue-600" />
          Your preferences
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <span className="text-sm">Prioritize speed</span>
            </div>
            <Switch 
              checked={bookingData.preferences.prioritizeTime}
              onCheckedChange={(checked) => 
                setBookingData({
                  ...bookingData, 
                  preferences: {...bookingData.preferences, prioritizeTime: checked}
                })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-500" />
              <span className="text-sm">Prioritize cost</span>
            </div>
            <Switch 
              checked={bookingData.preferences.prioritizeCost}
              onCheckedChange={(checked) => 
                setBookingData({
                  ...bookingData, 
                  preferences: {...bookingData.preferences, prioritizeCost: checked}
                })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Leaf className="h-4 w-4 text-green-600" />
              <span className="text-sm">Eco-friendly options</span>
            </div>
            <Switch 
              checked={bookingData.preferences.prioritizeEco}
              onCheckedChange={(checked) => 
                setBookingData({
                  ...bookingData, 
                  preferences: {...bookingData.preferences, prioritizeEco: checked}
                })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <span className="text-sm">Share ride to save money</span>
            </div>
            <Switch 
              checked={bookingData.preferences.shareRide}
              onCheckedChange={(checked) => 
                setBookingData({
                  ...bookingData, 
                  preferences: {...bookingData.preferences, shareRide: checked}
                })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4 text-purple-500" />
              <span className="text-sm">Accessibility needs</span>
            </div>
            <Switch 
              checked={bookingData.preferences.accessibilityNeeds}
              onCheckedChange={(checked) => 
                setBookingData({
                  ...bookingData, 
                  preferences: {...bookingData.preferences, accessibilityNeeds: checked}
                })
              }
            />
          </div>
        </div>

        {/* Preference Impact */}
        <div className="bg-purple-50 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <TrendingUp className="h-4 w-4 text-purple-600 mr-2" />
            <span className="text-sm font-medium text-purple-800">Impact Preview</span>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Estimated savings:</span>
              <span className="font-medium ml-1">₹25</span>
            </div>
            <div>
              <span className="text-gray-600">Carbon impact:</span>
              <span className="font-medium ml-1 text-green-600">-2.1kg CO₂</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const renderStep4 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Sparkles className="h-5 w-5 mr-2 text-blue-600" />
          Smart recommendations
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {smartOptions.map((option) => (
          <Card key={option.id} className="border-2 hover:border-blue-300 cursor-pointer transition-colors">
            <CardContent className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <div className="flex items-center space-x-2">
                    <h3 className="font-medium">{option.title}</h3>
                    <Badge className="bg-blue-100 text-blue-800 text-xs">{option.subtitle}</Badge>
                  </div>
                  <Badge variant="outline" className="mt-1 text-xs">{option.badge}</Badge>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold">₹{option.price}</div>
                  <div className="text-sm text-gray-600">{option.time} min</div>
                </div>
              </div>

              <div className="grid grid-cols-4 gap-4 mb-3 text-center">
                <div>
                  <div className="text-xs text-gray-600">AI Score</div>
                  <div className="font-medium text-blue-600">{option.aiScore}%</div>
                </div>
                <div>
                  <div className="text-xs text-gray-600">Carbon</div>
                  <div className="font-medium text-green-600">{option.carbon}kg</div>
                </div>
                <div>
                  <div className="text-xs text-gray-600">Comfort</div>
                  <div className="flex justify-center">
                    {[...Array(5)].map((_, i) => (
                      <Star 
                        key={i} 
                        className={`h-3 w-3 ${i < option.comfort ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                      />
                    ))}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-gray-600">Time</div>
                  <div className="font-medium">{option.time}m</div>
                </div>
              </div>

              <div className="flex flex-wrap gap-1">
                {option.features.map((feature, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {feature}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}

        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center mb-2">
            <Brain className="h-4 w-4 text-yellow-600 mr-2" />
            <span className="text-sm font-medium text-yellow-800">AI Insight</span>
          </div>
          <p className="text-sm text-gray-700">
            Based on your preferences and current conditions, the Eco Choice offers the best value with 95% AI confidence.
          </p>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen p-4">
      {/* Header */}
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Smart Booking</h1>
        <p className="text-gray-600">AI-powered ride optimization</p>
      </div>

      {renderStepIndicator()}

      {/* Step Content */}
      <div className="mb-6">
        {step === 1 && renderStep1()}
        {step === 2 && renderStep2()}
        {step === 3 && renderStep3()}
        {step === 4 && renderStep4()}
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={handlePrevious}
          disabled={step === 1}
        >
          Previous
        </Button>
        
        {step < 4 ? (
          <Button onClick={handleNext}>
            Next
          </Button>
        ) : (
          <Button 
            onClick={() => onBookingComplete(bookingData)}
            className="bg-green-600 hover:bg-green-700"
          >
            Book Now
          </Button>
        )}
      </div>
    </div>
  );
}
