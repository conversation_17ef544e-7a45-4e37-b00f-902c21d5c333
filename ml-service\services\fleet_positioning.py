import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN, KMeans
from sklearn.preprocessing import StandardScaler
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from geopy.distance import geodesic
import json

logger = logging.getLogger(__name__)

class FleetPositioningService:
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        
        # Mumbai boundaries
        self.mumbai_bounds = {
            'north': 19.2695,
            'south': 18.8930,
            'east': 72.9781,
            'west': 72.7761
        }
        
        # Key locations in Mumbai
        self.key_locations = [
            {"name": "Andheri East", "lat": 19.1136, "lon": 72.8697, "importance": 0.9},
            {"name": "Bandra Kurla Complex", "lat": 19.0596, "lon": 72.8656, "importance": 1.0},
            {"name": "Powai", "lat": 19.1197, "lon": 72.9056, "importance": 0.8},
            {"name": "Malad West", "lat": 19.1875, "lon": 72.8304, "importance": 0.7},
            {"name": "Goregaon East", "lat": 19.1653, "lon": 72.8526, "importance": 0.7},
            {"name": "Lower Parel", "lat": 19.0176, "lon": 72.8286, "importance": 0.9},
            {"name": "Worli", "lat": 19.0176, "lon": 72.8156, "importance": 0.8},
            {"name": "Hiranandani Gardens", "lat": 19.1197, "lon": 72.9056, "importance": 0.8},
            {"name": "Kurla", "lat": 19.0728, "lon": 72.8826, "importance": 0.7},
            {"name": "Santacruz East", "lat": 19.0896, "lon": 72.8656, "importance": 0.7}
        ]
    
    async def optimize_positioning(self, current_drivers: List[Dict], time_horizon: int = 60):
        """Optimize driver positioning based on predicted demand"""
        try:
            # Get demand predictions for key locations
            demand_predictions = await self._get_demand_predictions(time_horizon)
            
            # Analyze current driver distribution
            driver_distribution = self._analyze_driver_distribution(current_drivers)
            
            # Identify demand-supply gaps
            gaps = self._identify_demand_supply_gaps(demand_predictions, driver_distribution)
            
            # Generate positioning recommendations
            recommendations = self._generate_positioning_recommendations(
                current_drivers, gaps, demand_predictions
            )
            
            # Calculate efficiency score
            efficiency_score = self._calculate_efficiency_score(
                driver_distribution, demand_predictions
            )
            
            # Identify demand hotspots
            hotspots = await self.identify_demand_hotspots()
            
            result = {
                "recommendations": recommendations,
                "demand_hotspots": hotspots,
                "efficiency_score": efficiency_score,
                "current_distribution": driver_distribution,
                "predicted_demand": demand_predictions,
                "timestamp": datetime.now()
            }
            
            # Cache result
            cache_key = f"fleet_positioning:{datetime.now().hour}"
            await self.cache_manager.set(cache_key, result, expire=900)  # 15 minutes
            
            return result
            
        except Exception as e:
            logger.error(f"Fleet positioning optimization error: {e}")
            raise
    
    async def _get_demand_predictions(self, time_horizon: int):
        """Get demand predictions for all key locations"""
        predictions = {}
        
        for location in self.key_locations:
            # Check cache first
            cache_key = f"demand_prediction:{location['name']}:{datetime.now().hour}"
            cached_prediction = await self.cache_manager.get(cache_key)
            
            if cached_prediction:
                predictions[location['name']] = cached_prediction
            else:
                # Generate mock prediction (in real implementation, call ML service)
                prediction = self._generate_mock_demand_prediction(location, time_horizon)
                predictions[location['name']] = prediction
        
        return predictions
    
    def _generate_mock_demand_prediction(self, location: Dict, time_horizon: int):
        """Generate mock demand prediction for testing"""
        current_hour = datetime.now().hour
        base_demand = location['importance'] * 10
        
        # Peak hour adjustments
        if current_hour in [8, 9, 18, 19]:
            base_demand *= 1.5
        elif current_hour in [7, 10, 17, 20]:
            base_demand *= 1.2
        
        # Add some randomness
        demand = max(1, base_demand + np.random.normal(0, 2))
        
        return {
            "location": location['name'],
            "predicted_demand": demand,
            "confidence": 0.8,
            "latitude": location['lat'],
            "longitude": location['lon'],
            "time_horizon": time_horizon
        }
    
    def _analyze_driver_distribution(self, current_drivers: List[Dict]):
        """Analyze current driver distribution across locations"""
        distribution = {}
        
        for driver in current_drivers:
            if not driver.get('isAvailable', True):
                continue
                
            lat = driver.get('latitude', 0)
            lon = driver.get('longitude', 0)
            
            # Find nearest key location
            nearest_location = self._find_nearest_location(lat, lon)
            
            if nearest_location not in distribution:
                distribution[nearest_location] = {
                    'driver_count': 0,
                    'drivers': [],
                    'average_rating': 0,
                    'total_earnings_today': 0
                }
            
            distribution[nearest_location]['driver_count'] += 1
            distribution[nearest_location]['drivers'].append(driver)
            distribution[nearest_location]['average_rating'] += driver.get('rating', 4.5)
            distribution[nearest_location]['total_earnings_today'] += driver.get('earningsToday', 0)
        
        # Calculate averages
        for location, data in distribution.items():
            if data['driver_count'] > 0:
                data['average_rating'] /= data['driver_count']
        
        return distribution
    
    def _find_nearest_location(self, lat: float, lon: float):
        """Find nearest key location to given coordinates"""
        min_distance = float('inf')
        nearest_location = self.key_locations[0]['name']
        
        for location in self.key_locations:
            distance = geodesic((lat, lon), (location['lat'], location['lon'])).kilometers
            if distance < min_distance:
                min_distance = distance
                nearest_location = location['name']
        
        return nearest_location
    
    def _identify_demand_supply_gaps(self, demand_predictions: Dict, driver_distribution: Dict):
        """Identify locations with demand-supply imbalances"""
        gaps = []
        
        for location_name, prediction in demand_predictions.items():
            predicted_demand = prediction['predicted_demand']
            current_supply = driver_distribution.get(location_name, {}).get('driver_count', 0)
            
            # Calculate demand-supply ratio
            if current_supply == 0:
                ratio = float('inf') if predicted_demand > 0 else 0
            else:
                ratio = predicted_demand / current_supply
            
            gap_severity = 'low'
            if ratio > 3:
                gap_severity = 'high'
            elif ratio > 2:
                gap_severity = 'medium'
            
            if ratio > 1.5:  # Significant gap
                gaps.append({
                    'location': location_name,
                    'predicted_demand': predicted_demand,
                    'current_supply': current_supply,
                    'demand_supply_ratio': ratio,
                    'gap_severity': gap_severity,
                    'latitude': prediction['latitude'],
                    'longitude': prediction['longitude'],
                    'drivers_needed': max(1, int(predicted_demand - current_supply))
                })
        
        # Sort by severity and ratio
        gaps.sort(key=lambda x: x['demand_supply_ratio'], reverse=True)
        
        return gaps
    
    def _generate_positioning_recommendations(self, current_drivers: List[Dict], 
                                            gaps: List[Dict], demand_predictions: Dict):
        """Generate specific positioning recommendations for drivers"""
        recommendations = []
        
        # Get available drivers (not currently on rides)
        available_drivers = [d for d in current_drivers if d.get('isAvailable', True)]
        
        for gap in gaps[:5]:  # Top 5 gaps
            # Find best drivers to reposition
            suitable_drivers = self._find_suitable_drivers_for_repositioning(
                available_drivers, gap
            )
            
            for driver in suitable_drivers[:gap['drivers_needed']]:
                # Calculate travel time and distance
                current_lat = driver.get('latitude', 0)
                current_lon = driver.get('longitude', 0)
                target_lat = gap['latitude']
                target_lon = gap['longitude']
                
                distance = geodesic((current_lat, current_lon), (target_lat, target_lon)).kilometers
                travel_time = max(5, int(distance * 3))  # Rough estimate: 3 min per km
                
                # Calculate potential earnings
                potential_earnings = gap['predicted_demand'] * 50  # ₹50 per ride estimate
                
                recommendation = {
                    'driver_id': driver.get('id'),
                    'driver_name': driver.get('name'),
                    'current_location': {
                        'latitude': current_lat,
                        'longitude': current_lon,
                        'area': self._find_nearest_location(current_lat, current_lon)
                    },
                    'recommended_location': {
                        'latitude': target_lat,
                        'longitude': target_lon,
                        'area': gap['location']
                    },
                    'travel_distance_km': round(distance, 1),
                    'estimated_travel_time_minutes': travel_time,
                    'demand_level': gap['gap_severity'],
                    'potential_earnings': round(potential_earnings),
                    'priority': self._calculate_recommendation_priority(driver, gap),
                    'reason': f"High demand expected in {gap['location']} ({gap['predicted_demand']:.1f} rides predicted)",
                    'confidence': demand_predictions[gap['location']]['confidence']
                }
                
                recommendations.append(recommendation)
        
        # Sort by priority
        recommendations.sort(key=lambda x: x['priority'], reverse=True)
        
        return recommendations
    
    def _find_suitable_drivers_for_repositioning(self, available_drivers: List[Dict], gap: Dict):
        """Find drivers suitable for repositioning to fill demand gap"""
        suitable_drivers = []
        
        for driver in available_drivers:
            current_lat = driver.get('latitude', 0)
            current_lon = driver.get('longitude', 0)
            
            # Calculate distance to target location
            distance = geodesic(
                (current_lat, current_lon), 
                (gap['latitude'], gap['longitude'])
            ).kilometers
            
            # Consider drivers within reasonable distance (< 10km)
            if distance < 10:
                driver_score = self._calculate_driver_suitability_score(driver, gap, distance)
                suitable_drivers.append({
                    **driver,
                    'suitability_score': driver_score,
                    'distance_to_target': distance
                })
        
        # Sort by suitability score
        suitable_drivers.sort(key=lambda x: x['suitability_score'], reverse=True)
        
        return suitable_drivers
    
    def _calculate_driver_suitability_score(self, driver: Dict, gap: Dict, distance: float):
        """Calculate how suitable a driver is for repositioning"""
        score = 100
        
        # Distance penalty (closer is better)
        score -= distance * 5
        
        # Rating bonus
        rating = driver.get('rating', 4.5)
        score += (rating - 4.0) * 20
        
        # Earnings balance (help lower-earning drivers)
        earnings_today = driver.get('earningsToday', 0)
        if earnings_today < 300:  # Below average
            score += 15
        elif earnings_today > 600:  # Above average
            score -= 10
        
        # Experience bonus
        rides_completed = driver.get('ridesCompletedToday', 0)
        if rides_completed > 5:
            score += 10
        
        return max(0, score)
    
    def _calculate_recommendation_priority(self, driver: Dict, gap: Dict):
        """Calculate priority score for recommendation"""
        priority = 50
        
        # Gap severity
        if gap['gap_severity'] == 'high':
            priority += 30
        elif gap['gap_severity'] == 'medium':
            priority += 20
        else:
            priority += 10
        
        # Driver rating
        rating = driver.get('rating', 4.5)
        priority += (rating - 4.0) * 10
        
        # Distance factor (closer gets higher priority)
        distance = gap.get('distance_to_target', 5)
        priority += max(0, 20 - distance * 2)
        
        return priority
    
    def _calculate_efficiency_score(self, driver_distribution: Dict, demand_predictions: Dict):
        """Calculate overall fleet positioning efficiency score"""
        total_score = 0
        total_locations = len(self.key_locations)
        
        for location in self.key_locations:
            location_name = location['name']
            
            predicted_demand = demand_predictions.get(location_name, {}).get('predicted_demand', 0)
            current_supply = driver_distribution.get(location_name, {}).get('driver_count', 0)
            
            # Calculate location efficiency (0-100)
            if predicted_demand == 0:
                location_efficiency = 100 if current_supply == 0 else 50
            else:
                ratio = current_supply / predicted_demand
                if 0.8 <= ratio <= 1.2:  # Optimal range
                    location_efficiency = 100
                elif 0.5 <= ratio <= 1.5:  # Good range
                    location_efficiency = 80
                elif 0.3 <= ratio <= 2.0:  # Acceptable range
                    location_efficiency = 60
                else:  # Poor efficiency
                    location_efficiency = 30
            
            # Weight by location importance
            weighted_efficiency = location_efficiency * location['importance']
            total_score += weighted_efficiency
        
        # Calculate average efficiency
        total_importance = sum(loc['importance'] for loc in self.key_locations)
        efficiency_score = total_score / total_importance
        
        return round(efficiency_score, 1)
    
    async def identify_demand_hotspots(self):
        """Identify current and predicted demand hotspots"""
        hotspots = []
        
        for location in self.key_locations:
            # Get current and predicted demand
            current_demand = await self._get_current_demand(location)
            predicted_demand = self._generate_mock_demand_prediction(location, 60)
            
            # Calculate hotspot score
            hotspot_score = (current_demand * 0.4 + predicted_demand['predicted_demand'] * 0.6)
            
            if hotspot_score > 5:  # Threshold for hotspot
                hotspot_level = 'high' if hotspot_score > 10 else 'medium'
                
                hotspots.append({
                    'location': location['name'],
                    'latitude': location['lat'],
                    'longitude': location['lon'],
                    'current_demand': current_demand,
                    'predicted_demand': predicted_demand['predicted_demand'],
                    'hotspot_score': round(hotspot_score, 1),
                    'hotspot_level': hotspot_level,
                    'recommendation': self._get_hotspot_recommendation(hotspot_level, hotspot_score)
                })
        
        # Sort by hotspot score
        hotspots.sort(key=lambda x: x['hotspot_score'], reverse=True)
        
        return hotspots
    
    async def _get_current_demand(self, location: Dict):
        """Get current demand for a location (mock implementation)"""
        # In real implementation, this would query recent ride requests
        base_demand = location['importance'] * 5
        current_hour = datetime.now().hour
        
        # Peak hour adjustments
        if current_hour in [8, 9, 18, 19]:
            base_demand *= 1.3
        elif current_hour in [7, 10, 17, 20]:
            base_demand *= 1.1
        
        return max(0, base_demand + np.random.normal(0, 1))
    
    def _get_hotspot_recommendation(self, level: str, score: float):
        """Get recommendation for hotspot management"""
        if level == 'high':
            return f"Deploy 3-5 additional drivers immediately. High demand area (score: {score:.1f})"
        elif level == 'medium':
            return f"Consider deploying 1-2 additional drivers. Moderate demand area (score: {score:.1f})"
        else:
            return f"Monitor closely. Emerging demand area (score: {score:.1f})"
    
    async def retrain_model(self):
        """Retrain fleet positioning models with latest data"""
        try:
            # Get historical positioning data
            historical_data = await self._get_historical_positioning_data()
            
            if len(historical_data) > 50:
                # Analyze patterns and update positioning algorithms
                self._analyze_positioning_patterns(historical_data)
                logger.info("Fleet positioning model updated successfully")
            else:
                logger.warning("Insufficient data for positioning model update")
                
        except Exception as e:
            logger.error(f"Fleet positioning model update error: {e}")
    
    async def _get_historical_positioning_data(self):
        """Get historical driver positioning and demand data"""
        try:
            # Get data from last 7 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            # This would query actual database for historical positioning data
            return []
            
        except Exception as e:
            logger.error(f"Error fetching historical positioning data: {e}")
            return []
    
    def _analyze_positioning_patterns(self, historical_data):
        """Analyze historical positioning patterns to improve recommendations"""
        # Implement pattern analysis for positioning optimization
        # This would include clustering analysis, success rate calculations, etc.
        pass
    
    async def get_model_status(self):
        """Get current fleet positioning model status"""
        return {
            "model_type": "Geospatial Analysis + Demand Matching",
            "last_updated": datetime.now().isoformat(),
            "key_locations_count": len(self.key_locations),
            "status": "active"
        }
