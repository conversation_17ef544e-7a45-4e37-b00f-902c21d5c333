'use client';

import { lazy, Suspense, ComponentType, ReactNode } from 'react';
import { LoadingSpinner } from '@/components/ui/loading';

// Lazy loading wrapper with error boundary
interface LazyWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
}

function LazyWrapper({ children, fallback, errorFallback }: LazyWrapperProps) {
  return (
    <Suspense fallback={fallback || <LazyLoadingFallback />}>
      {children}
    </Suspense>
  );
}

// Default loading fallback
function LazyLoadingFallback() {
  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <LoadingSpinner className="mx-auto mb-4" />
        <p className="text-gray-600">Loading component...</p>
      </div>
    </div>
  );
}

// Skeleton loading components
export function RideBookingSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/3"></div>
      <div className="space-y-4">
        <div className="h-12 bg-gray-200 rounded"></div>
        <div className="h-12 bg-gray-200 rounded"></div>
        <div className="grid grid-cols-3 gap-4">
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
          <div className="h-20 bg-gray-200 rounded"></div>
        </div>
        <div className="h-12 bg-gray-200 rounded"></div>
      </div>
    </div>
  );
}

export function DashboardSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-gray-200 h-64 rounded-lg"></div>
        <div className="bg-gray-200 h-64 rounded-lg"></div>
      </div>
    </div>
  );
}

export function VehicleMapSkeleton() {
  return (
    <div className="space-y-4 animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/4"></div>
      <div className="h-96 bg-gray-200 rounded-lg"></div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
        ))}
      </div>
    </div>
  );
}

export function DeliveryMenuSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/3"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="space-y-4">
            <div className="h-48 bg-gray-200 rounded-lg"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function CorporateDashboardSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/2"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 bg-gray-200 h-96 rounded-lg"></div>
        <div className="space-y-4">
          <div className="bg-gray-200 h-48 rounded-lg"></div>
          <div className="bg-gray-200 h-32 rounded-lg"></div>
        </div>
      </div>
    </div>
  );
}

// Lazy loaded components
export const LazyRideBooking = lazy(() => 
  import('@/components/rides/RideBooking').then(module => ({
    default: module.default
  }))
);

export const LazyDashboard = lazy(() => 
  import('@/components/dashboard/Dashboard').then(module => ({
    default: module.default
  }))
);

export const LazyVehicleMap = lazy(() => 
  import('@/components/vehicles/VehicleMap').then(module => ({
    default: module.default
  }))
);

export const LazyDeliveryMenu = lazy(() => 
  import('@/components/delivery/DeliveryMenu').then(module => ({
    default: module.default
  }))
);

export const LazyCorporateDashboard = lazy(() => 
  import('@/components/corporate/CorporateDashboard').then(module => ({
    default: module.default
  }))
);

export const LazyAnalytics = lazy(() => 
  import('@/components/analytics/Analytics').then(module => ({
    default: module.default
  }))
);

export const LazyChat = lazy(() => 
  import('@/components/chat/Chat').then(module => ({
    default: module.default
  }))
);

export const LazyNotifications = lazy(() => 
  import('@/components/notifications/Notifications').then(module => ({
    default: module.default
  }))
);

// Wrapped lazy components with fallbacks
export function RideBookingLazy() {
  return (
    <LazyWrapper fallback={<RideBookingSkeleton />}>
      <LazyRideBooking />
    </LazyWrapper>
  );
}

export function DashboardLazy() {
  return (
    <LazyWrapper fallback={<DashboardSkeleton />}>
      <LazyDashboard />
    </LazyWrapper>
  );
}

export function VehicleMapLazy() {
  return (
    <LazyWrapper fallback={<VehicleMapSkeleton />}>
      <LazyVehicleMap />
    </LazyWrapper>
  );
}

export function DeliveryMenuLazy() {
  return (
    <LazyWrapper fallback={<DeliveryMenuSkeleton />}>
      <LazyDeliveryMenu />
    </LazyWrapper>
  );
}

export function CorporateDashboardLazy() {
  return (
    <LazyWrapper fallback={<CorporateDashboardSkeleton />}>
      <LazyCorporateDashboard />
    </LazyWrapper>
  );
}

export function AnalyticsLazy() {
  return (
    <LazyWrapper fallback={<DashboardSkeleton />}>
      <LazyAnalytics />
    </LazyWrapper>
  );
}

export function ChatLazy() {
  return (
    <LazyWrapper fallback={<div className="h-96 bg-gray-100 rounded-lg animate-pulse"></div>}>
      <LazyChat />
    </LazyWrapper>
  );
}

export function NotificationsLazy() {
  return (
    <LazyWrapper fallback={<div className="h-64 bg-gray-100 rounded-lg animate-pulse"></div>}>
      <LazyNotifications />
    </LazyWrapper>
  );
}

// Higher-order component for lazy loading
export function withLazyLoading<T extends object>(
  Component: ComponentType<T>,
  fallback?: ReactNode
) {
  const LazyComponent = lazy(() => Promise.resolve({ default: Component }));
  
  return function WrappedComponent(props: T) {
    return (
      <LazyWrapper fallback={fallback}>
        <LazyComponent {...props} />
      </LazyWrapper>
    );
  };
}

// Intersection Observer based lazy loading
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(ref.current);

    return () => observer.disconnect();
  }, [ref, options]);

  return isIntersecting;
}

// Lazy image component
interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  onLoad?: () => void;
  onError?: () => void;
}

export function LazyImage({ 
  src, 
  alt, 
  className, 
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2YzZjRmNiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZmlsbD0iIzljYTNhZiI+TG9hZGluZy4uLjwvdGV4dD48L3N2Zz4=',
  onLoad,
  onError
}: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const isInView = useIntersectionObserver(imgRef);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div ref={imgRef} className={className}>
      {isInView && (
        <img
          src={hasError ? placeholder : (isLoaded ? src : placeholder)}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-50'
          }`}
          loading="lazy"
        />
      )}
    </div>
  );
}
