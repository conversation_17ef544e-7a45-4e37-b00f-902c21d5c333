import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import random

logger = logging.getLogger(__name__)

class RevenueOptimizationService:
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.pricing_model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
        # A/B testing variants
        self.ab_test_variants = {
            'conservative': {'surge_multiplier': 0.9, 'base_adjustment': 0.95},
            'standard': {'surge_multiplier': 1.0, 'base_adjustment': 1.0},
            'aggressive': {'surge_multiplier': 1.15, 'base_adjustment': 1.05},
            'dynamic': {'surge_multiplier': 1.1, 'base_adjustment': 1.02}
        }
        
        # Pricing parameters
        self.base_price_per_km = 5.0
        self.minimum_fare = 25.0
        self.maximum_surge_multiplier = 3.0
        self.driver_commission_rate = 0.6  # 60% to driver
        
        # Load or train model
        self._load_or_train_model()
    
    def _load_or_train_model(self):
        """Load existing pricing model or train new one"""
        try:
            self.pricing_model = joblib.load("models/pricing_model.joblib")
            self.scaler = joblib.load("models/pricing_scaler.joblib")
            self.label_encoders = joblib.load("models/pricing_encoders.joblib")
            logger.info("Loaded existing pricing optimization model")
        except FileNotFoundError:
            logger.info("No existing pricing model found, training new model...")
            self._train_initial_model()
    
    def _train_initial_model(self):
        """Train initial pricing model with synthetic data"""
        # Generate synthetic pricing data
        synthetic_data = self._generate_synthetic_pricing_data()
        
        # Prepare features
        X, y = self._prepare_pricing_features(synthetic_data)
        
        # Train model
        self._train_pricing_model(X, y)
        
        logger.info("Initial pricing optimization model trained successfully")
    
    def _generate_synthetic_pricing_data(self, samples=5000):
        """Generate synthetic pricing and revenue data"""
        data = []
        
        for _ in range(samples):
            # Base ride parameters
            distance = np.random.uniform(2, 25)  # 2-25 km rides
            base_price = distance * self.base_price_per_km
            
            # Time factors
            hour = np.random.randint(0, 24)
            day_of_week = np.random.randint(0, 7)
            is_weekend = day_of_week >= 5
            
            # Demand factors
            demand_level = np.random.choice(['low', 'medium', 'high'], p=[0.3, 0.5, 0.2])
            driver_availability = np.random.randint(5, 50)
            
            # Weather factors
            weather = np.random.choice(['clear', 'cloudy', 'rain', 'storm'], p=[0.4, 0.3, 0.2, 0.1])
            
            # Location factors
            location_type = np.random.choice(['residential', 'commercial', 'airport', 'station'], p=[0.4, 0.3, 0.15, 0.15])
            
            # Calculate surge multiplier based on factors
            surge_multiplier = self._calculate_synthetic_surge(
                demand_level, driver_availability, weather, hour, is_weekend, location_type
            )
            
            # Final pricing
            final_price = max(self.minimum_fare, base_price * surge_multiplier)
            
            # Simulate conversion rate (probability of ride acceptance)
            conversion_rate = self._calculate_conversion_rate(final_price, base_price, demand_level)
            
            # Simulate actual bookings
            was_booked = np.random.random() < conversion_rate
            
            # Revenue calculation
            revenue = final_price * (1 - self.driver_commission_rate) if was_booked else 0
            
            data.append({
                'distance': distance,
                'base_price': base_price,
                'hour': hour,
                'day_of_week': day_of_week,
                'is_weekend': is_weekend,
                'demand_level': demand_level,
                'driver_availability': driver_availability,
                'weather': weather,
                'location_type': location_type,
                'surge_multiplier': surge_multiplier,
                'final_price': final_price,
                'conversion_rate': conversion_rate,
                'was_booked': was_booked,
                'revenue': revenue
            })
        
        return pd.DataFrame(data)
    
    def _calculate_synthetic_surge(self, demand, drivers, weather, hour, is_weekend, location):
        """Calculate synthetic surge multiplier for training data"""
        surge = 1.0
        
        # Demand factor
        if demand == 'high':
            surge *= 1.5
        elif demand == 'medium':
            surge *= 1.2
        
        # Driver availability factor
        if drivers < 10:
            surge *= 1.4
        elif drivers < 20:
            surge *= 1.2
        
        # Weather factor
        if weather == 'rain':
            surge *= 1.3
        elif weather == 'storm':
            surge *= 1.6
        
        # Time factor
        if hour in [8, 9, 18, 19]:  # Peak hours
            surge *= 1.3
        elif hour in [7, 10, 17, 20]:
            surge *= 1.1
        
        # Weekend factor
        if is_weekend and hour in range(10, 22):
            surge *= 1.1
        
        # Location factor
        if location == 'airport':
            surge *= 1.2
        elif location == 'station':
            surge *= 1.15
        
        return min(self.maximum_surge_multiplier, surge)
    
    def _calculate_conversion_rate(self, final_price, base_price, demand_level):
        """Calculate conversion rate based on pricing"""
        price_ratio = final_price / base_price
        
        # Base conversion rate by demand
        base_conversion = {
            'low': 0.6,
            'medium': 0.75,
            'high': 0.85
        }[demand_level]
        
        # Price sensitivity
        if price_ratio <= 1.2:
            conversion = base_conversion
        elif price_ratio <= 1.5:
            conversion = base_conversion * 0.9
        elif price_ratio <= 2.0:
            conversion = base_conversion * 0.7
        else:
            conversion = base_conversion * 0.5
        
        return max(0.1, min(0.95, conversion))
    
    def _prepare_pricing_features(self, data):
        """Prepare features for pricing model"""
        feature_data = data.copy()
        
        # Encode categorical variables
        categorical_columns = ['demand_level', 'weather', 'location_type']
        for col in categorical_columns:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                feature_data[col + '_encoded'] = self.label_encoders[col].fit_transform(feature_data[col])
            else:
                feature_data[col + '_encoded'] = self.label_encoders[col].transform(feature_data[col])
        
        # Create time-based features
        feature_data['hour_sin'] = np.sin(2 * np.pi * feature_data['hour'] / 24)
        feature_data['hour_cos'] = np.cos(2 * np.pi * feature_data['hour'] / 24)
        feature_data['day_sin'] = np.sin(2 * np.pi * feature_data['day_of_week'] / 7)
        feature_data['day_cos'] = np.cos(2 * np.pi * feature_data['day_of_week'] / 7)
        
        # Feature columns
        feature_columns = [
            'distance', 'base_price', 'hour', 'day_of_week', 'is_weekend',
            'driver_availability', 'demand_level_encoded', 'weather_encoded',
            'location_type_encoded', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos'
        ]
        
        X = feature_data[feature_columns]
        y = feature_data['revenue']  # Predict revenue, not just price
        
        return X, y
    
    def _train_pricing_model(self, X, y):
        """Train the pricing optimization model"""
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.pricing_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        self.pricing_model.fit(X_train_scaled, y_train)
        
        # Evaluate
        train_score = self.pricing_model.score(X_train_scaled, y_train)
        test_score = self.pricing_model.score(X_test_scaled, y_test)
        
        logger.info(f"Pricing model performance - Train R2: {train_score:.3f}, Test R2: {test_score:.3f}")
        
        # Save model
        self._save_pricing_model()
    
    def _save_pricing_model(self):
        """Save trained pricing model"""
        joblib.dump(self.pricing_model, "models/pricing_model.joblib")
        joblib.dump(self.scaler, "models/pricing_scaler.joblib")
        joblib.dump(self.label_encoders, "models/pricing_encoders.joblib")
        logger.info("Pricing model saved successfully")
    
    async def optimize_pricing(self, base_distance: float, current_demand: str, 
                             weather_condition: str, time_of_day: str, 
                             driver_availability: int, historical_data: Optional[Dict] = None):
        """Optimize pricing using ML model and A/B testing"""
        try:
            # Get A/B test variant for this request
            ab_variant = self._get_ab_test_variant()
            
            # Prepare features for ML model
            features = await self._prepare_pricing_prediction_features(
                base_distance, current_demand, weather_condition, 
                time_of_day, driver_availability
            )
            
            # Get base pricing recommendation from ML model
            ml_recommendation = self._get_ml_pricing_recommendation(features)
            
            # Apply A/B test variant adjustments
            final_pricing = self._apply_ab_test_variant(ml_recommendation, ab_variant)
            
            # Calculate confidence based on model certainty and market conditions
            confidence = self._calculate_pricing_confidence(features, ml_recommendation)
            
            # Analyze pricing factors
            factors = self._analyze_pricing_factors(features, final_pricing)
            
            result = {
                "recommended_price": round(final_pricing['price'], 2),
                "surge_multiplier": round(final_pricing['surge_multiplier'], 2),
                "confidence": round(confidence, 2),
                "factors": factors,
                "ab_test_variant": ab_variant,
                "base_price": round(base_distance * self.base_price_per_km, 2),
                "driver_earnings": round(final_pricing['price'] * self.driver_commission_rate, 2),
                "platform_revenue": round(final_pricing['price'] * (1 - self.driver_commission_rate), 2),
                "estimated_conversion_rate": round(final_pricing['conversion_rate'], 2),
                "timestamp": datetime.now()
            }
            
            # Cache result
            cache_key = f"pricing_optimization:{hash(str(features))}"
            await self.cache_manager.set(cache_key, result, expire=300)  # 5 minutes
            
            # Log A/B test data for analysis
            await self._log_ab_test_data(features, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Pricing optimization error: {e}")
            raise
    
    async def _prepare_pricing_prediction_features(self, distance, demand, weather, time_of_day, drivers):
        """Prepare features for pricing prediction"""
        current_time = datetime.now()
        
        # Parse time_of_day
        if time_of_day == 'peak':
            hour = 18  # Evening peak
        elif time_of_day == 'morning':
            hour = 8   # Morning peak
        elif time_of_day == 'night':
            hour = 22  # Night
        else:
            hour = current_time.hour
        
        features = {
            'distance': distance,
            'base_price': distance * self.base_price_per_km,
            'hour': hour,
            'day_of_week': current_time.weekday(),
            'is_weekend': current_time.weekday() >= 5,
            'driver_availability': drivers,
            'demand_level': demand,
            'weather': weather,
            'location_type': 'commercial'  # Default, could be enhanced with actual location data
        }
        
        return features
    
    def _get_ml_pricing_recommendation(self, features):
        """Get pricing recommendation from ML model"""
        # Encode categorical features
        encoded_features = features.copy()
        
        for col in ['demand_level', 'weather', 'location_type']:
            if col in self.label_encoders:
                try:
                    encoded_features[col + '_encoded'] = self.label_encoders[col].transform([features[col]])[0]
                except ValueError:
                    encoded_features[col + '_encoded'] = 0
            else:
                encoded_features[col + '_encoded'] = 0
        
        # Add time-based features
        encoded_features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        encoded_features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        encoded_features['day_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        encoded_features['day_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        
        # Prepare feature vector
        feature_columns = [
            'distance', 'base_price', 'hour', 'day_of_week', 'is_weekend',
            'driver_availability', 'demand_level_encoded', 'weather_encoded',
            'location_type_encoded', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos'
        ]
        
        feature_vector = np.array([[encoded_features[col] for col in feature_columns]])
        feature_vector_scaled = self.scaler.transform(feature_vector)
        
        # Predict revenue
        predicted_revenue = self.pricing_model.predict(feature_vector_scaled)[0]
        
        # Calculate optimal price based on predicted revenue
        # This is a simplified approach - in practice, you'd use more sophisticated optimization
        base_price = features['base_price']
        
        # Estimate surge multiplier based on conditions
        surge_multiplier = self._calculate_surge_multiplier(features)
        optimal_price = max(self.minimum_fare, base_price * surge_multiplier)
        
        # Estimate conversion rate
        conversion_rate = self._estimate_conversion_rate(optimal_price, base_price, features['demand_level'])
        
        return {
            'price': optimal_price,
            'surge_multiplier': surge_multiplier,
            'predicted_revenue': predicted_revenue,
            'conversion_rate': conversion_rate
        }
    
    def _calculate_surge_multiplier(self, features):
        """Calculate surge multiplier based on current conditions"""
        surge = 1.0
        
        # Demand factor
        if features['demand_level'] == 'high':
            surge *= 1.4
        elif features['demand_level'] == 'medium':
            surge *= 1.15
        
        # Driver availability factor
        if features['driver_availability'] < 10:
            surge *= 1.3
        elif features['driver_availability'] < 20:
            surge *= 1.15
        
        # Weather factor
        if features['weather'] == 'rain':
            surge *= 1.25
        elif features['weather'] == 'storm':
            surge *= 1.5
        
        # Time factor
        if features['hour'] in [8, 9, 18, 19]:
            surge *= 1.25
        elif features['hour'] in [7, 10, 17, 20]:
            surge *= 1.1
        
        # Weekend factor
        if features['is_weekend'] and features['hour'] in range(10, 22):
            surge *= 1.1
        
        return min(self.maximum_surge_multiplier, surge)
    
    def _estimate_conversion_rate(self, price, base_price, demand_level):
        """Estimate conversion rate for given pricing"""
        price_ratio = price / base_price
        
        base_rates = {'low': 0.6, 'medium': 0.75, 'high': 0.85}
        base_rate = base_rates.get(demand_level, 0.7)
        
        # Price sensitivity curve
        if price_ratio <= 1.2:
            return base_rate
        elif price_ratio <= 1.5:
            return base_rate * 0.9
        elif price_ratio <= 2.0:
            return base_rate * 0.75
        else:
            return base_rate * 0.6
    
    def _get_ab_test_variant(self):
        """Get A/B test variant for this request"""
        # Simple random assignment - in practice, you'd use user ID for consistent assignment
        variants = list(self.ab_test_variants.keys())
        weights = [0.25, 0.4, 0.25, 0.1]  # Standard gets most traffic
        return np.random.choice(variants, p=weights)
    
    def _apply_ab_test_variant(self, ml_recommendation, variant):
        """Apply A/B test variant adjustments to ML recommendation"""
        variant_config = self.ab_test_variants[variant]
        
        adjusted_price = (ml_recommendation['price'] * 
                         variant_config['base_adjustment'])
        
        adjusted_surge = (ml_recommendation['surge_multiplier'] * 
                         variant_config['surge_multiplier'])
        
        # Recalculate conversion rate for adjusted price
        base_price = adjusted_price / adjusted_surge
        adjusted_conversion = self._estimate_conversion_rate(
            adjusted_price, base_price, 'medium'  # Default demand level
        )
        
        return {
            'price': adjusted_price,
            'surge_multiplier': adjusted_surge,
            'conversion_rate': adjusted_conversion,
            'predicted_revenue': ml_recommendation['predicted_revenue'] * variant_config['base_adjustment']
        }
    
    def _calculate_pricing_confidence(self, features, ml_recommendation):
        """Calculate confidence in pricing recommendation"""
        confidence = 0.8  # Base confidence
        
        # Adjust based on demand certainty
        if features['demand_level'] == 'high':
            confidence += 0.1
        elif features['demand_level'] == 'low':
            confidence -= 0.1
        
        # Adjust based on driver availability
        if features['driver_availability'] > 20:
            confidence += 0.05
        elif features['driver_availability'] < 5:
            confidence -= 0.1
        
        # Weather uncertainty
        if features['weather'] in ['storm', 'rain']:
            confidence -= 0.05
        
        return max(0.5, min(0.95, confidence))
    
    def _analyze_pricing_factors(self, features, pricing):
        """Analyze factors contributing to pricing decision"""
        factors = {}
        
        # Demand impact
        if features['demand_level'] == 'high':
            factors['high_demand'] = 0.3
        elif features['demand_level'] == 'medium':
            factors['moderate_demand'] = 0.15
        
        # Supply impact
        if features['driver_availability'] < 10:
            factors['low_driver_supply'] = 0.25
        elif features['driver_availability'] < 20:
            factors['limited_driver_supply'] = 0.15
        
        # Weather impact
        if features['weather'] == 'rain':
            factors['rain_surge'] = 0.2
        elif features['weather'] == 'storm':
            factors['storm_surge'] = 0.4
        
        # Time impact
        if features['hour'] in [8, 9, 18, 19]:
            factors['peak_hour_surge'] = 0.25
        
        # Weekend impact
        if features['is_weekend']:
            factors['weekend_adjustment'] = 0.1
        
        return factors
    
    async def _log_ab_test_data(self, features, result):
        """Log A/B test data for analysis"""
        try:
            ab_test_log = {
                'timestamp': datetime.now(),
                'variant': result['ab_test_variant'],
                'features': features,
                'recommended_price': result['recommended_price'],
                'surge_multiplier': result['surge_multiplier'],
                'estimated_conversion': result['estimated_conversion_rate']
            }
            
            # In practice, this would be stored in a database for analysis
            cache_key = f"ab_test_log:{datetime.now().strftime('%Y%m%d%H')}"
            existing_logs = await self.cache_manager.get(cache_key) or []
            existing_logs.append(ab_test_log)
            await self.cache_manager.set(cache_key, existing_logs, expire=86400)  # 24 hours
            
        except Exception as e:
            logger.warning(f"Failed to log A/B test data: {e}")
    
    async def analyze_revenue_trends(self):
        """Analyze revenue trends and provide insights"""
        try:
            # Get recent pricing data
            recent_data = await self._get_recent_pricing_data()
            
            # Calculate trends
            trends = {
                'daily_revenue': self._calculate_daily_revenue_trend(recent_data),
                'surge_effectiveness': self._analyze_surge_effectiveness(recent_data),
                'conversion_rates': self._analyze_conversion_trends(recent_data),
                'ab_test_performance': await self._analyze_ab_test_performance(),
                'recommendations': self._generate_revenue_recommendations(recent_data)
            }
            
            return trends
            
        except Exception as e:
            logger.error(f"Revenue trend analysis error: {e}")
            return {
                'daily_revenue': {'trend': 'stable', 'change_percent': 0},
                'surge_effectiveness': {'average_multiplier': 1.2, 'success_rate': 0.75},
                'conversion_rates': {'average': 0.72, 'trend': 'stable'},
                'recommendations': ['Monitor pricing performance', 'Continue current strategy']
            }
    
    async def _get_recent_pricing_data(self):
        """Get recent pricing and booking data"""
        # Mock data - in practice, query from database
        return []
    
    def _calculate_daily_revenue_trend(self, data):
        """Calculate daily revenue trend"""
        return {'trend': 'increasing', 'change_percent': 5.2}
    
    def _analyze_surge_effectiveness(self, data):
        """Analyze surge pricing effectiveness"""
        return {'average_multiplier': 1.3, 'success_rate': 0.78}
    
    def _analyze_conversion_trends(self, data):
        """Analyze conversion rate trends"""
        return {'average': 0.74, 'trend': 'stable'}
    
    async def _analyze_ab_test_performance(self):
        """Analyze A/B test performance"""
        performance = {}
        
        for variant in self.ab_test_variants.keys():
            # Mock performance data
            performance[variant] = {
                'conversion_rate': np.random.uniform(0.65, 0.85),
                'average_revenue': np.random.uniform(40, 60),
                'sample_size': np.random.randint(100, 500)
            }
        
        return performance
    
    def _generate_revenue_recommendations(self, data):
        """Generate revenue optimization recommendations"""
        return [
            "Consider increasing surge multiplier during peak hours",
            "Test lower pricing in low-demand areas to increase conversion",
            "Implement dynamic pricing based on real-time demand"
        ]
    
    async def retrain_model(self):
        """Retrain pricing model with latest data"""
        try:
            # Get latest pricing and revenue data
            recent_data = await self._get_recent_pricing_data()
            
            if len(recent_data) > 100:
                # Retrain model with new data
                logger.info("Retraining pricing model with latest data...")
                # Implementation would go here
            else:
                logger.warning("Insufficient data for pricing model retraining")
                
        except Exception as e:
            logger.error(f"Pricing model retraining error: {e}")
    
    async def get_model_status(self):
        """Get current pricing model status"""
        return {
            "model_type": "RandomForest Revenue Optimizer",
            "last_trained": datetime.now().isoformat(),
            "ab_test_variants": len(self.ab_test_variants),
            "status": "active"
        }
