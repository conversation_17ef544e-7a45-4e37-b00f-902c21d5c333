"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

export function NavLinks() {
  const pathname = usePathname()

  const links = [
    { href: "/", label: "Home" },
    { href: "/find-ride", label: "Find Ride" },
    { href: "/offer-ride", label: "Offer Ride" },
    { href: "/payments", label: "Payments" },
    { href: "/dashboard", label: "Dashboard" },
  ]

  return (
    <div className="flex items-center space-x-4">
      {links.map((link) => (
        <Link
          key={link.href}
          href={link.href}
          className={cn(
            "text-lg font-medium transition-colors hover:text-primary sm:text-sm",
            pathname === link.href ? "text-primary" : "text-foreground/60",
          )}
        >
          {link.label}
        </Link>
      ))}
    </div>
  )
}

