import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { CreditCard, Wallet, IndianRupee, Plus, Clock, ArrowDownUp } from "lucide-react"
import { PaymentMethods } from "@/components/payment-methods"
import { TransactionHistory } from "@/components/transaction-history"

export default function PaymentsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Payments & Wallet</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Tabs defaultValue="wallet" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="wallet">Wallet</TabsTrigger>
              <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
            </TabsList>

            <TabsContent value="wallet" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Your Wallet</CardTitle>
                  <CardDescription>Manage your wallet balance and add funds</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex flex-col md:flex-row gap-6">
                    <Card className="flex-1 bg-primary/5">
                      <CardHeader className="pb-2">
                        <CardDescription>Available Balance</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center">
                          <IndianRupee className="h-5 w-5 mr-1 text-primary" />
                          <span className="text-3xl font-bold">1,250</span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="flex-1 bg-muted/50">
                      <CardHeader className="pb-2">
                        <CardDescription>Cashback Earned</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center">
                          <IndianRupee className="h-5 w-5 mr-1 text-primary" />
                          <span className="text-3xl font-bold">320</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">Add Money to Wallet</h3>
                    <div className="grid grid-cols-4 gap-2 mb-4">
                      {[100, 200, 500, 1000].map((amount) => (
                        <Button key={amount} variant="outline" className="h-12">
                          ₹{amount}
                        </Button>
                      ))}
                    </div>

                    <div className="flex gap-2 mb-6">
                      <div className="flex-grow">
                        <Label htmlFor="custom-amount">Custom Amount</Label>
                        <div className="flex mt-2">
                          <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted">
                            <IndianRupee className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <Input
                            id="custom-amount"
                            type="number"
                            placeholder="Enter amount"
                            className="rounded-l-none"
                          />
                        </div>
                      </div>
                      <div className="flex items-end">
                        <Button className="h-10 mt-2">Add Money</Button>
                      </div>
                    </div>

                    <RadioGroup defaultValue="card" className="space-y-3">
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <RadioGroupItem value="card" id="card" />
                        <Label htmlFor="card" className="flex items-center">
                          <CreditCard className="h-4 w-4 mr-2" />
                          HDFC Bank Credit Card (••••4582)
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <RadioGroupItem value="upi" id="upi" />
                        <Label htmlFor="upi" className="flex items-center">
                          <img src="/placeholder.svg?height=16&width=16" alt="UPI" className="h-4 w-4 mr-2" />
                          UPI - user@okbank
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-md p-3">
                        <RadioGroupItem value="new" id="new" />
                        <Label htmlFor="new" className="flex items-center">
                          <Plus className="h-4 w-4 mr-2" />
                          Add New Payment Method
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">Proceed to Add Money</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="payment-methods" className="mt-6">
              <PaymentMethods />
            </TabsContent>

            <TabsContent value="transactions" className="mt-6">
              <TransactionHistory />
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button variant="outline" className="w-full justify-start">
                <Wallet className="mr-2 h-4 w-4" />
                Add Money to Wallet
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <ArrowDownUp className="mr-2 h-4 w-4" />
                Transfer to Bank
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <CreditCard className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Clock className="mr-2 h-4 w-4" />
                Scheduled Payments
              </Button>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Payment Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 text-sm">
              <div className="space-y-2">
                <h3 className="font-medium">Auto-Recharge</h3>
                <p className="text-muted-foreground">
                  Enable auto-recharge to automatically add money when your balance falls below ₹100.
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">Cashback Rewards</h3>
                <p className="text-muted-foreground">
                  Get 5% cashback on all rides paid through wallet (up to ₹50 per ride).
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="font-medium">Secure Payments</h3>
                <p className="text-muted-foreground">
                  All transactions are secured with 256-bit encryption and comply with RBI guidelines.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

