'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  ChevronRight, 
  Home, 
  Car, 
  Bike, 
  Package, 
  Building2,
  User,
  Settings,
  Bell,
  Menu,
  X,
  MapPin,
  Clock,
  Star,
  TrendingUp,
  Calendar,
  CreditCard,
  Shield,
  HelpCircle,
  LogOut
} from 'lucide-react';

interface NavigationItem {
  label: string;
  href: string;
  icon?: any;
  badge?: string | number;
  children?: NavigationItem[];
  description?: string;
}

interface BreadcrumbItem {
  label: string;
  href?: string;
  icon?: any;
}

interface AdvancedNavigationProps {
  user?: {
    name: string;
    email: string;
    role: string;
    avatar?: string;
  };
  currentPath?: string;
}

interface SearchResult {
  id: string;
  title: string;
  description: string;
  category: string;
  href: string;
  icon?: any;
}

// Main Navigation Component
export default function AdvancedNavigation({ user, currentPath }: AdvancedNavigationProps) {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  // Navigation structure
  const navigationItems: NavigationItem[] = [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      description: 'Overview and quick actions',
    },
    {
      label: 'Rides',
      href: '/rides',
      icon: Car,
      badge: '3',
      description: 'Book and manage rides',
      children: [
        { label: 'Book Ride', href: '/rides/book', icon: MapPin },
        { label: 'My Rides', href: '/rides/history', icon: Clock },
        { label: 'Scheduled', href: '/rides/scheduled', icon: Calendar },
        { label: 'Favorites', href: '/rides/favorites', icon: Star },
      ],
    },
    {
      label: 'Vehicles',
      href: '/vehicles',
      icon: Bike,
      description: 'Bikes and scooters',
      children: [
        { label: 'Find Nearby', href: '/vehicles/nearby', icon: MapPin },
        { label: 'My Rentals', href: '/vehicles/rentals', icon: Clock },
        { label: 'Reservations', href: '/vehicles/reservations', icon: Calendar },
      ],
    },
    {
      label: 'Delivery',
      href: '/delivery',
      icon: Package,
      badge: 'New',
      description: 'Food and package delivery',
      children: [
        { label: 'Food Delivery', href: '/delivery/food', icon: Package },
        { label: 'Package Delivery', href: '/delivery/packages', icon: Package },
        { label: 'Track Orders', href: '/delivery/track', icon: MapPin },
        { label: 'Order History', href: '/delivery/history', icon: Clock },
      ],
    },
    {
      label: 'Corporate',
      href: '/corporate',
      icon: Building2,
      description: 'Business travel solutions',
      children: [
        { label: 'Dashboard', href: '/corporate/dashboard', icon: TrendingUp },
        { label: 'Book Travel', href: '/corporate/book', icon: Car },
        { label: 'Analytics', href: '/corporate/analytics', icon: TrendingUp },
        { label: 'Team Management', href: '/corporate/team', icon: User },
      ],
    },
  ];

  // Search functionality
  useEffect(() => {
    if (searchQuery.length > 2) {
      setIsLoading(true);
      // Simulate search API call
      setTimeout(() => {
        const mockResults: SearchResult[] = [
          {
            id: '1',
            title: 'Book a Ride',
            description: 'Find and book rides with verified drivers',
            category: 'Rides',
            href: '/rides/book',
            icon: Car,
          },
          {
            id: '2',
            title: 'Find Nearby Bikes',
            description: 'Locate available bikes and scooters',
            category: 'Vehicles',
            href: '/vehicles/nearby',
            icon: Bike,
          },
          {
            id: '3',
            title: 'Order Food',
            description: 'Get food delivered from local restaurants',
            category: 'Delivery',
            href: '/delivery/food',
            icon: Package,
          },
          {
            id: '4',
            title: 'Corporate Dashboard',
            description: 'View business travel analytics',
            category: 'Corporate',
            href: '/corporate/dashboard',
            icon: Building2,
          },
        ].filter(item => 
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
        );
        
        setSearchResults(mockResults);
        setIsLoading(false);
      }, 300);
    } else {
      setSearchResults([]);
    }
  }, [searchQuery]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.length > 0) {
      setIsSearchOpen(true);
    }
  };

  const handleSearchSelect = (result: SearchResult) => {
    router.push(result.href);
    setIsSearchOpen(false);
    setSearchQuery('');
  };

  return (
    <nav className="bg-white border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Primary Navigation */}
          <div className="flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M</span>
              </div>
              <span className="text-xl font-bold text-gray-900">MobilityHub</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <NavigationDropdown key={item.href} item={item} currentPath={pathname} />
              ))}
            </div>
          </div>

          {/* Search and Actions */}
          <div className="flex items-center space-x-4">
            {/* Global Search */}
            <div className="relative">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search anything..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  onFocus={() => searchQuery.length > 0 && setIsSearchOpen(true)}
                  className="pl-10 pr-4 w-64 lg:w-80"
                />
              </div>

              {/* Search Results Dropdown */}
              {isSearchOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                  {isLoading ? (
                    <div className="p-4 text-center text-gray-500">
                      <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent mx-auto"></div>
                      <p className="mt-2 text-sm">Searching...</p>
                    </div>
                  ) : searchResults.length > 0 ? (
                    <div className="py-2">
                      {searchResults.map((result) => {
                        const Icon = result.icon;
                        return (
                          <button
                            key={result.id}
                            onClick={() => handleSearchSelect(result)}
                            className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                          >
                            <div className="flex items-center space-x-3">
                              <div className="p-2 bg-blue-100 rounded-lg">
                                <Icon className="h-4 w-4 text-blue-600" />
                              </div>
                              <div className="flex-1">
                                <div className="font-medium text-gray-900">{result.title}</div>
                                <div className="text-sm text-gray-500">{result.description}</div>
                                <Badge variant="secondary" className="mt-1 text-xs">
                                  {result.category}
                                </Badge>
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  ) : searchQuery.length > 2 ? (
                    <div className="p-4 text-center text-gray-500">
                      <p className="text-sm">No results found for "{searchQuery}"</p>
                    </div>
                  ) : null}
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <Button variant="ghost" size="sm" className="relative">
              <Bell className="h-4 w-4" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-red-500 text-white">
                3
              </Badge>
            </Button>

            {/* User Menu */}
            {user ? (
              <UserMenu user={user} />
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/login">
                  <Button variant="ghost" size="sm">Sign In</Button>
                </Link>
                <Link href="/register">
                  <Button size="sm">Get Started</Button>
                </Link>
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="sm" className="lg:hidden">
              <Menu className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Click outside to close search */}
      {isSearchOpen && (
        <div 
          className="fixed inset-0 z-30" 
          onClick={() => setIsSearchOpen(false)}
        />
      )}
    </nav>
  );
}

// Navigation Dropdown Component
function NavigationDropdown({ item, currentPath }: { item: NavigationItem; currentPath: string }) {
  const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/');
  const Icon = item.icon;

  if (!item.children) {
    return (
      <Link
        href={item.href}
        className={cn(
          'flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors',
          isActive 
            ? 'bg-blue-100 text-blue-700' 
            : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
        )}
      >
        <Icon className="h-4 w-4" />
        <span>{item.label}</span>
        {item.badge && (
          <Badge variant="secondary" className="text-xs">
            {item.badge}
          </Badge>
        )}
      </Link>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            'flex items-center space-x-2 px-3 py-2 text-sm font-medium',
            isActive 
              ? 'bg-blue-100 text-blue-700' 
              : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
          )}
        >
          <Icon className="h-4 w-4" />
          <span>{item.label}</span>
          {item.badge && (
            <Badge variant="secondary" className="text-xs">
              {item.badge}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 p-2">
        <DropdownMenuLabel>
          <div className="flex items-center space-x-2">
            <Icon className="h-4 w-4" />
            <span>{item.label}</span>
          </div>
          {item.description && (
            <p className="text-xs text-gray-500 mt-1 font-normal">{item.description}</p>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {item.children.map((child) => {
          const ChildIcon = child.icon;
          return (
            <DropdownMenuItem key={child.href} asChild>
              <Link href={child.href} className="flex items-center space-x-3 cursor-pointer">
                <ChildIcon className="h-4 w-4 text-gray-500" />
                <span>{child.label}</span>
              </Link>
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// User Menu Component
function UserMenu({ user }: { user: { name: string; email: string; role: string; avatar?: string } }) {
  const router = useRouter();

  const handleLogout = () => {
    localStorage.removeItem('token');
    router.push('/');
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full">
          <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 flex items-center justify-center text-white font-semibold text-sm">
            {user.avatar ? (
              <img src={user.avatar} alt={user.name} className="h-8 w-8 rounded-full object-cover" />
            ) : (
              user.name.charAt(0)
            )}
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.name}</p>
            <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
            <Badge variant="secondary" className="w-fit text-xs mt-1">
              {user.role}
            </Badge>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/profile" className="cursor-pointer">
            <User className="mr-2 h-4 w-4" />
            <span>Profile</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/dashboard" className="cursor-pointer">
            <Home className="mr-2 h-4 w-4" />
            <span>Dashboard</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/billing" className="cursor-pointer">
            <CreditCard className="mr-2 h-4 w-4" />
            <span>Billing</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/settings" className="cursor-pointer">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link href="/help" className="cursor-pointer">
            <HelpCircle className="mr-2 h-4 w-4" />
            <span>Help & Support</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href="/safety" className="cursor-pointer">
            <Shield className="mr-2 h-4 w-4" />
            <span>Safety</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
