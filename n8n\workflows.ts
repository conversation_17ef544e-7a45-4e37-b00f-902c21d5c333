// n8n Workflow Templates and Definitions
// These are JSON workflow definitions that can be imported into n8n

export const RIDE_BOOKING_WORKFLOW = {
  "name": "Ride Booking Automation",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "ride-request",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-ride-request",
      "name": "Webhook - Ride Request",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300],
      "webhookId": "ride-request-webhook"
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.trigger}}",
              "operation": "equal",
              "value2": "ride_requested"
            }
          ]
        }
      },
      "id": "if-ride-requested",
      "name": "If Ride Requested",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "url": "={{$env.APP_URL}}/api/rides/find-drivers",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer {{$env.API_TOKEN}}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "rideId",
              "value": "={{$json.data.ride.id}}"
            },
            {
              "name": "pickupLocation",
              "value": "={{$json.data.ride.pickupLocation}}"
            },
            {
              "name": "radius",
              "value": "5"
            }
          ]
        }
      },
      "id": "find-drivers",
      "name": "Find Available Drivers",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [680, 200]
    },
    {
      "parameters": {
        "conditions": {
          "number": [
            {
              "value1": "={{$json.drivers.length}}",
              "operation": "larger",
              "value2": 0
            }
          ]
        }
      },
      "id": "if-drivers-found",
      "name": "If Drivers Found",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [900, 200]
    },
    {
      "parameters": {
        "url": "={{$env.APP_URL}}/api/rides/request-driver",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer {{$env.API_TOKEN}}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "rideId",
              "value": "={{$json.data.ride.id}}"
            },
            {
              "name": "driverId",
              "value": "={{$json.drivers[0].id}}"
            }
          ]
        }
      },
      "id": "request-driver",
      "name": "Request Driver",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1120, 100]
    },
    {
      "parameters": {
        "url": "={{$env.WEBSOCKET_URL}}/notify",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "userId",
              "value": "={{$json.drivers[0].id}}"
            },
            {
              "name": "type",
              "value": "ride_request"
            },
            {
              "name": "data",
              "value": "={{$json.data}}"
            }
          ]
        }
      },
      "id": "notify-driver",
      "name": "Notify Driver",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1340, 100]
    },
    {
      "parameters": {
        "url": "={{$env.WEBSOCKET_URL}}/notify",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "userId",
              "value": "={{$json.data.ride.riderId}}"
            },
            {
              "name": "type",
              "value": "no_drivers"
            },
            {
              "name": "message",
              "value": "No drivers available in your area. Please try again later."
            }
          ]
        }
      },
      "id": "notify-no-drivers",
      "name": "Notify No Drivers",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1120, 300]
    }
  ],
  "connections": {
    "Webhook - Ride Request": {
      "main": [
        [
          {
            "node": "If Ride Requested",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Ride Requested": {
      "main": [
        [
          {
            "node": "Find Available Drivers",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Find Available Drivers": {
      "main": [
        [
          {
            "node": "If Drivers Found",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Drivers Found": {
      "main": [
        [
          {
            "node": "Request Driver",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Notify No Drivers",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Request Driver": {
      "main": [
        [
          {
            "node": "Notify Driver",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {},
  "versionId": "1"
};

export const DRIVER_NOTIFICATION_WORKFLOW = {
  "name": "Driver Notification System",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "driver-assignment",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-driver-assignment",
      "name": "Webhook - Driver Assignment",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.recipient.role}}",
              "operation": "equal",
              "value2": "driver"
            }
          ]
        }
      },
      "id": "if-driver-notification",
      "name": "If Driver Notification",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "url": "={{$env.WEBSOCKET_URL}}/notify",
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "userId",
              "value": "={{$json.recipient.userId}}"
            },
            {
              "name": "type",
              "value": "={{$json.message.type}}"
            },
            {
              "name": "title",
              "value": "={{$json.message.title}}"
            },
            {
              "name": "body",
              "value": "={{$json.message.body}}"
            }
          ]
        }
      },
      "id": "websocket-notification",
      "name": "WebSocket Notification",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [680, 200]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.channels}}",
              "operation": "contains",
              "value2": "sms"
            }
          ]
        }
      },
      "id": "if-sms-enabled",
      "name": "If SMS Enabled",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [900, 200]
    },
    {
      "parameters": {
        "url": "={{$env.SMS_API_URL}}/send",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer {{$env.SMS_API_KEY}}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "to",
              "value": "={{$json.recipient.phone}}"
            },
            {
              "name": "message",
              "value": "={{$json.message.title}}: {{$json.message.body}}"
            }
          ]
        }
      },
      "id": "send-sms",
      "name": "Send SMS",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1120, 100]
    }
  ],
  "connections": {
    "Webhook - Driver Assignment": {
      "main": [
        [
          {
            "node": "If Driver Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Driver Notification": {
      "main": [
        [
          {
            "node": "WebSocket Notification",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "WebSocket Notification": {
      "main": [
        [
          {
            "node": "If SMS Enabled",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If SMS Enabled": {
      "main": [
        [
          {
            "node": "Send SMS",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {},
  "versionId": "1"
};

export const PAYMENT_PROCESSING_WORKFLOW = {
  "name": "Payment Processing Automation",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "payment-processing",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-payment",
      "name": "Webhook - Payment",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [240, 300]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{$json.trigger}}",
              "operation": "equal",
              "value2": "payment_processed"
            }
          ]
        }
      },
      "id": "if-payment-processed",
      "name": "If Payment Processed",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [460, 300]
    },
    {
      "parameters": {
        "url": "={{$env.APP_URL}}/api/transactions/create",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer {{$env.API_TOKEN}}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "rideId",
              "value": "={{$json.payment.rideId}}"
            },
            {
              "name": "amount",
              "value": "={{$json.payment.amount}}"
            },
            {
              "name": "method",
              "value": "={{$json.payment.method}}"
            },
            {
              "name": "status",
              "value": "completed"
            }
          ]
        }
      },
      "id": "create-transaction",
      "name": "Create Transaction",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [680, 200]
    },
    {
      "parameters": {
        "url": "={{$env.APP_URL}}/api/drivers/update-earnings",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer {{$env.API_TOKEN}}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "driverId",
              "value": "={{$json.driver.id}}"
            },
            {
              "name": "earnings",
              "value": "={{$json.driver.earnings}}"
            }
          ]
        }
      },
      "id": "update-driver-earnings",
      "name": "Update Driver Earnings",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [900, 200]
    },
    {
      "parameters": {
        "url": "={{$env.EMAIL_API_URL}}/send",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "Authorization",
              "value": "Bearer {{$env.EMAIL_API_KEY}}"
            }
          ]
        },
        "sendBody": true,
        "bodyParameters": {
          "parameters": [
            {
              "name": "to",
              "value": "={{$json.rider.email}}"
            },
            {
              "name": "subject",
              "value": "Payment Confirmation - Ride #{{$json.payment.rideId}}"
            },
            {
              "name": "template",
              "value": "payment_confirmation"
            },
            {
              "name": "data",
              "value": "={{$json}}"
            }
          ]
        }
      },
      "id": "send-receipt",
      "name": "Send Receipt",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 3,
      "position": [1120, 200]
    }
  ],
  "connections": {
    "Webhook - Payment": {
      "main": [
        [
          {
            "node": "If Payment Processed",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "If Payment Processed": {
      "main": [
        [
          {
            "node": "Create Transaction",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create Transaction": {
      "main": [
        [
          {
            "node": "Update Driver Earnings",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update Driver Earnings": {
      "main": [
        [
          {
            "node": "Send Receipt",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": true,
  "settings": {},
  "versionId": "1"
};

// Export all workflow templates
export const WORKFLOW_TEMPLATES = {
  RIDE_BOOKING_WORKFLOW,
  DRIVER_NOTIFICATION_WORKFLOW,
  PAYMENT_PROCESSING_WORKFLOW,
};

// Workflow deployment helper
export async function deployWorkflows(n8nBaseUrl: string, apiKey: string) {
  const workflows = Object.values(WORKFLOW_TEMPLATES);
  const results = [];

  for (const workflow of workflows) {
    try {
      const response = await fetch(`${n8nBaseUrl}/api/v1/workflows`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
        },
        body: JSON.stringify(workflow),
      });

      if (response.ok) {
        const result = await response.json();
        results.push({ name: workflow.name, success: true, id: result.id });
      } else {
        results.push({ name: workflow.name, success: false, error: await response.text() });
      }
    } catch (error) {
      results.push({ name: workflow.name, success: false, error: error.message });
    }
  }

  return results;
}
