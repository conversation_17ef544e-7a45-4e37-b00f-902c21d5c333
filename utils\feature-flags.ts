/**
 * Feature Flag Management Utility for Two-Wheeler Sharing Platform
 * Provides type-safe feature flag checking and management
 */

export interface FeatureFlagConfig {
  enabled: boolean;
  description: string;
  environments: {
    development: boolean;
    staging: boolean;
    production: boolean;
  };
  rollout_percentage: number;
  dependencies: string[];
}

export interface FeatureFlags {
  ai_enhanced_ride_matching: FeatureFlagConfig;
  autonomous_vehicle_control: FeatureFlagConfig;
  carbon_neutrality_optimization: FeatureFlagConfig;
  voice_ai_commands: FeatureFlagConfig;
  ar_navigation: FeatureFlagConfig;
  quantum_optimization: FeatureFlagConfig;
  neural_interface_integration: FeatureFlagConfig;
  smart_city_integration: FeatureFlagConfig;
  predictive_maintenance: FeatureFlagConfig;
  multi_modal_journey_planning: FeatureFlagConfig;
  enterprise_multi_tenant: FeatureFlagConfig;
  global_deployment: FeatureFlagConfig;
  advanced_analytics: FeatureFlagConfig;
  blockchain_integration: FeatureFlagConfig;
  edge_computing: FeatureFlagConfig;
}

export type FeatureFlagName = keyof FeatureFlags;

class FeatureFlagManager {
  private flags: FeatureFlags | null = null;
  private environment: 'development' | 'staging' | 'production';
  private userId?: string;

  constructor() {
    this.environment = this.getEnvironment();
    this.loadFeatureFlags();
  }

  private getEnvironment(): 'development' | 'staging' | 'production' {
    if (typeof window === 'undefined') {
      return process.env.NODE_ENV === 'production' ? 'production' : 'development';
    }
    
    const hostname = window.location.hostname;
    if (hostname.includes('staging')) return 'staging';
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) return 'development';
    return 'production';
  }

  private async loadFeatureFlags(): Promise<void> {
    try {
      // In a real implementation, this would fetch from an API
      // For now, we'll use the static configuration
      const response = await fetch('/api/feature-flags');
      if (response.ok) {
        const config = await response.json();
        this.flags = config.feature_flags;
      } else {
        // Fallback to default configuration
        this.loadDefaultFlags();
      }
    } catch (error) {
      console.warn('Failed to load feature flags, using defaults:', error);
      this.loadDefaultFlags();
    }
  }

  private loadDefaultFlags(): void {
    // Default feature flags configuration
    this.flags = {
      ai_enhanced_ride_matching: {
        enabled: true,
        description: "Enable AI-enhanced ride matching with ML predictions",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 100,
        dependencies: ["ml_service", "demand_prediction"]
      },
      autonomous_vehicle_control: {
        enabled: true,
        description: "Enable autonomous vehicle control and monitoring",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 75,
        dependencies: ["computer_vision", "edge_computing"]
      },
      carbon_neutrality_optimization: {
        enabled: true,
        description: "Enable carbon footprint tracking and optimization",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 100,
        dependencies: ["sustainability_engine", "renewable_energy"]
      },
      voice_ai_commands: {
        enabled: true,
        description: "Enable voice AI commands for mobile app",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 80,
        dependencies: ["ollama_integration", "speech_recognition"]
      },
      ar_navigation: {
        enabled: true,
        description: "Enable AR navigation features",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 75,
        dependencies: ["ar_framework", "real_time_tracking"]
      },
      quantum_optimization: {
        enabled: true,
        description: "Enable quantum computing optimization (experimental)",
        environments: { development: true, staging: true, production: false },
        rollout_percentage: 25,
        dependencies: ["quantum_processor", "hybrid_computing"]
      },
      neural_interface_integration: {
        enabled: false,
        description: "Enable neural interface capabilities (research)",
        environments: { development: true, staging: false, production: false },
        rollout_percentage: 1,
        dependencies: ["bci_hardware", "neural_security"]
      },
      smart_city_integration: {
        enabled: true,
        description: "Enable smart city API integrations",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 90,
        dependencies: ["fiware_client", "gtfs_integration"]
      },
      predictive_maintenance: {
        enabled: true,
        description: "Enable predictive maintenance for vehicles",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 100,
        dependencies: ["iot_sensors", "ml_maintenance_models"]
      },
      multi_modal_journey_planning: {
        enabled: true,
        description: "Enable multi-modal transportation planning",
        environments: { development: true, staging: true, production: false },
        rollout_percentage: 50,
        dependencies: ["public_transport_api", "unified_payment"]
      },
      enterprise_multi_tenant: {
        enabled: true,
        description: "Enable enterprise multi-tenant architecture",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 100,
        dependencies: ["tenant_isolation", "enterprise_auth"]
      },
      global_deployment: {
        enabled: true,
        description: "Enable global multi-region deployment",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 100,
        dependencies: ["localization_engine", "compliance_framework"]
      },
      advanced_analytics: {
        enabled: true,
        description: "Enable advanced analytics and reporting",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 100,
        dependencies: ["analytics_engine", "real_time_dashboards"]
      },
      blockchain_integration: {
        enabled: false,
        description: "Enable blockchain for payments and contracts",
        environments: { development: true, staging: false, production: false },
        rollout_percentage: 5,
        dependencies: ["blockchain_client", "smart_contracts"]
      },
      edge_computing: {
        enabled: true,
        description: "Enable edge computing for real-time processing",
        environments: { development: true, staging: true, production: true },
        rollout_percentage: 60,
        dependencies: ["edge_nodes", "distributed_processing"]
      }
    };
  }

  /**
   * Check if a feature flag is enabled
   */
  public isEnabled(flagName: FeatureFlagName): boolean {
    if (!this.flags) {
      console.warn(`Feature flags not loaded, defaulting ${flagName} to false`);
      return false;
    }

    const flag = this.flags[flagName];
    if (!flag) {
      console.warn(`Feature flag ${flagName} not found, defaulting to false`);
      return false;
    }

    // Check if flag is enabled globally
    if (!flag.enabled) {
      return false;
    }

    // Check environment-specific setting
    if (!flag.environments[this.environment]) {
      return false;
    }

    // Check rollout percentage (simplified - in production would use user ID)
    if (flag.rollout_percentage < 100) {
      const rolloutCheck = this.getUserRolloutValue(flagName);
      if (rolloutCheck > flag.rollout_percentage) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get feature flag configuration
   */
  public getFlag(flagName: FeatureFlagName): FeatureFlagConfig | null {
    if (!this.flags) return null;
    return this.flags[flagName] || null;
  }

  /**
   * Get all enabled feature flags
   */
  public getEnabledFlags(): FeatureFlagName[] {
    if (!this.flags) return [];
    
    return Object.keys(this.flags).filter(flagName => 
      this.isEnabled(flagName as FeatureFlagName)
    ) as FeatureFlagName[];
  }

  /**
   * Set user ID for rollout calculations
   */
  public setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Get user-specific rollout value (0-100)
   */
  private getUserRolloutValue(flagName: FeatureFlagName): number {
    if (!this.userId) {
      // Use a deterministic value based on session
      const sessionId = this.getSessionId();
      return this.hashString(`${flagName}-${sessionId}`) % 100;
    }
    
    // Use user ID for consistent rollout
    return this.hashString(`${flagName}-${this.userId}`) % 100;
  }

  /**
   * Get or create session ID
   */
  private getSessionId(): string {
    if (typeof window === 'undefined') return 'server-session';
    
    let sessionId = sessionStorage.getItem('feature-flag-session');
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2, 15);
      sessionStorage.setItem('feature-flag-session', sessionId);
    }
    return sessionId;
  }

  /**
   * Simple hash function for consistent rollout
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Refresh feature flags from server
   */
  public async refresh(): Promise<void> {
    await this.loadFeatureFlags();
  }
}

// Create singleton instance
const featureFlagManager = new FeatureFlagManager();

// Export convenience functions
export const isFeatureEnabled = (flagName: FeatureFlagName): boolean => {
  return featureFlagManager.isEnabled(flagName);
};

export const getFeatureFlag = (flagName: FeatureFlagName): FeatureFlagConfig | null => {
  return featureFlagManager.getFlag(flagName);
};

export const getEnabledFeatures = (): FeatureFlagName[] => {
  return featureFlagManager.getEnabledFlags();
};

export const setUserId = (userId: string): void => {
  featureFlagManager.setUserId(userId);
};

export const refreshFeatureFlags = async (): Promise<void> => {
  await featureFlagManager.refresh();
};

// Export the manager for advanced usage
export { featureFlagManager };

// React hook for feature flags
export const useFeatureFlag = (flagName: FeatureFlagName) => {
  const [isEnabled, setIsEnabled] = React.useState(() => 
    featureFlagManager.isEnabled(flagName)
  );

  React.useEffect(() => {
    const checkFlag = () => {
      setIsEnabled(featureFlagManager.isEnabled(flagName));
    };

    // Check immediately
    checkFlag();

    // Set up periodic refresh (every 5 minutes)
    const interval = setInterval(async () => {
      await featureFlagManager.refresh();
      checkFlag();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [flagName]);

  return isEnabled;
};

// Import React for the hook
import React from 'react';
