import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Ride } from '@/lib/models/Ride';
import { User } from '@/lib/models/User';
import { authenticateRequest } from '@/lib/auth';

// In production, this would be replaced with a proper notification service
// like Firebase Cloud Messaging, Pusher, or WebSocket connections
const pendingDriverRequests = new Map<string, {
  rideId: string;
  riderId: string;
  driverId: string;
  requestTime: Date;
  expiresAt: Date;
}>();

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (user.role !== 'rider') {
      return NextResponse.json(
        { error: 'Only riders can request drivers' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { rideId, driverId } = body;

    if (!rideId || !driverId) {
      return NextResponse.json(
        { error: 'Ride ID and driver ID are required' },
        { status: 400 }
      );
    }

    // Verify the ride exists and belongs to the user
    const ride = await Ride.findOne({ 
      _id: rideId, 
      riderId: user.userId,
      status: 'pending'
    }).populate('riderId', 'firstName lastName phone');

    if (!ride) {
      return NextResponse.json(
        { error: 'Ride not found or not in pending status' },
        { status: 404 }
      );
    }

    // Verify the driver exists and is available
    const driver = await User.findOne({
      _id: driverId,
      role: 'driver',
      isActive: true,
      'driverProfile.isApproved': true,
    });

    if (!driver) {
      return NextResponse.json(
        { error: 'Driver not found or not available' },
        { status: 404 }
      );
    }

    // Check if there's already a pending request for this ride
    const existingRequest = Array.from(pendingDriverRequests.values())
      .find(req => req.rideId === rideId && req.expiresAt > new Date());

    if (existingRequest) {
      return NextResponse.json(
        { error: 'A driver request is already pending for this ride' },
        { status: 409 }
      );
    }

    // Create a new driver request (expires in 2 minutes)
    const requestId = `${rideId}-${driverId}-${Date.now()}`;
    const expiresAt = new Date(Date.now() + 2 * 60 * 1000); // 2 minutes

    pendingDriverRequests.set(requestId, {
      rideId,
      riderId: user.userId,
      driverId,
      requestTime: new Date(),
      expiresAt,
    });

    // In production, send push notification to driver here
    // For now, we'll simulate the notification
    console.log(`Notification sent to driver ${driver.firstName} ${driver.lastName} for ride ${rideId}`);

    // Auto-expire the request after 2 minutes
    setTimeout(() => {
      if (pendingDriverRequests.has(requestId)) {
        pendingDriverRequests.delete(requestId);
        console.log(`Driver request ${requestId} expired`);
      }
    }, 2 * 60 * 1000);

    return NextResponse.json({
      message: 'Driver request sent successfully',
      requestId,
      expiresAt,
      driver: {
        _id: driver._id,
        firstName: driver.firstName,
        lastName: driver.lastName,
        rating: driver.driverProfile?.rating || 5.0,
        vehicleModel: driver.driverProfile?.vehicleModel,
        vehicleColor: driver.driverProfile?.vehicleColor,
      },
    });

  } catch (error) {
    console.error('Request driver error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get pending requests for a driver
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (user.role !== 'driver') {
      return NextResponse.json(
        { error: 'Only drivers can view ride requests' },
        { status: 403 }
      );
    }

    // Clean up expired requests
    const now = new Date();
    for (const [requestId, request] of pendingDriverRequests.entries()) {
      if (request.expiresAt <= now) {
        pendingDriverRequests.delete(requestId);
      }
    }

    // Find pending requests for this driver
    const driverRequests = Array.from(pendingDriverRequests.entries())
      .filter(([_, request]) => request.driverId === user.userId)
      .map(([requestId, request]) => ({ requestId, ...request }));

    // Get ride details for each request
    const requestsWithDetails = await Promise.all(
      driverRequests.map(async (request) => {
        const ride = await Ride.findById(request.rideId)
          .populate('riderId', 'firstName lastName phone profileImage');
        
        return {
          requestId: request.requestId,
          ride: ride ? {
            _id: ride._id,
            pickupLocation: ride.pickupLocation,
            dropoffLocation: ride.dropoffLocation,
            estimatedDistance: ride.estimatedDistance,
            estimatedDuration: ride.estimatedDuration,
            finalAmount: ride.finalAmount,
            rideType: ride.rideType,
            paymentMethod: ride.paymentMethod,
            specialInstructions: ride.specialInstructions,
            rider: ride.riderId,
          } : null,
          expiresAt: request.expiresAt,
          timeRemaining: Math.max(0, Math.floor((request.expiresAt.getTime() - now.getTime()) / 1000)),
        };
      })
    );

    // Filter out requests where ride was not found
    const validRequests = requestsWithDetails.filter(req => req.ride !== null);

    return NextResponse.json({
      requests: validRequests,
      totalRequests: validRequests.length,
    });

  } catch (error) {
    console.error('Get driver requests error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
