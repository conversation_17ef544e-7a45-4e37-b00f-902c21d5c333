import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { Wallet } from '@/lib/models/Wallet';
import { Transaction } from '@/lib/models/Transaction';
import connectDB from '@/lib/mongodb';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    await connectDB();

    // Find or create wallet for user
    let wallet = await Wallet.findOne({ userId: decoded.userId });
    
    if (!wallet) {
      // Create new wallet for user
      wallet = new Wallet({
        userId: decoded.userId,
        balance: 0,
        currency: 'INR',
        isActive: true,
        kycStatus: 'pending',
      });
      await wallet.save();
    }

    // Get recent transactions
    const recentTransactions = await Transaction.find({ 
      userId: decoded.userId 
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .select('type category amount currency status description createdAt');

    return NextResponse.json({
      success: true,
      data: {
        wallet: {
          id: wallet._id,
          balance: wallet.balance,
          formattedBalance: wallet.formattedBalance,
          currency: wallet.currency,
          isActive: wallet.isActive,
          kycStatus: wallet.kycStatus,
          dailyLimit: wallet.dailyLimit,
          monthlyLimit: wallet.monthlyLimit,
          totalSpent: wallet.totalSpent,
          totalEarned: wallet.totalEarned,
          lastTransactionAt: wallet.lastTransactionAt,
        },
        recentTransactions,
      },
    });

  } catch (error) {
    console.error('Get wallet balance error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get wallet balance',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, amount, description } = body;

    if (!action || !['credit', 'debit'].includes(action)) {
      return NextResponse.json(
        { error: 'Valid action (credit/debit) is required' },
        { status: 400 }
      );
    }

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Valid amount is required' },
        { status: 400 }
      );
    }

    await connectDB();

    // Find wallet
    const wallet = await Wallet.findOne({ userId: decoded.userId });
    if (!wallet) {
      return NextResponse.json(
        { error: 'Wallet not found' },
        { status: 404 }
      );
    }

    // Check if wallet is active and KYC verified for transactions
    if (!wallet.isActive) {
      return NextResponse.json(
        { error: 'Wallet is not active' },
        { status: 400 }
      );
    }

    if (wallet.kycStatus !== 'verified' && amount > 1000) {
      return NextResponse.json(
        { error: 'KYC verification required for transactions above ₹1000' },
        { status: 400 }
      );
    }

    const balanceBefore = wallet.balance;

    // Validate transaction
    if (action === 'debit') {
      if (!wallet.canDebit(amount)) {
        return NextResponse.json(
          { error: 'Insufficient balance or wallet not eligible for debit' },
          { status: 400 }
        );
      }
      
      wallet.balance -= amount;
      wallet.totalSpent += amount;
    } else {
      if (!wallet.canCredit(amount)) {
        return NextResponse.json(
          { error: 'Wallet not eligible for credit' },
          { status: 400 }
        );
      }
      
      wallet.balance += amount;
      wallet.totalEarned += amount;
    }

    wallet.lastTransactionAt = new Date();
    await wallet.save();

    // Create transaction record
    const transaction = new Transaction({
      userId: decoded.userId,
      walletId: wallet._id,
      type: action,
      category: action === 'credit' ? 'wallet_topup' : 'wallet_withdrawal',
      amount,
      currency: wallet.currency,
      paymentMethod: 'wallet',
      balanceBefore,
      balanceAfter: wallet.balance,
      status: 'completed',
      description: description || `Wallet ${action}`,
      processedAt: new Date(),
      completedAt: new Date(),
    });

    await transaction.save();

    return NextResponse.json({
      success: true,
      message: `Wallet ${action} successful`,
      data: {
        transaction: {
          id: transaction._id,
          type: transaction.type,
          amount: transaction.amount,
          balanceBefore,
          balanceAfter: wallet.balance,
          description: transaction.description,
        },
        wallet: {
          balance: wallet.balance,
          formattedBalance: wallet.formattedBalance,
        },
      },
    });

  } catch (error) {
    console.error('Wallet transaction error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process wallet transaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
