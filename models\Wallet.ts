import mongoose, { Schema, Document } from 'mongoose';

export interface IWallet extends Document {
  userId: mongoose.Types.ObjectId;
  balance: number;
  currency: string;
  isActive: boolean;
  dailyLimit: number;
  monthlyLimit: number;
  totalSpent: number;
  totalEarned: number;
  lastTransactionAt: Date;
  kycStatus: 'pending' | 'verified' | 'rejected';
  kycDocuments: {
    aadhaar?: string;
    pan?: string;
    bankAccount?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

const WalletSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true
  },
  balance: {
    type: Number,
    required: true,
    default: 0,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'INR',
    enum: ['INR', 'USD']
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  dailyLimit: {
    type: Number,
    default: 10000, // ₹10,000 daily limit
    min: 0
  },
  monthlyLimit: {
    type: Number,
    default: 100000, // ₹1,00,000 monthly limit
    min: 0
  },
  totalSpent: {
    type: Number,
    default: 0,
    min: 0
  },
  totalEarned: {
    type: Number,
    default: 0,
    min: 0
  },
  lastTransactionAt: {
    type: Date,
    default: null
  },
  kycStatus: {
    type: String,
    enum: ['pending', 'verified', 'rejected'],
    default: 'pending',
    index: true
  },
  kycDocuments: {
    aadhaar: {
      type: String,
      default: null
    },
    pan: {
      type: String,
      default: null
    },
    bankAccount: {
      type: String,
      default: null
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
WalletSchema.index({ userId: 1, isActive: 1 });
WalletSchema.index({ kycStatus: 1 });
WalletSchema.index({ createdAt: -1 });

// Virtual for formatted balance
WalletSchema.virtual('formattedBalance').get(function() {
  return `₹${this.balance.toFixed(2)}`;
});

// Methods
WalletSchema.methods.canDebit = function(amount: number): boolean {
  return this.isActive && this.balance >= amount && this.kycStatus === 'verified';
};

WalletSchema.methods.canCredit = function(amount: number): boolean {
  return this.isActive && this.kycStatus === 'verified';
};

WalletSchema.methods.checkDailyLimit = function(amount: number): boolean {
  // This would need to check daily spending from transactions
  return amount <= this.dailyLimit;
};

WalletSchema.methods.checkMonthlyLimit = function(amount: number): boolean {
  // This would need to check monthly spending from transactions
  return amount <= this.monthlyLimit;
};

export const Wallet = mongoose.models.Wallet || mongoose.model<IWallet>('Wallet', WalletSchema);
