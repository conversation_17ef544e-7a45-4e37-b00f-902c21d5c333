import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Ride } from '@/lib/models/Ride';
import { User } from '@/lib/models/User';
import { Transaction } from '@/lib/models/Transaction';
import { authenticateRequest } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { rideId, status, actualDistance, actualDuration, rating, feedback } = body;

    if (!rideId || !status) {
      return NextResponse.json(
        { error: 'Ride ID and status are required' },
        { status: 400 }
      );
    }

    const validStatuses = ['in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: in_progress, completed, cancelled' },
        { status: 400 }
      );
    }

    // Find the ride
    const ride = await Ride.findById(rideId)
      .populate('riderId', 'firstName lastName phone rewardPoints totalSpent')
      .populate('driverId', 'firstName lastName phone driverProfile totalEarnings');

    if (!ride) {
      return NextResponse.json(
        { error: 'Ride not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const isRider = user.userId === ride.riderId._id.toString();
    const isDriver = user.userId === ride.driverId?._id.toString();

    if (!isRider && !isDriver) {
      return NextResponse.json(
        { error: 'You are not authorized to update this ride' },
        { status: 403 }
      );
    }

    // Validate status transitions
    const currentStatus = ride.status;
    const validTransitions: { [key: string]: string[] } = {
      'accepted': ['in_progress', 'cancelled'],
      'in_progress': ['completed', 'cancelled'],
    };

    if (!validTransitions[currentStatus]?.includes(status)) {
      return NextResponse.json(
        { error: `Cannot change status from ${currentStatus} to ${status}` },
        { status: 400 }
      );
    }

    // Update ride status
    ride.status = status;

    if (status === 'in_progress') {
      ride.startedAt = new Date();
      // Only drivers can start a ride
      if (!isDriver) {
        return NextResponse.json(
          { error: 'Only drivers can start a ride' },
          { status: 403 }
        );
      }
    }

    if (status === 'completed') {
      ride.completedAt = new Date();
      
      // Update actual distance and duration if provided
      if (actualDistance) ride.actualDistance = actualDistance;
      if (actualDuration) ride.actualDuration = actualDuration;

      // Handle payment
      if (ride.paymentStatus === 'pending') {
        ride.paymentStatus = 'completed';

        // Create transaction record
        const transaction = new Transaction({
          userId: ride.riderId._id,
          rideId: ride._id,
          type: 'payment',
          category: 'ride_payment',
          amount: ride.finalAmount,
          currency: 'INR',
          paymentMethod: ride.paymentMethod,
          status: 'completed',
          description: `Payment for ride from ${ride.pickupLocation.address} to ${ride.dropoffLocation.address}`,
          completedAt: new Date(),
        });
        await transaction.save();

        // Update rider's total spent
        const rider = await User.findById(ride.riderId._id);
        if (rider) {
          rider.totalSpent = (rider.totalSpent || 0) + ride.finalAmount;
          
          // Award reward points (10 points per ride)
          const rewardPoints = 10;
          rider.rewardPoints = (rider.rewardPoints || 0) + rewardPoints;
          
          // Update rider's total rides
          if (rider.riderProfile) {
            rider.riderProfile.totalRides = (rider.riderProfile.totalRides || 0) + 1;
          }
          
          await rider.save();

          // Create reward transaction
          const rewardTransaction = new Transaction({
            userId: ride.riderId._id,
            rideId: ride._id,
            type: 'reward',
            category: 'reward_points',
            amount: 0,
            currency: 'INR',
            paymentMethod: 'system',
            status: 'completed',
            description: `Reward points for completing ride`,
            rewardPointsEarned: rewardPoints,
            completedAt: new Date(),
          });
          await rewardTransaction.save();
        }

        // Update driver's earnings
        const driver = await User.findById(ride.driverId._id);
        if (driver) {
          const driverEarnings = ride.finalAmount * 0.8; // Driver gets 80%, platform takes 20%
          driver.totalEarnings = (driver.totalEarnings || 0) + driverEarnings;
          await driver.save();

          // Create driver earnings transaction
          const earningsTransaction = new Transaction({
            userId: ride.driverId._id,
            rideId: ride._id,
            type: 'payment',
            category: 'driver_earnings',
            amount: driverEarnings,
            currency: 'INR',
            paymentMethod: 'system',
            status: 'completed',
            description: `Earnings from ride`,
            completedAt: new Date(),
          });
          await earningsTransaction.save();
        }
      }

      // Handle ratings
      if (rating && feedback !== undefined) {
        if (isRider) {
          ride.driverRating = rating;
          ride.riderFeedback = feedback;
          
          // Update driver's average rating
          const driver = await User.findById(ride.driverId._id);
          if (driver && driver.driverProfile) {
            const totalRides = driver.driverProfile.totalRides || 1;
            const currentRating = driver.driverProfile.rating || 5.0;
            const newRating = ((currentRating * (totalRides - 1)) + rating) / totalRides;
            driver.driverProfile.rating = Math.round(newRating * 10) / 10;
            await driver.save();
          }
        } else if (isDriver) {
          ride.riderRating = rating;
          ride.driverFeedback = feedback;
          
          // Update rider's average rating
          const rider = await User.findById(ride.riderId._id);
          if (rider && rider.riderProfile) {
            const totalRides = rider.riderProfile.totalRides || 1;
            const currentRating = rider.riderProfile.rating || 5.0;
            const newRating = ((currentRating * (totalRides - 1)) + rating) / totalRides;
            rider.riderProfile.rating = Math.round(newRating * 10) / 10;
            await rider.save();
          }
        }
      }
    }

    if (status === 'cancelled') {
      ride.cancelledAt = new Date();
      ride.cancelledBy = user.role;
      
      // Handle cancellation fee if applicable
      const timeSinceAccepted = ride.acceptedAt ? 
        (new Date().getTime() - ride.acceptedAt.getTime()) / (1000 * 60) : 0;
      
      if (timeSinceAccepted > 5 && isRider) { // 5 minutes grace period
        const cancellationFee = 20; // ₹20 cancellation fee
        ride.cancellationFee = cancellationFee;
        
        // Create cancellation fee transaction
        const cancellationTransaction = new Transaction({
          userId: ride.riderId._id,
          rideId: ride._id,
          type: 'penalty',
          category: 'cancellation_fee',
          amount: cancellationFee,
          currency: 'INR',
          paymentMethod: ride.paymentMethod,
          status: 'completed',
          description: `Cancellation fee for ride`,
          completedAt: new Date(),
        });
        await cancellationTransaction.save();
      }
    }

    await ride.save();

    // In production, send real-time notifications here
    console.log(`Ride ${rideId} status updated to ${status} by ${user.role}`);

    return NextResponse.json({
      message: 'Ride status updated successfully',
      ride: {
        _id: ride._id,
        status: ride.status,
        startedAt: ride.startedAt,
        completedAt: ride.completedAt,
        cancelledAt: ride.cancelledAt,
        actualDistance: ride.actualDistance,
        actualDuration: ride.actualDuration,
        paymentStatus: ride.paymentStatus,
        cancellationFee: ride.cancellationFee,
        driverRating: ride.driverRating,
        riderRating: ride.riderRating,
        riderFeedback: ride.riderFeedback,
        driverFeedback: ride.driverFeedback,
      },
    });

  } catch (error) {
    console.error('Update ride status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
