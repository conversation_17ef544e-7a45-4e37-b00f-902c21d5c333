import { Schema, model, models, Document } from 'mongoose';

export interface IUser extends Document {
  _id: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: 'rider' | 'driver';
  isVerified: boolean;
  profileImage?: string;
  dateOfBirth?: Date;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  // Driver-specific fields
  driverProfile?: {
    licenseNumber: string;
    licenseExpiry: Date;
    vehicleRegistration: string;
    vehicleModel: string;
    vehicleYear: number;
    vehicleColor: string;
    isApproved: boolean;
    rating: number;
    totalRides: number;
    documentsUploaded: {
      license: boolean;
      registration: boolean;
      insurance: boolean;
    };
  };
  // Rider-specific fields
  riderProfile?: {
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
    preferredPaymentMethod: string;
    rating: number;
    totalRides: number;
  };
  // Rewards and points
  rewardPoints: number;
  totalEarnings: number; // For drivers
  totalSpent: number; // For riders
  // Account status
  isActive: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
  },
  phone: {
    type: String,
    required: true,
    unique: true,
  },
  role: {
    type: String,
    enum: ['rider', 'driver'],
    required: true,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  profileImage: {
    type: String,
    default: null,
  },
  dateOfBirth: {
    type: Date,
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
  },
  driverProfile: {
    licenseNumber: String,
    licenseExpiry: Date,
    vehicleRegistration: String,
    vehicleModel: String,
    vehicleYear: Number,
    vehicleColor: String,
    isApproved: {
      type: Boolean,
      default: false,
    },
    rating: {
      type: Number,
      default: 5.0,
      min: 1,
      max: 5,
    },
    totalRides: {
      type: Number,
      default: 0,
    },
    documentsUploaded: {
      license: {
        type: Boolean,
        default: false,
      },
      registration: {
        type: Boolean,
        default: false,
      },
      insurance: {
        type: Boolean,
        default: false,
      },
    },
  },
  riderProfile: {
    emergencyContact: {
      name: String,
      phone: String,
      relationship: String,
    },
    preferredPaymentMethod: String,
    rating: {
      type: Number,
      default: 5.0,
      min: 1,
      max: 5,
    },
    totalRides: {
      type: Number,
      default: 0,
    },
  },
  rewardPoints: {
    type: Number,
    default: 0,
  },
  totalEarnings: {
    type: Number,
    default: 0,
  },
  totalSpent: {
    type: Number,
    default: 0,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  lastLogin: {
    type: Date,
  },
}, {
  timestamps: true,
});

// Indexes for performance
UserSchema.index({ email: 1 });
UserSchema.index({ phone: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ 'driverProfile.isApproved': 1 });

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Ensure virtual fields are serialized
UserSchema.set('toJSON', {
  virtuals: true,
});

export const User = models.User || model<IUser>('User', UserSchema);
