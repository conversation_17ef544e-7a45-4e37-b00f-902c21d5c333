import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { foodDeliveryService } from '@/lib/delivery/foodDeliveryService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      restaurantId,
      items,
      deliveryAddress,
      customerContact,
      paymentMethod,
      specialInstructions
    } = body;

    // Validate required fields
    if (!restaurantId || !items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: 'Restaurant ID and items are required' },
        { status: 400 }
      );
    }

    if (!deliveryAddress || !deliveryAddress.latitude || !deliveryAddress.longitude || !deliveryAddress.address) {
      return NextResponse.json(
        { error: 'Valid delivery address with latitude, longitude, and address is required' },
        { status: 400 }
      );
    }

    if (!customerContact || !customerContact.name || !customerContact.phone) {
      return NextResponse.json(
        { error: 'Customer contact information (name and phone) is required' },
        { status: 400 }
      );
    }

    // Validate items
    for (const item of items) {
      if (!item.menuItemId || !item.quantity || item.quantity <= 0) {
        return NextResponse.json(
          { error: 'Each item must have a valid menuItemId and quantity > 0' },
          { status: 400 }
        );
      }

      if (item.customizations && !Array.isArray(item.customizations)) {
        return NextResponse.json(
          { error: 'Item customizations must be an array' },
          { status: 400 }
        );
      }
    }

    // Create food order
    const foodOrder = await foodDeliveryService.createFoodOrder({
      customerId: decoded.userId,
      restaurantId,
      items: items.map(item => ({
        menuItemId: item.menuItemId,
        quantity: item.quantity,
        customizations: item.customizations || [],
        specialInstructions: item.specialInstructions,
        price: 0, // Will be calculated by the service
      })),
      deliveryAddress,
      customerContact,
      paymentMethod: paymentMethod || 'card',
      specialInstructions,
    });

    const response = {
      success: true,
      data: {
        order: {
          id: foodOrder.id,
          restaurantId: foodOrder.restaurantId,
          status: foodOrder.status,
          estimatedDeliveryTime: foodOrder.estimatedDeliveryTime,
          preparationTime: foodOrder.preparationTime,
          pricing: foodOrder.pricing,
          items: foodOrder.items,
          deliveryAddress: foodOrder.deliveryAddress,
          customerContact: foodOrder.customerContact,
          paymentMethod: foodOrder.paymentMethod,
          paymentStatus: foodOrder.paymentStatus,
          specialInstructions: foodOrder.specialInstructions,
          createdAt: foodOrder.createdAt,
        },
        nextSteps: [
          'Your order has been placed successfully',
          'Restaurant will confirm your order shortly',
          'You will receive real-time updates on order status',
          'Estimated delivery time: ' + foodOrder.estimatedDeliveryTime.toLocaleTimeString(),
        ],
        tracking: {
          orderStatus: foodOrder.status,
          estimatedDeliveryTime: foodOrder.estimatedDeliveryTime,
          preparationTime: foodOrder.preparationTime,
          canCancel: foodOrder.status === 'placed',
        },
      },
      metadata: {
        orderId: foodOrder.id,
        customerId: decoded.userId,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Create food order API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create food order',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Mock user's food orders (in real app, would filter by user)
    const mockOrders = [
      {
        id: 'food_001',
        restaurantId: 'rest_001',
        restaurantName: 'Spice Garden',
        status: 'delivered',
        estimatedDeliveryTime: new Date(Date.now() - 30 * 60 * 1000),
        actualDeliveryTime: new Date(Date.now() - 25 * 60 * 1000),
        pricing: {
          subtotal: 280,
          deliveryFee: 30,
          packagingFee: 10,
          taxes: 14,
          total: 334,
        },
        items: [
          {
            menuItemId: 'menu_001',
            name: 'Butter Chicken',
            quantity: 1,
            price: 280,
          },
        ],
        deliveryAddress: {
          address: '123 Home Street, Bangalore',
          latitude: 12.9716,
          longitude: 77.5946,
        },
        paymentMethod: 'card',
        paymentStatus: 'paid',
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        rating: 5,
        canReorder: true,
      },
      {
        id: 'food_002',
        restaurantId: 'rest_002',
        restaurantName: 'Pizza Corner',
        status: 'preparing',
        estimatedDeliveryTime: new Date(Date.now() + 25 * 60 * 1000),
        pricing: {
          subtotal: 400,
          deliveryFee: 25,
          packagingFee: 15,
          taxes: 20,
          total: 460,
        },
        items: [
          {
            menuItemId: 'menu_002',
            name: 'Margherita Pizza (Medium)',
            quantity: 1,
            price: 400,
          },
        ],
        deliveryAddress: {
          address: '456 Office Complex, Bangalore',
          latitude: 12.9784,
          longitude: 77.6408,
        },
        paymentMethod: 'upi',
        paymentStatus: 'paid',
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        canCancel: false,
        canReorder: true,
      },
    ];

    // Apply filters
    let filteredOrders = mockOrders;
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }

    // Apply pagination
    const paginatedOrders = filteredOrders.slice(offset, offset + limit);

    // Calculate summary statistics
    const totalOrders = filteredOrders.length;
    const totalSpent = filteredOrders
      .filter(order => order.paymentStatus === 'paid')
      .reduce((sum, order) => sum + order.pricing.total, 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    const favoriteRestaurants = [...new Set(filteredOrders.map(order => order.restaurantName))];

    const response = {
      success: true,
      data: {
        orders: paginatedOrders,
        pagination: {
          total: filteredOrders.length,
          limit,
          offset,
          hasMore: offset + limit < filteredOrders.length,
        },
        summary: {
          totalOrders,
          totalSpent,
          averageOrderValue: Math.round(averageOrderValue),
          favoriteRestaurants: favoriteRestaurants.slice(0, 3),
          recentActivity: {
            ordersThisWeek: filteredOrders.filter(order => 
              order.createdAt > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            ).length,
            ordersThisMonth: filteredOrders.filter(order => 
              order.createdAt > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            ).length,
          },
        },
        filters: {
          availableStatuses: ['placed', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled'],
          sortOptions: ['newest', 'oldest', 'amount_high', 'amount_low'],
        },
        quickActions: [
          { action: 'reorder', label: 'Reorder Favorite', available: totalOrders > 0 },
          { action: 'track', label: 'Track Active Orders', available: filteredOrders.some(o => ['confirmed', 'preparing', 'ready', 'picked_up'].includes(o.status)) },
          { action: 'support', label: 'Order Support', available: true },
        ],
      },
      metadata: {
        customerId: decoded.userId,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get food orders API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch food orders',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
