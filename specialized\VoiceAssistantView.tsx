'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX,
  Bot,
  User,
  MapPin,
  Car,
  Clock,
  DollarSign,
  Zap,
  Settings,
  Languages,
  Sparkles,
  MessageCircle,
  Phone,
  Navigation
} from 'lucide-react';

interface VoiceAssistantViewProps {
  isActive: boolean;
  onToggle: (active: boolean) => void;
}

export default function VoiceAssistantView({ isActive, onToggle }: VoiceAssistantViewProps) {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentCommand, setCurrentCommand] = useState('');
  const [conversation, setConversation] = useState([
    {
      type: 'assistant',
      message: 'Hi! I\'m <PERSON>, your mobility assistant. How can I help you today?',
      timestamp: new Date(),
      actions: []
    }
  ]);
  const [language, setLanguage] = useState('en');
  const [voiceEnabled, setVoiceEnabled] = useState(true);

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'hi', name: 'हिंदी', flag: '🇮🇳' },
    { code: 'kn', name: 'ಕನ್ನಡ', flag: '🇮🇳' },
    { code: 'ta', name: 'தமிழ்', flag: '🇮🇳' },
    { code: 'te', name: 'తెలుగు', flag: '🇮🇳' }
  ];

  const quickCommands = [
    { text: 'Book a ride to airport', icon: '✈️', action: 'book_ride' },
    { text: 'Find nearby bikes', icon: '🚲', action: 'find_vehicles' },
    { text: 'Order food delivery', icon: '🍕', action: 'order_food' },
    { text: 'Check my carbon footprint', icon: '🌱', action: 'carbon_check' },
    { text: 'Call emergency contact', icon: '🚨', action: 'emergency' },
    { text: 'Share my location', icon: '📍', action: 'share_location' }
  ];

  const handleVoiceCommand = (command: string) => {
    setCurrentCommand(command);
    setIsListening(false);
    
    // Simulate processing
    setTimeout(() => {
      processCommand(command);
    }, 1000);
  };

  const processCommand = (command: string) => {
    const lowerCommand = command.toLowerCase();
    let response = '';
    let actions = [];

    if (lowerCommand.includes('book') && lowerCommand.includes('ride')) {
      response = 'I\'ll help you book a ride. Where would you like to go?';
      actions = [
        { type: 'book_ride', label: 'Book Ride', icon: <Car className="h-4 w-4" /> }
      ];
    } else if (lowerCommand.includes('find') && (lowerCommand.includes('bike') || lowerCommand.includes('scooter'))) {
      response = 'I found 5 bikes and 3 scooters within 200m of your location. The nearest bike is 50m away.';
      actions = [
        { type: 'show_map', label: 'Show Map', icon: <MapPin className="h-4 w-4" /> },
        { type: 'unlock_vehicle', label: 'Unlock Nearest', icon: <Zap className="h-4 w-4" /> }
      ];
    } else if (lowerCommand.includes('food') || lowerCommand.includes('order')) {
      response = 'I can help you order food. What would you like to eat today?';
      actions = [
        { type: 'browse_restaurants', label: 'Browse Restaurants', icon: <MessageCircle className="h-4 w-4" /> }
      ];
    } else if (lowerCommand.includes('carbon') || lowerCommand.includes('environment')) {
      response = 'This month you\'ve saved 12.5kg of CO₂! You\'re doing great for the environment.';
      actions = [
        { type: 'carbon_report', label: 'Full Report', icon: <Sparkles className="h-4 w-4" /> }
      ];
    } else if (lowerCommand.includes('emergency') || lowerCommand.includes('help')) {
      response = 'I can help with emergency situations. Would you like me to call your emergency contact or emergency services?';
      actions = [
        { type: 'call_emergency', label: 'Call Emergency', icon: <Phone className="h-4 w-4" /> },
        { type: 'share_location', label: 'Share Location', icon: <MapPin className="h-4 w-4" /> }
      ];
    } else if (lowerCommand.includes('location') || lowerCommand.includes('share')) {
      response = 'I\'ll share your current location with your emergency contacts. Location shared successfully!';
      actions = [];
    } else {
      response = 'I understand you said "' + command + '". How can I help you with your mobility needs?';
      actions = [];
    }

    const newMessage = {
      type: 'assistant' as const,
      message: response,
      timestamp: new Date(),
      actions
    };

    setConversation(prev => [...prev, 
      {
        type: 'user' as const,
        message: command,
        timestamp: new Date(),
        actions: []
      },
      newMessage
    ]);

    // Speak the response if voice is enabled
    if (voiceEnabled) {
      speakText(response);
    }
  };

  const speakText = (text: string) => {
    if ('speechSynthesis' in window) {
      setIsSpeaking(true);
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = language === 'hi' ? 'hi-IN' : 'en-US';
      utterance.onend = () => setIsSpeaking(false);
      speechSynthesis.speak(utterance);
    }
  };

  const startListening = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.lang = language === 'hi' ? 'hi-IN' : 'en-US';
      recognition.continuous = false;
      recognition.interimResults = false;

      recognition.onstart = () => setIsListening(true);
      recognition.onend = () => setIsListening(false);
      
      recognition.onresult = (event) => {
        const command = event.results[0][0].transcript;
        handleVoiceCommand(command);
      };

      recognition.onerror = () => {
        setIsListening(false);
        setConversation(prev => [...prev, {
          type: 'assistant',
          message: 'Sorry, I couldn\'t hear you clearly. Please try again.',
          timestamp: new Date(),
          actions: []
        }]);
      };

      recognition.start();
    }
  };

  const handleQuickCommand = (command: any) => {
    handleVoiceCommand(command.text);
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <Bot className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-lg font-semibold">Maya Assistant</h1>
              <p className="text-purple-100 text-sm">
                {isListening ? 'Listening...' : isSpeaking ? 'Speaking...' : 'Ready to help'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={() => setVoiceEnabled(!voiceEnabled)}
            >
              {voiceEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Language Selector */}
      <div className="p-4 border-b">
        <div className="flex items-center space-x-2 overflow-x-auto">
          <Languages className="h-4 w-4 text-gray-600 flex-shrink-0" />
          {languages.map((lang) => (
            <Button
              key={lang.code}
              size="sm"
              variant={language === lang.code ? 'default' : 'outline'}
              className="flex-shrink-0"
              onClick={() => setLanguage(lang.code)}
            >
              <span className="mr-1">{lang.flag}</span>
              {lang.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Conversation */}
      <div className="flex-1 p-4 space-y-4 max-h-96 overflow-y-auto">
        {conversation.map((message, index) => (
          <div key={index} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-xs p-3 rounded-lg ${
              message.type === 'user' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-100 text-gray-900'
            }`}>
              <div className="flex items-start space-x-2">
                {message.type === 'assistant' && (
                  <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />
                )}
                <div className="flex-1">
                  <p className="text-sm">{message.message}</p>
                  <p className="text-xs opacity-75 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
                {message.type === 'user' && (
                  <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                )}
              </div>
              
              {message.actions.length > 0 && (
                <div className="mt-2 space-y-1">
                  {message.actions.map((action, actionIndex) => (
                    <Button
                      key={actionIndex}
                      size="sm"
                      variant="outline"
                      className="w-full justify-start text-xs"
                    >
                      {action.icon}
                      <span className="ml-2">{action.label}</span>
                    </Button>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Commands */}
      <div className="p-4 border-t">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Commands</h3>
        <div className="grid grid-cols-2 gap-2">
          {quickCommands.map((command, index) => (
            <Button
              key={index}
              size="sm"
              variant="outline"
              className="h-auto p-2 flex-col text-xs"
              onClick={() => handleQuickCommand(command)}
            >
              <span className="text-lg mb-1">{command.icon}</span>
              <span className="text-center leading-tight">{command.text}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Voice Input */}
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center justify-center space-x-4">
          <Button
            size="lg"
            className={`w-16 h-16 rounded-full ${
              isListening 
                ? 'bg-red-600 hover:bg-red-700 animate-pulse' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
            onClick={startListening}
            disabled={isSpeaking}
          >
            {isListening ? (
              <div className="text-center">
                <MicOff className="h-6 w-6" />
              </div>
            ) : (
              <Mic className="h-6 w-6" />
            )}
          </Button>
        </div>
        
        <p className="text-center text-sm text-gray-600 mt-2">
          {isListening 
            ? 'Listening... Speak now' 
            : isSpeaking 
            ? 'Speaking...' 
            : 'Tap to speak with Maya'
          }
        </p>

        {currentCommand && (
          <div className="mt-3 p-2 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              Processing: "{currentCommand}"
            </p>
          </div>
        )}
      </div>

      {/* Status Indicators */}
      <div className="flex justify-center space-x-4 p-2 bg-gray-100">
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${isActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
          <span className="text-xs text-gray-600">Assistant</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${voiceEnabled ? 'bg-blue-500' : 'bg-gray-400'}`}></div>
          <span className="text-xs text-gray-600">Voice</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-purple-500"></div>
          <span className="text-xs text-gray-600">{languages.find(l => l.code === language)?.name}</span>
        </div>
      </div>
    </div>
  );
}
