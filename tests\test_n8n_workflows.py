#!/usr/bin/env python3
"""
Comprehensive n8n Workflow Testing Suite
Tests all automation workflows for functionality and performance
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
import uuid

class N8nWorkflowTester:
    def __init__(self, n8n_url: str = "http://localhost:5678"):
        self.n8n_url = n8n_url
        self.session = None
        self.test_results = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_ai_enhanced_ride_matching(self):
        """Test AI-enhanced ride matching workflow"""
        print("🤖 Testing AI-Enhanced Ride Matching Workflow...")
        
        test_ride_request = {
            "rideRequest": {
                "userId": "test_user_001",
                "pickup": "Andheri East",
                "destination": "BKC",
                "distance": 12,
                "pickupLat": 19.1136,
                "pickupLon": 72.8697,
                "weather": "clear",
                "timeOfDay": "peak",
                "requestId": str(uuid.uuid4())
            },
            "availableDrivers": [
                {
                    "id": "driver_001",
                    "latitude": 19.1100,
                    "longitude": 72.8700,
                    "rating": 4.8,
                    "isAvailable": True,
                    "earningsToday": 1200
                },
                {
                    "id": "driver_002",
                    "latitude": 19.1150,
                    "longitude": 72.8750,
                    "rating": 4.6,
                    "isAvailable": True,
                    "earningsToday": 800
                }
            ]
        }
        
        workflow_tests = []
        response_times = []
        
        start_time = time.time()
        
        try:
            # Test AI-enhanced ride matching webhook
            async with self.session.post(
                f"{self.n8n_url}/webhook/ai-ride-request",
                json=test_ride_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Validate AI-enhanced response structure
                    required_fields = [
                        "success", "rideId", "driverId", 
                        "estimatedPickupTime", "pricing", "aiInsights"
                    ]
                    
                    has_required_fields = all(field in result for field in required_fields)
                    has_ai_insights = "aiInsights" in result and isinstance(result["aiInsights"], dict)
                    has_valid_pricing = "pricing" in result and "total_fare" in result["pricing"]
                    
                    test_passed = has_required_fields and has_ai_insights and has_valid_pricing
                    workflow_tests.append(1.0 if test_passed else 0.0)
                    
                    print(f"  ✅ AI Ride Matching: Success={result.get('success')}, Driver={result.get('driverId')}, Time={response_time:.3f}s")
                    
                    if has_ai_insights:
                        ai_insights = result["aiInsights"]
                        print(f"     AI Insights: Demand={ai_insights.get('demandPrediction', 'N/A')}, Confidence={ai_insights.get('assignmentConfidence', 'N/A')}")
                    
                else:
                    print(f"  ❌ AI Ride Matching: HTTP {response.status}")
                    workflow_tests.append(0.0)
                    
        except Exception as e:
            print(f"  ❌ AI Ride Matching: Error - {e}")
            workflow_tests.append(0.0)
        
        # Calculate metrics
        success_rate = np.mean(workflow_tests) * 100 if workflow_tests else 0
        avg_response_time = np.mean(response_times) if response_times else 0
        
        self.test_results["ai_ride_matching"] = {
            "success_rate_percentage": success_rate,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_success_rate": 95.0,
            "target_response_time_ms": 2000,
            "passed": success_rate >= 95.0 and avg_response_time <= 2.0
        }
        
        print(f"📊 AI Ride Matching Results:")
        print(f"   Success Rate: {success_rate:.1f}% (Target: 95%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <2000ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['ai_ride_matching']['passed'] else '❌ FAILED'}")
        
        return self.test_results["ai_ride_matching"]
    
    async def test_ride_completion_workflow(self):
        """Test ride completion workflow"""
        print("\n🏁 Testing Ride Completion Workflow...")
        
        test_completion_data = {
            "rideData": {
                "rideId": str(uuid.uuid4()),
                "userId": "test_user_001",
                "driverId": "driver_001",
                "pickup": "Andheri East",
                "destination": "BKC",
                "distance": 12.5,
                "duration": 35,
                "fare": 180,
                "rating": 4.8,
                "completedAt": datetime.now().isoformat(),
                "paymentMethod": "digital_wallet"
            }
        }
        
        workflow_tests = []
        response_times = []
        
        start_time = time.time()
        
        try:
            # Test ride completion webhook
            async with self.session.post(
                f"{self.n8n_url}/webhook/ride-completed",
                json=test_completion_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Validate completion response
                    required_fields = ["success", "rewardsUpdated", "paymentProcessed"]
                    has_required_fields = all(field in result for field in required_fields)
                    
                    # Check if rewards and payment were processed
                    rewards_updated = result.get("rewardsUpdated", False)
                    payment_processed = result.get("paymentProcessed", False)
                    
                    test_passed = has_required_fields and rewards_updated and payment_processed
                    workflow_tests.append(1.0 if test_passed else 0.0)
                    
                    print(f"  ✅ Ride Completion: Success={result.get('success')}, Rewards={rewards_updated}, Payment={payment_processed}, Time={response_time:.3f}s")
                    
                else:
                    print(f"  ❌ Ride Completion: HTTP {response.status}")
                    workflow_tests.append(0.0)
                    
        except Exception as e:
            print(f"  ❌ Ride Completion: Error - {e}")
            workflow_tests.append(0.0)
        
        # Calculate metrics
        success_rate = np.mean(workflow_tests) * 100 if workflow_tests else 0
        avg_response_time = np.mean(response_times) if response_times else 0
        
        self.test_results["ride_completion"] = {
            "success_rate_percentage": success_rate,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_success_rate": 98.0,
            "target_response_time_ms": 1500,
            "passed": success_rate >= 98.0 and avg_response_time <= 1.5
        }
        
        print(f"📊 Ride Completion Results:")
        print(f"   Success Rate: {success_rate:.1f}% (Target: 98%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <1500ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['ride_completion']['passed'] else '❌ FAILED'}")
        
        return self.test_results["ride_completion"]
    
    async def test_carbon_neutrality_optimization(self):
        """Test carbon neutrality optimization workflow"""
        print("\n🌱 Testing Carbon Neutrality Optimization Workflow...")
        
        test_carbon_data = {
            "carbonOptimization": {
                "fleetOperations": {
                    "totalVehicles": 100,
                    "activeVehicles": 75,
                    "totalRides": 1500,
                    "totalDistance": 18000,
                    "energyConsumption": 2400
                },
                "renewableEnergyData": {
                    "solarGeneration": 1800,
                    "batteryStorage": 600,
                    "gridConsumption": 400
                },
                "carbonTargets": {
                    "dailyCarbonLimit": 500,
                    "renewableEnergyTarget": 80,
                    "carbonNeutralityGoal": True
                }
            }
        }
        
        workflow_tests = []
        response_times = []
        
        start_time = time.time()
        
        try:
            # Test carbon optimization webhook
            async with self.session.post(
                f"{self.n8n_url}/webhook/carbon-optimization",
                json=test_carbon_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Validate carbon optimization response
                    required_fields = [
                        "carbonFootprintCalculated", "renewableEnergyOptimized", 
                        "carbonOffsetsManaged", "sustainabilityTargetsUpdated"
                    ]
                    
                    has_required_fields = all(field in result for field in required_fields)
                    
                    # Check optimization results
                    carbon_calculated = result.get("carbonFootprintCalculated", False)
                    renewable_optimized = result.get("renewableEnergyOptimized", False)
                    offsets_managed = result.get("carbonOffsetsManaged", False)
                    
                    test_passed = has_required_fields and carbon_calculated and renewable_optimized
                    workflow_tests.append(1.0 if test_passed else 0.0)
                    
                    print(f"  ✅ Carbon Optimization: Carbon={carbon_calculated}, Renewable={renewable_optimized}, Offsets={offsets_managed}, Time={response_time:.3f}s")
                    
                    if "carbonMetrics" in result:
                        metrics = result["carbonMetrics"]
                        print(f"     Carbon Metrics: Footprint={metrics.get('totalCarbonFootprint', 'N/A')}kg, Renewable={metrics.get('renewablePercentage', 'N/A')}%")
                    
                else:
                    print(f"  ❌ Carbon Optimization: HTTP {response.status}")
                    workflow_tests.append(0.0)
                    
        except Exception as e:
            print(f"  ❌ Carbon Optimization: Error - {e}")
            workflow_tests.append(0.0)
        
        # Calculate metrics
        success_rate = np.mean(workflow_tests) * 100 if workflow_tests else 0
        avg_response_time = np.mean(response_times) if response_times else 0
        
        self.test_results["carbon_optimization"] = {
            "success_rate_percentage": success_rate,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_success_rate": 90.0,
            "target_response_time_ms": 3000,
            "passed": success_rate >= 90.0 and avg_response_time <= 3.0
        }
        
        print(f"📊 Carbon Optimization Results:")
        print(f"   Success Rate: {success_rate:.1f}% (Target: 90%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <3000ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['carbon_optimization']['passed'] else '❌ FAILED'}")
        
        return self.test_results["carbon_optimization"]
    
    async def test_global_city_onboarding(self):
        """Test global city onboarding workflow"""
        print("\n🌍 Testing Global City Onboarding Workflow...")
        
        test_city_data = {
            "cityOnboarding": {
                "cityInfo": {
                    "cityId": "test_city_001",
                    "cityName": "Test City",
                    "country": "Test Country",
                    "region": "test-region",
                    "population": 2000000,
                    "coordinates": {
                        "latitude": 19.0760,
                        "longitude": 72.8777
                    }
                },
                "requirements": {
                    "regulatoryCompliance": ["local_transport_law", "data_privacy"],
                    "technicalRequirements": ["traffic_api", "payment_gateway"],
                    "smartCityIntegration": ["traffic_management", "public_transport"]
                },
                "configuration": {
                    "supportedLanguages": ["en", "local"],
                    "supportedCurrencies": ["USD", "LOCAL"],
                    "localPaymentMethods": ["credit_card", "local_wallet"]
                }
            }
        }
        
        workflow_tests = []
        response_times = []
        
        start_time = time.time()
        
        try:
            # Test city onboarding webhook
            async with self.session.post(
                f"{self.n8n_url}/webhook/city-onboarding",
                json=test_city_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if response.status == 200:
                    result = await response.json()
                    
                    # Validate onboarding response
                    required_fields = [
                        "infrastructureDeployed", "localizationCompleted",
                        "complianceValidated", "smartCityIntegrated"
                    ]
                    
                    has_required_fields = all(field in result for field in required_fields)
                    
                    # Check onboarding steps
                    infrastructure_deployed = result.get("infrastructureDeployed", False)
                    localization_completed = result.get("localizationCompleted", False)
                    compliance_validated = result.get("complianceValidated", False)
                    
                    test_passed = has_required_fields and infrastructure_deployed and localization_completed
                    workflow_tests.append(1.0 if test_passed else 0.0)
                    
                    print(f"  ✅ City Onboarding: Infrastructure={infrastructure_deployed}, Localization={localization_completed}, Compliance={compliance_validated}, Time={response_time:.3f}s")
                    
                    if "deploymentStatus" in result:
                        status = result["deploymentStatus"]
                        print(f"     Deployment Status: {status.get('status', 'N/A')}, Progress: {status.get('progress', 'N/A')}%")
                    
                else:
                    print(f"  ❌ City Onboarding: HTTP {response.status}")
                    workflow_tests.append(0.0)
                    
        except Exception as e:
            print(f"  ❌ City Onboarding: Error - {e}")
            workflow_tests.append(0.0)
        
        # Calculate metrics
        success_rate = np.mean(workflow_tests) * 100 if workflow_tests else 0
        avg_response_time = np.mean(response_times) if response_times else 0
        
        self.test_results["city_onboarding"] = {
            "success_rate_percentage": success_rate,
            "avg_response_time_ms": avg_response_time * 1000,
            "target_success_rate": 85.0,
            "target_response_time_ms": 5000,
            "passed": success_rate >= 85.0 and avg_response_time <= 5.0
        }
        
        print(f"📊 City Onboarding Results:")
        print(f"   Success Rate: {success_rate:.1f}% (Target: 85%)")
        print(f"   Avg Response Time: {avg_response_time*1000:.1f}ms (Target: <5000ms)")
        print(f"   Status: {'✅ PASSED' if self.test_results['city_onboarding']['passed'] else '❌ FAILED'}")
        
        return self.test_results["city_onboarding"]
    
    async def run_all_tests(self):
        """Run all n8n workflow tests"""
        print("🚀 Starting Comprehensive n8n Workflow Testing...\n")
        
        await self.test_ai_enhanced_ride_matching()
        await self.test_ride_completion_workflow()
        await self.test_carbon_neutrality_optimization()
        await self.test_global_city_onboarding()
        
        # Generate overall summary
        print("\n" + "="*60)
        print("📋 OVERALL N8N WORKFLOW TEST SUMMARY")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["passed"])
        
        for test_name, result in self.test_results.items():
            status = "✅ PASSED" if result["passed"] else "❌ FAILED"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall Success Rate: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        
        return self.test_results

# Test execution function
async def main():
    async with N8nWorkflowTester() as tester:
        results = await tester.run_all_tests()
        return results

if __name__ == "__main__":
    import numpy as np
    asyncio.run(main())
