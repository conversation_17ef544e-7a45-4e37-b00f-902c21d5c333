'use client';

import { ReactNode, forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Eye, 
  EyeOff, 
  Search, 
  MapPin, 
  Calendar,
  Clock,
  Phone,
  Mail,
  User,
  CreditCard,
  Check,
  X,
  AlertCircle,
  Info
} from 'lucide-react';

interface FormFieldProps {
  label: string;
  error?: string;
  hint?: string;
  required?: boolean;
  children: ReactNode;
  className?: string;
}

interface EnhancedInputProps {
  type?: 'text' | 'email' | 'password' | 'tel' | 'search' | 'url';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  success?: boolean;
  loading?: boolean;
  icon?: ReactNode;
  rightIcon?: ReactNode;
  onRightIconClick?: () => void;
  className?: string;
  disabled?: boolean;
  autoComplete?: string;
}

interface LocationInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onLocationSelect?: (location: any) => void;
  suggestions?: any[];
  loading?: boolean;
  error?: string;
}

interface DateTimePickerProps {
  value?: Date;
  onChange?: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
  placeholder?: string;
  error?: string;
  showTime?: boolean;
}

interface PhoneInputProps {
  value?: string;
  onChange?: (value: string) => void;
  countryCode?: string;
  onCountryChange?: (code: string) => void;
  error?: string;
  placeholder?: string;
}

// Form Field Wrapper
export function FormField({ label, error, hint, required, children, className }: FormFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      {children}
      {hint && !error && (
        <p className="text-xs text-gray-500 flex items-center">
          <Info className="h-3 w-3 mr-1" />
          {hint}
        </p>
      )}
      {error && (
        <p className="text-xs text-red-600 flex items-center">
          <AlertCircle className="h-3 w-3 mr-1" />
          {error}
        </p>
      )}
    </div>
  );
}

// Enhanced Input Component
export const EnhancedInput = forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ 
    type = 'text', 
    placeholder, 
    value, 
    onChange, 
    error, 
    success, 
    loading, 
    icon, 
    rightIcon, 
    onRightIconClick,
    className,
    disabled,
    autoComplete,
    ...props 
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [focused, setFocused] = useState(false);

    const inputType = type === 'password' && showPassword ? 'text' : type;

    const handlePasswordToggle = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className="relative">
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}
        
        <Input
          ref={ref}
          type={inputType}
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          disabled={disabled || loading}
          autoComplete={autoComplete}
          className={cn(
            'transition-all duration-200',
            icon && 'pl-10',
            (rightIcon || type === 'password') && 'pr-10',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            success && 'border-green-500 focus:border-green-500 focus:ring-green-500',
            focused && !error && !success && 'border-blue-500 ring-2 ring-blue-500/20',
            loading && 'animate-pulse',
            className
          )}
          {...props}
        />
        
        {type === 'password' && (
          <button
            type="button"
            onClick={handlePasswordToggle}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        )}
        
        {rightIcon && type !== 'password' && (
          <button
            type="button"
            onClick={onRightIconClick}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {rightIcon}
          </button>
        )}
        
        {success && !rightIcon && type !== 'password' && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-500">
            <Check className="h-4 w-4" />
          </div>
        )}
        
        {error && !rightIcon && type !== 'password' && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-red-500">
            <X className="h-4 w-4" />
          </div>
        )}
        
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
          </div>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = 'EnhancedInput';

// Location Input with Autocomplete
export function LocationInput({ 
  placeholder = "Enter location", 
  value, 
  onChange, 
  onLocationSelect, 
  suggestions = [], 
  loading, 
  error 
}: LocationInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);

  return (
    <div className="relative">
      <EnhancedInput
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={() => setShowSuggestions(true)}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
        icon={<MapPin className="h-4 w-4" />}
        loading={loading}
        error={error}
      />
      
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => {
                onLocationSelect?.(suggestion);
                setShowSuggestions(false);
              }}
              className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-center">
                <MapPin className="h-4 w-4 text-gray-400 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">{suggestion.name}</div>
                  <div className="text-sm text-gray-500">{suggestion.address}</div>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// Date Time Picker
export function DateTimePicker({ 
  value, 
  onChange, 
  minDate, 
  maxDate, 
  placeholder = "Select date and time", 
  error,
  showTime = true 
}: DateTimePickerProps) {
  const [showPicker, setShowPicker] = useState(false);

  const formatDateTime = (date: Date) => {
    if (showTime) {
      return date.toLocaleString();
    }
    return date.toLocaleDateString();
  };

  return (
    <div className="relative">
      <EnhancedInput
        type="text"
        placeholder={placeholder}
        value={value ? formatDateTime(value) : ''}
        readOnly
        onClick={() => setShowPicker(true)}
        icon={showTime ? <Clock className="h-4 w-4" /> : <Calendar className="h-4 w-4" />}
        error={error}
        className="cursor-pointer"
      />
      
      {showPicker && (
        <div className="absolute top-full left-0 bg-white border border-gray-200 rounded-md shadow-lg z-50 p-4">
          <div className="space-y-4">
            <input
              type={showTime ? "datetime-local" : "date"}
              value={value ? (showTime ? value.toISOString().slice(0, 16) : value.toISOString().slice(0, 10)) : ''}
              onChange={(e) => {
                if (e.target.value) {
                  onChange?.(new Date(e.target.value));
                }
              }}
              min={minDate ? (showTime ? minDate.toISOString().slice(0, 16) : minDate.toISOString().slice(0, 10)) : undefined}
              max={maxDate ? (showTime ? maxDate.toISOString().slice(0, 16) : maxDate.toISOString().slice(0, 10)) : undefined}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowPicker(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                size="sm"
                onClick={() => setShowPicker(false)}
              >
                Done
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Phone Input with Country Code
export function PhoneInput({ 
  value, 
  onChange, 
  countryCode = '+91', 
  onCountryChange, 
  error, 
  placeholder = "Enter phone number" 
}: PhoneInputProps) {
  const countryCodes = [
    { code: '+91', country: 'IN', name: 'India' },
    { code: '+1', country: 'US', name: 'United States' },
    { code: '+44', country: 'GB', name: 'United Kingdom' },
    { code: '+86', country: 'CN', name: 'China' },
    { code: '+81', country: 'JP', name: 'Japan' },
  ];

  return (
    <div className="flex space-x-2">
      <Select value={countryCode} onValueChange={onCountryChange}>
        <SelectTrigger className="w-24">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {countryCodes.map((country) => (
            <SelectItem key={country.code} value={country.code}>
              {country.code}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <div className="flex-1">
        <EnhancedInput
          type="tel"
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          icon={<Phone className="h-4 w-4" />}
          error={error}
        />
      </div>
    </div>
  );
}

// Search Input with Suggestions
export function SearchInput({ 
  placeholder = "Search...", 
  value, 
  onChange, 
  onSearch, 
  suggestions = [], 
  loading 
}: {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (query: string) => void;
  suggestions?: string[];
  loading?: boolean;
}) {
  const [showSuggestions, setShowSuggestions] = useState(false);

  return (
    <div className="relative">
      <EnhancedInput
        type="search"
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={() => setShowSuggestions(true)}
        onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
        icon={<Search className="h-4 w-4" />}
        loading={loading}
        onKeyPress={(e) => {
          if (e.key === 'Enter') {
            onSearch?.(value || '');
          }
        }}
      />
      
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-40 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              type="button"
              onClick={() => {
                onChange?.(suggestion);
                setShowSuggestions(false);
              }}
              className="w-full text-left px-4 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-center">
                <Search className="h-4 w-4 text-gray-400 mr-3" />
                <span>{suggestion}</span>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// Form Actions Component
export function FormActions({ 
  primaryAction, 
  secondaryAction, 
  loading, 
  disabled 
}: {
  primaryAction: { label: string; onClick: () => void };
  secondaryAction?: { label: string; onClick: () => void };
  loading?: boolean;
  disabled?: boolean;
}) {
  return (
    <div className="flex flex-col sm:flex-row gap-3 pt-6">
      <Button
        onClick={primaryAction.onClick}
        disabled={disabled || loading}
        className="flex-1 sm:flex-none sm:min-w-32"
      >
        {loading && <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>}
        {primaryAction.label}
      </Button>
      
      {secondaryAction && (
        <Button
          variant="outline"
          onClick={secondaryAction.onClick}
          disabled={loading}
          className="flex-1 sm:flex-none sm:min-w-32"
        >
          {secondaryAction.label}
        </Button>
      )}
    </div>
  );
}
