import { Schema, model, models, Document } from 'mongoose';

export interface ITransaction extends Document {
  _id: string;
  userId: string;
  rideId?: string;
  type: 'payment' | 'refund' | 'reward' | 'withdrawal' | 'penalty' | 'credit' | 'debit' | 'payout' | 'fee' | 'bonus';
  category: 'ride_payment' | 'cancellation_fee' | 'reward_points' | 'driver_earnings' | 'bonus' | 'referral' | 'wallet_topup' | 'wallet_withdrawal' | 'platform_fee' | 'driver_payout';
  
  // Amount details
  amount: number;
  currency: string;
  
  // Payment details
  paymentMethod: 'cash' | 'card' | 'wallet' | 'upi' | 'bank_transfer' | 'netbanking' | 'emi' | 'bnpl';
  paymentGateway?: string;
  transactionId?: string;
  gatewayTransactionId?: string;
  walletId?: string;
  paymentId?: string;

  // Balance tracking
  balanceBefore?: number;
  balanceAfter?: number;
  
  // Status
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  
  // Metadata
  description: string;
  metadata?: {
    [key: string]: any;
  };
  
  // Reward points
  rewardPointsEarned?: number;
  rewardPointsUsed?: number;
  
  // Timestamps
  initiatedAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  failedAt?: Date;
  
  // Error handling
  errorCode?: string;
  errorMessage?: string;
  retryCount: number;
  
  // Reconciliation
  isReconciled: boolean;
  reconciledAt?: Date;
  
  createdAt: Date;
  updatedAt: Date;
}

const TransactionSchema = new Schema<ITransaction>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  rideId: {
    type: Schema.Types.ObjectId,
    ref: 'Ride',
  },
  type: {
    type: String,
    enum: ['payment', 'refund', 'reward', 'withdrawal', 'penalty'],
    required: true,
  },
  category: {
    type: String,
    enum: ['ride_payment', 'cancellation_fee', 'reward_points', 'driver_earnings', 'bonus', 'referral'],
    required: true,
  },
  
  amount: {
    type: Number,
    required: true,
  },
  currency: {
    type: String,
    default: 'INR',
  },
  
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'wallet', 'upi', 'bank_transfer'],
    required: true,
  },
  paymentGateway: String,
  transactionId: {
    type: String,
    unique: true,
    sparse: true,
  },
  gatewayTransactionId: String,
  
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending',
  },
  
  description: {
    type: String,
    required: true,
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {},
  },
  
  rewardPointsEarned: {
    type: Number,
    default: 0,
  },
  rewardPointsUsed: {
    type: Number,
    default: 0,
  },
  
  initiatedAt: {
    type: Date,
    default: Date.now,
  },
  processedAt: Date,
  completedAt: Date,
  failedAt: Date,
  
  errorCode: String,
  errorMessage: String,
  retryCount: {
    type: Number,
    default: 0,
  },
  
  isReconciled: {
    type: Boolean,
    default: false,
  },
  reconciledAt: Date,
}, {
  timestamps: true,
});

// Indexes for performance
TransactionSchema.index({ userId: 1 });
TransactionSchema.index({ rideId: 1 });
TransactionSchema.index({ type: 1 });
TransactionSchema.index({ status: 1 });
TransactionSchema.index({ transactionId: 1 });
TransactionSchema.index({ initiatedAt: -1 });
TransactionSchema.index({ isReconciled: 1 });

export const Transaction = models.Transaction || model<ITransaction>('Transaction', TransactionSchema);
