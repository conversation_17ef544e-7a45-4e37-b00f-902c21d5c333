"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowDown, ArrowUp, Download } from "lucide-react"

export function TransactionHistory() {
  const [filter, setFilter] = useState("all")

  // Sample transactions
  const transactions = [
    {
      id: 1,
      type: "credit",
      description: "Added money to wallet",
      amount: 500,
      date: "2023-03-25T10:30:00",
      status: "completed",
      method: "HDFC Bank Credit Card",
    },
    {
      id: 2,
      type: "debit",
      description: "Ride with Rahul S.",
      amount: 60,
      date: "2023-03-24T18:45:00",
      status: "completed",
      method: "Wallet",
    },
    {
      id: 3,
      type: "credit",
      description: "Cashback reward",
      amount: 25,
      date: "2023-03-24T18:45:00",
      status: "completed",
      method: "Promotion",
    },
    {
      id: 4,
      type: "debit",
      description: "Ride with Priya M.",
      amount: 40,
      date: "2023-03-23T09:15:00",
      status: "completed",
      method: "Wallet",
    },
    {
      id: 5,
      type: "credit",
      description: "Earnings from ride sharing",
      amount: 36,
      date: "2023-03-22T17:30:00",
      status: "completed",
      method: "Ride Earnings",
    },
    {
      id: 6,
      type: "debit",
      description: "Withdrawal to bank account",
      amount: 200,
      date: "2023-03-20T14:20:00",
      status: "completed",
      method: "Bank Transfer",
    },
  ]

  const filteredTransactions = filter === "all" ? transactions : transactions.filter((t) => t.type === filter)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction History</CardTitle>
        <CardDescription>View all your past transactions</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex justify-between items-center">
          <Tabs defaultValue="all" className="w-[400px]" onValueChange={setFilter}>
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="credit">Money In</TabsTrigger>
              <TabsTrigger value="debit">Money Out</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex gap-2">
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="this-month">This Month</SelectItem>
                <SelectItem value="last-month">Last Month</SelectItem>
                <SelectItem value="last-3-months">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {filteredTransactions.map((transaction) => (
            <div key={transaction.id} className="flex items-center justify-between border-b pb-4">
              <div className="flex items-center gap-4">
                <div
                  className={`h-10 w-10 rounded-full flex items-center justify-center ${
                    transaction.type === "credit" ? "bg-green-100 text-green-600" : "bg-red-100 text-red-600"
                  }`}
                >
                  {transaction.type === "credit" ? <ArrowDown className="h-5 w-5" /> : <ArrowUp className="h-5 w-5" />}
                </div>

                <div>
                  <p className="font-medium">{transaction.description}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(transaction.date).toLocaleDateString()} • {transaction.method}
                  </p>
                </div>
              </div>

              <div className="text-right">
                <p className={`font-medium ${transaction.type === "credit" ? "text-green-600" : "text-red-600"}`}>
                  {transaction.type === "credit" ? "+" : "-"}₹{transaction.amount}
                </p>
                <p className="text-xs text-muted-foreground uppercase">{transaction.status}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" className="w-full">
          Load More Transactions
        </Button>
      </CardFooter>
    </Card>
  )
}

