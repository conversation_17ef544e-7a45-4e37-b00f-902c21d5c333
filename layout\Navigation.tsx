'use client';

import Link from 'next/link';
import { useState } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import NotificationCenter from '@/components/notifications/NotificationCenter';
import {
  User,
  LogOut,
  Settings,
  MapPin,
  Car,
  CreditCard,
  Menu,
  Bike,
  Star,
  Wallet,
  Shield,
  HelpCircle
} from 'lucide-react';

export default function Navigation() {
  const { user, logout, isAuthenticated } = useAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const NavLink = ({ href, children, className = "", onClick }: {
    href: string;
    children: React.ReactNode;
    className?: string;
    onClick?: () => void;
  }) => (
    <Link
      href={href}
      className={`relative text-gray-700 hover:text-primary font-medium transition-all duration-200 hover:scale-105 ${className}`}
      onClick={onClick}
    >
      {children}
      <span className="absolute inset-x-0 -bottom-1 h-0.5 bg-primary transform scale-x-0 transition-transform duration-200 group-hover:scale-x-100"></span>
    </Link>
  );

  return (
    <nav className="bg-white/95 backdrop-blur-md shadow-soft border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Enhanced Logo */}
          <div className="flex items-center space-x-2">
            <div className="bg-gradient-primary p-2 rounded-xl shadow-glow">
              <Bike className="h-6 w-6 text-white" />
            </div>
            <Link href="/" className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              RideShare
            </Link>
          </div>

          {/* Enhanced Navigation Links */}
          <div className="hidden md:flex items-center space-x-6">
            {isAuthenticated ? (
              <>
                {user?.role === 'rider' && (
                  <>
                    <NavLink href="/book-ride" className="group flex items-center space-x-1">
                      <MapPin className="h-4 w-4" />
                      <span>Book Ride</span>
                    </NavLink>
                    <NavLink href="/dashboard" className="group flex items-center space-x-1">
                      <Car className="h-4 w-4" />
                      <span>My Rides</span>
                    </NavLink>
                  </>
                )}
                {user?.role === 'driver' && (
                  <>
                    <NavLink href="/driver/dashboard" className="group flex items-center space-x-1">
                      <Car className="h-4 w-4" />
                      <span>Dashboard</span>
                    </NavLink>
                    <div className="flex items-center space-x-1 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-green-600 font-medium">Online</span>
                    </div>
                  </>
                )}
              </>
            ) : (
              <>
                <NavLink href="/auth/login">
                  Sign In
                </NavLink>
                <Button asChild className="bg-gradient-primary hover:shadow-glow transition-all duration-300">
                  <Link href="/auth/register">
                    Get Started
                  </Link>
                </Button>
              </>
            )}
          </div>

          {/* Enhanced User Menu */}
          {isAuthenticated && user ? (
            <div className="flex items-center space-x-3">
              <NotificationCenter />

              {/* User Stats (for drivers) */}
              {user.role === 'driver' && (
                <div className="hidden lg:flex items-center space-x-3 text-sm">
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium">4.8</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Wallet className="h-4 w-4 text-green-500" />
                    <span className="font-medium">₹1,240</span>
                  </div>
                </div>
              )}

              <div className="hidden md:block text-right">
                <p className="text-sm font-semibold text-gray-900">{user.fullName}</p>
                <div className="flex items-center space-x-1">
                  <Badge variant="outline" className="text-xs capitalize">
                    {user.role}
                  </Badge>
                  {user.role === 'driver' && (
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  )}
                </div>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full ring-2 ring-primary/20 hover:ring-primary/40 transition-all">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={user.profileImage} alt={user.fullName} />
                      <AvatarFallback className="bg-gradient-primary text-white font-semibold">
                        {getInitials(user.firstName, user.lastName)}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-64 p-2" align="end" forceMount>
                  <div className="flex items-center space-x-3 p-3 bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg mb-2">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={user.profileImage} alt={user.fullName} />
                      <AvatarFallback className="bg-gradient-primary text-white">
                        {getInitials(user.firstName, user.lastName)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="font-semibold text-gray-900">{user.fullName}</p>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <Badge variant="outline" className="text-xs mt-1 capitalize">
                        {user.role}
                      </Badge>
                    </div>
                  </div>
                  <DropdownMenuSeparator />

                  {user.role === 'rider' && (
                    <>
                      <DropdownMenuItem asChild>
                        <Link href="/book-ride" className="cursor-pointer flex items-center">
                          <MapPin className="mr-3 h-4 w-4 text-primary" />
                          <span>Book a Ride</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href="/dashboard" className="cursor-pointer flex items-center">
                          <Car className="mr-3 h-4 w-4 text-primary" />
                          <span>My Rides</span>
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  {user.role === 'driver' && (
                    <DropdownMenuItem asChild>
                      <Link href="/driver/dashboard" className="cursor-pointer flex items-center">
                        <Car className="mr-3 h-4 w-4 text-primary" />
                        <span>Driver Dashboard</span>
                      </Link>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuItem asChild>
                    <Link href="/payments" className="cursor-pointer flex items-center">
                      <Wallet className="mr-3 h-4 w-4 text-green-600" />
                      <span>Wallet & Payments</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem asChild>
                    <Link href="/profile" className="cursor-pointer flex items-center">
                      <User className="mr-3 h-4 w-4 text-gray-600" />
                      <span>Profile Settings</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem asChild>
                    <Link href="/help" className="cursor-pointer flex items-center">
                      <HelpCircle className="mr-3 h-4 w-4 text-blue-600" />
                      <span>Help & Support</span>
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout} className="cursor-pointer flex items-center text-red-600 focus:text-red-600">
                    <LogOut className="mr-3 h-4 w-4" />
                    <span>Sign Out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ) : (
            <div className="hidden md:flex items-center space-x-3">
              <Button variant="outline" asChild className="hover:bg-primary/5">
                <Link href="/auth/login">Sign In</Link>
              </Button>
              <Button asChild className="bg-gradient-primary hover:shadow-glow transition-all duration-300">
                <Link href="/auth/register">Get Started</Link>
              </Button>
            </div>
          )}

          {/* Enhanced Mobile menu */}
          <div className="md:hidden">
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="relative">
                  <Menu className="h-5 w-5" />
                  {isAuthenticated && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full"></div>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                <SheetHeader>
                  <SheetTitle className="flex items-center space-x-2">
                    <div className="bg-gradient-primary p-2 rounded-lg">
                      <Bike className="h-5 w-5 text-white" />
                    </div>
                    <span>RideShare</span>
                  </SheetTitle>
                  <SheetDescription>
                    {isAuthenticated ? `Welcome back, ${user?.firstName}!` : 'Your ride sharing companion'}
                  </SheetDescription>
                </SheetHeader>

                <div className="mt-6 space-y-4">
                  {isAuthenticated ? (
                    <>
                      {user?.role === 'rider' && (
                        <>
                          <NavLink href="/book-ride" onClick={() => setMobileMenuOpen(false)} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                            <MapPin className="h-5 w-5 text-primary" />
                            <span>Book Ride</span>
                          </NavLink>
                          <NavLink href="/dashboard" onClick={() => setMobileMenuOpen(false)} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                            <Car className="h-5 w-5 text-primary" />
                            <span>My Rides</span>
                          </NavLink>
                        </>
                      )}
                      {user?.role === 'driver' && (
                        <NavLink href="/driver/dashboard" onClick={() => setMobileMenuOpen(false)} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                          <Car className="h-5 w-5 text-primary" />
                          <span>Dashboard</span>
                        </NavLink>
                      )}
                      <NavLink href="/payments" onClick={() => setMobileMenuOpen(false)} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                        <Wallet className="h-5 w-5 text-green-600" />
                        <span>Payments</span>
                      </NavLink>
                      <NavLink href="/profile" onClick={() => setMobileMenuOpen(false)} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                        <User className="h-5 w-5 text-gray-600" />
                        <span>Profile</span>
                      </NavLink>
                      <Button onClick={logout} variant="outline" className="w-full justify-start text-red-600 border-red-200 hover:bg-red-50">
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign Out
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button asChild variant="outline" className="w-full">
                        <Link href="/auth/login" onClick={() => setMobileMenuOpen(false)}>Sign In</Link>
                      </Button>
                      <Button asChild className="w-full bg-gradient-primary">
                        <Link href="/auth/register" onClick={() => setMobileMenuOpen(false)}>Get Started</Link>
                      </Button>
                    </>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
