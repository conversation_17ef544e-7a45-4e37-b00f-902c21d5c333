/**
 * AI-Powered Test Generation Service
 * Automatically generates comprehensive Jest tests for React components
 */

const fs = require('fs').promises;
const path = require('path');
const CodeAnalyzer = require('./code-analyzer');

class TestGenerator {
  constructor() {
    this.codeAnalyzer = new CodeAnalyzer();
    this.testTemplates = {
      react: this.getReactTestTemplate(),
      node: this.getNodeTestTemplate(),
      api: this.getApiTestTemplate()
    };
  }

  /**
   * Generate tests for a specific file
   */
  async generateTestsForFile(filePath) {
    try {
      const fileContent = await fs.readFile(filePath, 'utf8');
      const fileType = this.detectFileType(filePath, fileContent);
      
      console.log(`🧪 Generating tests for ${filePath} (${fileType})`);

      let testResult;
      switch (fileType) {
        case 'react-component':
          testResult = await this.generateReactComponentTests(filePath, fileContent);
          break;
        case 'api-route':
          testResult = await this.generateApiTests(filePath, fileContent);
          break;
        case 'utility':
          testResult = await this.generateUtilityTests(filePath, fileContent);
          break;
        default:
          testResult = await this.generateGenericTests(filePath, fileContent);
      }

      // Write test file
      await this.writeTestFile(testResult);
      
      return testResult;
    } catch (error) {
      console.error(`❌ Failed to generate tests for ${filePath}:`, error.message);
      throw error;
    }
  }

  /**
   * Generate tests for React components
   */
  async generateReactComponentTests(filePath, componentCode) {
    const componentName = this.extractComponentName(componentCode);
    const props = this.extractProps(componentCode);
    const hooks = this.extractHooks(componentCode);
    const events = this.extractEventHandlers(componentCode);

    const testCode = `
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import ${componentName} from '${this.getRelativeImportPath(filePath)}';

describe('${componentName}', () => {
  // Default props for testing
  const defaultProps = {
    ${this.generateDefaultProps(props)}
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(<${componentName} {...defaultProps} />);
      expect(screen.getByTestId('${componentName.toLowerCase()}')).toBeInTheDocument();
    });

    it('renders with correct initial state', () => {
      render(<${componentName} {...defaultProps} />);
      ${this.generateInitialStateTests(componentCode)}
    });

    ${this.generatePropTests(props, componentName)}
  });

  ${this.generateEventTests(events, componentName)}

  ${this.generateHookTests(hooks, componentName)}

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(<${componentName} {...defaultProps} />);
      ${this.generateAccessibilityTests(componentCode)}
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<${componentName} {...defaultProps} />);
      ${this.generateKeyboardTests(componentCode)}
    });
  });

  describe('Edge Cases', () => {
    ${this.generateEdgeCaseTests(componentCode, componentName)}
  });

  describe('Performance', () => {
    it('does not cause unnecessary re-renders', () => {
      const renderSpy = jest.fn();
      const TestComponent = React.memo(() => {
        renderSpy();
        return <${componentName} {...defaultProps} />;
      });
      
      const { rerender } = render(<TestComponent />);
      rerender(<TestComponent />);
      
      expect(renderSpy).toHaveBeenCalledTimes(1);
    });
  });
});`;

    return {
      filePath,
      testFilePath: this.getTestFilePath(filePath),
      testCode: testCode.trim(),
      componentName,
      coverage: this.calculateCoverage(componentCode, testCode)
    };
  }

  /**
   * Generate tests for API routes
   */
  async generateApiTests(filePath, apiCode) {
    const endpoints = this.extractApiEndpoints(apiCode);
    const testCode = `
import request from 'supertest';
import app from '${this.getRelativeImportPath(filePath)}';

describe('${path.basename(filePath, path.extname(filePath))} API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  ${endpoints.map(endpoint => this.generateEndpointTests(endpoint)).join('\n\n')}

  describe('Error Handling', () => {
    ${this.generateApiErrorTests(endpoints)}
  });

  describe('Security', () => {
    ${this.generateSecurityTests(endpoints)}
  });
});`;

    return {
      filePath,
      testFilePath: this.getTestFilePath(filePath),
      testCode: testCode.trim(),
      endpoints,
      coverage: this.calculateCoverage(apiCode, testCode)
    };
  }

  /**
   * Generate tests for utility functions
   */
  async generateUtilityTests(filePath, utilityCode) {
    const functions = this.extractFunctions(utilityCode);
    
    const testCode = `
import {
  ${functions.map(f => f.name).join(',\n  ')}
} from '${this.getRelativeImportPath(filePath)}';

describe('${path.basename(filePath, path.extname(filePath))} utilities', () => {
  ${functions.map(func => this.generateFunctionTests(func)).join('\n\n')}
});`;

    return {
      filePath,
      testFilePath: this.getTestFilePath(filePath),
      testCode: testCode.trim(),
      functions,
      coverage: this.calculateCoverage(utilityCode, testCode)
    };
  }

  /**
   * Detect file type for appropriate test generation
   */
  detectFileType(filePath, content) {
    if (content.includes('export default') && content.includes('return (')) {
      return 'react-component';
    }
    if (content.includes('app.get') || content.includes('app.post')) {
      return 'api-route';
    }
    if (content.includes('export function') || content.includes('export const')) {
      return 'utility';
    }
    return 'generic';
  }

  /**
   * Extract component name from React component code
   */
  extractComponentName(code) {
    const match = code.match(/(?:export default|const|function)\s+(\w+)/);
    return match ? match[1] : 'Component';
  }

  /**
   * Extract props from React component
   */
  extractProps(code) {
    const propsMatch = code.match(/\{\s*([^}]+)\s*\}/);
    if (!propsMatch) return [];
    
    return propsMatch[1]
      .split(',')
      .map(prop => prop.trim())
      .filter(prop => prop.length > 0);
  }

  /**
   * Extract hooks usage from component
   */
  extractHooks(code) {
    const hooks = [];
    const hookMatches = code.match(/use\w+\(/g);
    if (hookMatches) {
      hookMatches.forEach(hook => {
        const hookName = hook.replace('(', '');
        if (!hooks.includes(hookName)) {
          hooks.push(hookName);
        }
      });
    }
    return hooks;
  }

  /**
   * Extract event handlers from component
   */
  extractEventHandlers(code) {
    const events = [];
    const eventMatches = code.match(/on\w+\s*=\s*\{[^}]+\}/g);
    if (eventMatches) {
      eventMatches.forEach(event => {
        const eventName = event.match(/on(\w+)/)[1];
        events.push(eventName.toLowerCase());
      });
    }
    return events;
  }

  /**
   * Generate default props for testing
   */
  generateDefaultProps(props) {
    return props.map(prop => {
      if (prop.includes('onClick') || prop.includes('onSubmit')) {
        return `${prop}: jest.fn()`;
      }
      if (prop.includes('title') || prop.includes('name')) {
        return `${prop}: 'Test ${prop}'`;
      }
      if (prop.includes('id')) {
        return `${prop}: 'test-id'`;
      }
      return `${prop}: undefined`;
    }).join(',\n    ');
  }

  /**
   * Generate prop validation tests
   */
  generatePropTests(props, componentName) {
    return props.map(prop => `
    it('handles ${prop} prop correctly', () => {
      const testValue = 'test-${prop}';
      render(<${componentName} {...defaultProps} ${prop}={testValue} />);
      // Add specific assertions based on prop type
    });`).join('\n');
  }

  /**
   * Generate event handler tests
   */
  generateEventTests(events, componentName) {
    if (events.length === 0) return '';
    
    return `
  describe('Event Handling', () => {
    ${events.map(event => `
    it('handles ${event} event', async () => {
      const user = userEvent.setup();
      const handle${event.charAt(0).toUpperCase() + event.slice(1)} = jest.fn();
      
      render(<${componentName} {...defaultProps} on${event.charAt(0).toUpperCase() + event.slice(1)}={handle${event.charAt(0).toUpperCase() + event.slice(1)}} />);
      
      // Trigger the event and verify handler is called
      // Add specific event triggering logic here
      
      expect(handle${event.charAt(0).toUpperCase() + event.slice(1)}).toHaveBeenCalled();
    });`).join('\n')}
  });`;
  }

  /**
   * Get test file path
   */
  getTestFilePath(filePath) {
    const dir = path.dirname(filePath);
    const name = path.basename(filePath, path.extname(filePath));
    const ext = path.extname(filePath);
    return path.join(dir, `${name}.test${ext}`);
  }

  /**
   * Get relative import path
   */
  getRelativeImportPath(filePath) {
    return `./${path.basename(filePath, path.extname(filePath))}`;
  }

  /**
   * Write test file to disk
   */
  async writeTestFile(testResult) {
    try {
      await fs.writeFile(testResult.testFilePath, testResult.testCode, 'utf8');
      console.log(`✅ Test file generated: ${testResult.testFilePath}`);
    } catch (error) {
      console.error(`❌ Failed to write test file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calculate test coverage estimate
   */
  calculateCoverage(sourceCode, testCode) {
    const sourceLines = sourceCode.split('\n').filter(line => line.trim()).length;
    const testLines = testCode.split('\n').filter(line => line.trim()).length;
    
    return {
      estimated_coverage: Math.min(90, Math.round((testLines / sourceLines) * 100)),
      source_lines: sourceLines,
      test_lines: testLines
    };
  }

  /**
   * Get React test template
   */
  getReactTestTemplate() {
    return {
      imports: `import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';`,
      setup: `beforeEach(() => {
  jest.clearAllMocks();
});`,
      teardown: `afterEach(() => {
  jest.restoreAllMocks();
});`
    };
  }

  /**
   * Get Node.js test template
   */
  getNodeTestTemplate() {
    return {
      imports: `const request = require('supertest');`,
      setup: `beforeEach(() => {
  jest.clearAllMocks();
});`,
      teardown: `afterEach(() => {
  jest.restoreAllMocks();
});`
    };
  }

  /**
   * Get API test template
   */
  getApiTestTemplate() {
    return {
      imports: `import request from 'supertest';`,
      setup: `beforeEach(() => {
  jest.clearAllMocks();
});`,
      teardown: `afterEach(() => {
  jest.restoreAllMocks();
});`
    };
  }

  // Additional helper methods would be implemented here...
  generateInitialStateTests(code) { return '// Initial state tests'; }
  generateAccessibilityTests(code) { return '// Accessibility tests'; }
  generateKeyboardTests(code) { return '// Keyboard navigation tests'; }
  generateEdgeCaseTests(code, name) { return '// Edge case tests'; }
  generateApiErrorTests(endpoints) { return '// API error tests'; }
  generateSecurityTests(endpoints) { return '// Security tests'; }
  generateEndpointTests(endpoint) { return '// Endpoint tests'; }
  generateFunctionTests(func) { return '// Function tests'; }
  extractApiEndpoints(code) { return []; }
  extractFunctions(code) { return []; }
  generateGenericTests(filePath, content) { return { testCode: '// Generic tests' }; }
}

module.exports = TestGenerator;
