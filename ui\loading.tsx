'use client';

import { cn } from '@/lib/utils';
import { <PERSON><PERSON>, MapPin, Car, Clock } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  return (
    <div className={cn('animate-spin rounded-full border-2 border-gray-300 border-t-primary', sizeClasses[size], className)} />
  );
}

interface LoadingDotsProps {
  className?: string;
}

export function LoadingDots({ className }: LoadingDotsProps) {
  return (
    <div className={cn('flex space-x-1', className)}>
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className="w-2 h-2 bg-primary rounded-full animate-pulse"
          style={{ animationDelay: `${i * 0.2}s` }}
        />
      ))}
    </div>
  );
}

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
}

export function LoadingSkeleton({ className, lines = 3 }: LoadingSkeletonProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className="h-4 bg-gray-200 rounded animate-pulse"
          style={{ width: `${Math.random() * 40 + 60}%` }}
        />
      ))}
    </div>
  );
}

interface RideLoadingProps {
  message?: string;
  className?: string;
}

export function RideLoading({ message = 'Finding your ride...', className }: RideLoadingProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 space-y-6', className)}>
      <div className="relative">
        <div className="w-20 h-20 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <Bike className="w-8 h-8 text-primary animate-pulse" />
        </div>
      </div>
      <div className="text-center space-y-2">
        <p className="text-lg font-medium text-gray-900">{message}</p>
        <LoadingDots />
      </div>
    </div>
  );
}

interface MapLoadingProps {
  className?: string;
}

export function MapLoading({ className }: MapLoadingProps) {
  return (
    <div className={cn('bg-gray-100 rounded-lg p-8 flex flex-col items-center justify-center space-y-4', className)}>
      <div className="relative">
        <MapPin className="w-12 h-12 text-primary animate-bounce" />
        <div className="absolute -inset-2 border-2 border-primary/30 rounded-full animate-ping"></div>
      </div>
      <p className="text-gray-600 font-medium">Loading map...</p>
    </div>
  );
}

interface DriverLoadingProps {
  className?: string;
}

export function DriverLoading({ className }: DriverLoadingProps) {
  return (
    <div className={cn('flex items-center space-x-4 p-4 bg-blue-50 rounded-lg', className)}>
      <div className="relative">
        <Car className="w-8 h-8 text-primary animate-pulse" />
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
      </div>
      <div className="flex-1">
        <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
        <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4"></div>
      </div>
    </div>
  );
}

interface PageLoadingProps {
  title?: string;
  description?: string;
  className?: string;
}

export function PageLoading({ 
  title = 'Loading...', 
  description = 'Please wait while we prepare your content',
  className 
}: PageLoadingProps) {
  return (
    <div className={cn('min-h-screen flex items-center justify-center bg-gray-50', className)}>
      <div className="text-center space-y-6 max-w-md mx-auto p-8">
        <div className="relative mx-auto w-24 h-24">
          <div className="absolute inset-0 border-4 border-gray-200 rounded-full"></div>
          <div className="absolute inset-0 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <Bike className="w-10 h-10 text-primary" />
          </div>
        </div>
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          <p className="text-gray-600">{description}</p>
        </div>
        <LoadingDots className="justify-center" />
      </div>
    </div>
  );
}

interface ButtonLoadingProps {
  children: React.ReactNode;
  loading?: boolean;
  className?: string;
}

export function ButtonLoading({ children, loading = false, className }: ButtonLoadingProps) {
  return (
    <div className={cn('relative', className)}>
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" />
        </div>
      )}
      <div className={cn(loading && 'opacity-0')}>{children}</div>
    </div>
  );
}

interface CardLoadingProps {
  className?: string;
}

export function CardLoading({ className }: CardLoadingProps) {
  return (
    <div className={cn('p-6 bg-white rounded-lg shadow-soft border animate-pulse', className)}>
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-4/6"></div>
      </div>
    </div>
  );
}

interface ProgressBarProps {
  progress: number;
  className?: string;
  showPercentage?: boolean;
}

export function ProgressBar({ progress, className, showPercentage = true }: ProgressBarProps) {
  return (
    <div className={cn('w-full', className)}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">Progress</span>
        {showPercentage && (
          <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-gradient-primary h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
}

interface PulseLoadingProps {
  className?: string;
  children?: React.ReactNode;
}

export function PulseLoading({ className, children }: PulseLoadingProps) {
  return (
    <div className={cn('animate-pulse-soft', className)}>
      {children}
    </div>
  );
}
