'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { 
  Power,
  DollarSign,
  Clock,
  Star,
  MapPin,
  Navigation,
  Phone,
  MessageCircle,
  TrendingUp,
  Battery,
  Fuel,
  AlertCircle,
  CheckCircle,
  Target,
  Award,
  Calendar,
  BarChart3
} from 'lucide-react';

interface DriverDashboardProps {
  driverId: string;
}

export default function DriverDashboard({ driverId }: DriverDashboardProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [currentRide, setCurrentRide] = useState(null);
  const [earnings, setEarnings] = useState({ today: 450, week: 2800, month: 12500 });

  const driverData = {
    name: '<PERSON><PERSON>',
    rating: 4.8,
    totalRides: 1247,
    joinDate: '2023-01-15',
    vehicle: {
      type: 'Bike',
      model: 'Honda Activa',
      number: 'KA 01 AB 1234',
      fuelLevel: 75,
      batteryLevel: 85
    },
    todayStats: {
      ridesCompleted: 12,
      hoursOnline: 8.5,
      earnings: 450,
      rating: 4.9
    },
    weeklyGoal: {
      target: 3000,
      current: 2800,
      percentage: 93
    }
  };

  const pendingRides = [
    {
      id: 'R001',
      pickup: 'MG Road Metro Station',
      destination: 'Brigade Road',
      distance: 3.2,
      fare: 45,
      eta: 5,
      riderRating: 4.7
    },
    {
      id: 'R002',
      pickup: 'Koramangala 5th Block',
      destination: 'Electronic City',
      distance: 8.5,
      fare: 120,
      eta: 8,
      riderRating: 4.9
    }
  ];

  return (
    <div className="max-w-md mx-auto bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-green-700 text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold">Driver Dashboard</h1>
            <p className="text-green-100 text-sm">Welcome back, {driverData.name}</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm">{isOnline ? 'Online' : 'Offline'}</span>
            <Switch 
              checked={isOnline} 
              onCheckedChange={setIsOnline}
              className="data-[state=checked]:bg-white"
            />
          </div>
        </div>
      </div>

      {/* Online Status */}
      {isOnline && (
        <Card className="m-4 border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <p className="font-medium text-green-800">You're online and ready for rides!</p>
                <p className="text-sm text-green-600">Searching for nearby ride requests...</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Today's Earnings */}
      <Card className="m-4 shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center justify-between">
            <span className="flex items-center">
              <DollarSign className="h-4 w-4 mr-2 text-green-600" />
              Today's Earnings
            </span>
            <Badge className="bg-green-100 text-green-800">
              +₹{earnings.today}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">₹{earnings.today}</p>
              <p className="text-xs text-gray-600">Today</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-blue-600">₹{earnings.week}</p>
              <p className="text-xs text-gray-600">This Week</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-purple-600">₹{earnings.month}</p>
              <p className="text-xs text-gray-600">This Month</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Today's Stats */}
      <div className="grid grid-cols-2 gap-4 m-4">
        <Card className="text-center">
          <CardContent className="p-3">
            <Target className="h-5 w-5 text-blue-600 mx-auto mb-1" />
            <p className="text-xs text-gray-600">Rides</p>
            <p className="font-bold text-lg">{driverData.todayStats.ridesCompleted}</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-3">
            <Clock className="h-5 w-5 text-green-600 mx-auto mb-1" />
            <p className="text-xs text-gray-600">Hours Online</p>
            <p className="font-bold text-lg">{driverData.todayStats.hoursOnline}</p>
          </CardContent>
        </Card>
      </div>

      {/* Weekly Goal Progress */}
      <Card className="m-4 shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center">
            <Award className="h-4 w-4 mr-2 text-yellow-600" />
            Weekly Goal Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>₹{driverData.weeklyGoal.current} / ₹{driverData.weeklyGoal.target}</span>
              <span className="text-green-600">{driverData.weeklyGoal.percentage}%</span>
            </div>
            <Progress value={driverData.weeklyGoal.percentage} className="h-2" />
            <p className="text-xs text-gray-600">
              ₹{driverData.weeklyGoal.target - driverData.weeklyGoal.current} more to reach your goal!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Status */}
      <Card className="m-4 shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center">
            <span className="mr-2">🏍️</span>
            Vehicle Status
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">{driverData.vehicle.model}</span>
              <Badge variant="outline">{driverData.vehicle.number}</Badge>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Fuel className="h-4 w-4 text-orange-600" />
                <div className="flex-1">
                  <div className="flex justify-between text-xs">
                    <span>Fuel</span>
                    <span>{driverData.vehicle.fuelLevel}%</span>
                  </div>
                  <Progress value={driverData.vehicle.fuelLevel} className="h-1 mt-1" />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Battery className="h-4 w-4 text-green-600" />
                <div className="flex-1">
                  <div className="flex justify-between text-xs">
                    <span>Battery</span>
                    <span>{driverData.vehicle.batteryLevel}%</span>
                  </div>
                  <Progress value={driverData.vehicle.batteryLevel} className="h-1 mt-1" />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Available Rides */}
      <Card className="m-4 shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center justify-between">
            <span className="flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-blue-600" />
              Available Rides
            </span>
            <Badge className="bg-blue-100 text-blue-800">
              {pendingRides.length} nearby
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0 space-y-3">
          {pendingRides.map((ride) => (
            <Card key={ride.id} className="border border-gray-200">
              <CardContent className="p-3">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {ride.distance} km • ₹{ride.fare}
                    </Badge>
                    <div className="flex items-center text-xs text-gray-600">
                      <Star className="h-3 w-3 text-yellow-500 mr-1" />
                      {ride.riderRating}
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-gray-700">{ride.pickup}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-gray-700">{ride.destination}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between pt-2">
                    <span className="text-xs text-gray-600">
                      <Clock className="h-3 w-3 inline mr-1" />
                      {ride.eta} min away
                    </span>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" className="text-xs px-3 py-1">
                        Decline
                      </Button>
                      <Button size="sm" className="text-xs px-3 py-1 bg-green-600 hover:bg-green-700">
                        Accept
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 gap-4 m-4">
        <Button variant="outline" className="flex items-center justify-center space-x-2">
          <BarChart3 className="h-4 w-4" />
          <span>Analytics</span>
        </Button>
        <Button variant="outline" className="flex items-center justify-center space-x-2">
          <Calendar className="h-4 w-4" />
          <span>Schedule</span>
        </Button>
      </div>

      {/* Driver Rating */}
      <Card className="m-4 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium">Your Rating</p>
              <div className="flex items-center space-x-1">
                <Star className="h-5 w-5 text-yellow-500 fill-current" />
                <span className="text-lg font-bold">{driverData.rating}</span>
                <span className="text-sm text-gray-600">({driverData.totalRides} rides)</span>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Member since</p>
              <p className="text-sm font-medium">Jan 2023</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Emergency Button */}
      <div className="m-4">
        <Button 
          variant="destructive" 
          className="w-full flex items-center justify-center space-x-2"
        >
          <AlertCircle className="h-4 w-4" />
          <span>Emergency Support</span>
        </Button>
      </div>

      {/* Bottom Padding */}
      <div className="h-6"></div>
    </div>
  );
}
