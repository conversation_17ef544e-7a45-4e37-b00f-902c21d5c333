import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { fleetManagementService, microMobilityPricingService } from '@/lib/micromobility/fleetManagementService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const latitude = parseFloat(searchParams.get('latitude') || '0');
    const longitude = parseFloat(searchParams.get('longitude') || '0');
    const vehicleType = searchParams.get('vehicleType') || 'e_scooter';
    const maxDistance = parseInt(searchParams.get('maxDistance') || '500'); // meters
    const minBattery = parseInt(searchParams.get('minBattery') || '20');

    // Validate coordinates
    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: 'Valid latitude and longitude are required' },
        { status: 400 }
      );
    }

    // Find nearby vehicles
    const rideRequest = {
      userId: decoded.userId,
      vehicleType: vehicleType as any,
      location: { latitude, longitude },
      maxWalkingDistance: maxDistance,
      preferences: {
        batteryLevel: minBattery,
        maxUnlockFee: 50,
        preferredBrands: [],
      },
    };

    const nearbyVehicles = await fleetManagementService.findNearbyVehicles(rideRequest);

    // Calculate dynamic pricing for each vehicle
    const vehiclesWithPricing = nearbyVehicles.map(vehicle => {
      const distance = calculateDistance(
        { latitude, longitude },
        vehicle.location
      );

      // Determine demand level based on nearby vehicles
      const demandLevel = nearbyVehicles.length < 3 ? 'high' : 
                         nearbyVehicles.length < 6 ? 'medium' : 'low';

      const dynamicPricing = microMobilityPricingService.calculateDynamicPricing(
        vehicle.type,
        vehicle.battery.level,
        vehicle.location,
        demandLevel as any,
        new Date()
      );

      return {
        id: vehicle.id,
        type: vehicle.type,
        model: vehicle.model,
        manufacturer: vehicle.manufacturer,
        location: vehicle.location,
        distance: Math.round(distance * 1000), // meters
        walkingTime: Math.round((distance * 1000) / 80), // seconds at 80m/min
        battery: {
          level: vehicle.battery.level,
          estimatedRange: vehicle.battery.estimatedRange,
        },
        pricing: {
          unlockFee: dynamicPricing.unlockFee,
          perMinuteRate: dynamicPricing.perMinuteRate,
          perKmRate: dynamicPricing.perKmRate,
          maxDailyRate: dynamicPricing.maxDailyRate,
          multiplier: dynamicPricing.multiplier,
          factors: dynamicPricing.factors,
        },
        features: {
          gpsEnabled: vehicle.hardware.gpsEnabled,
          lights: vehicle.hardware.lights,
          horn: vehicle.hardware.horn,
        },
        zone: vehicle.zone,
        lastRide: vehicle.lastRide ? {
          rating: vehicle.lastRide.rating,
          distance: vehicle.lastRide.distance,
        } : null,
      };
    });

    // Sort by distance and battery level
    vehiclesWithPricing.sort((a, b) => {
      if (Math.abs(a.distance - b.distance) < 50) { // Within 50m
        return b.battery.level - a.battery.level;
      }
      return a.distance - b.distance;
    });

    const response = {
      success: true,
      data: {
        vehicles: vehiclesWithPricing,
        summary: {
          total: vehiclesWithPricing.length,
          averageDistance: vehiclesWithPricing.length > 0 
            ? Math.round(vehiclesWithPricing.reduce((sum, v) => sum + v.distance, 0) / vehiclesWithPricing.length)
            : 0,
          averageBattery: vehiclesWithPricing.length > 0
            ? Math.round(vehiclesWithPricing.reduce((sum, v) => sum + v.battery.level, 0) / vehiclesWithPricing.length)
            : 0,
          priceRange: vehiclesWithPricing.length > 0 ? {
            minUnlockFee: Math.min(...vehiclesWithPricing.map(v => v.pricing.unlockFee)),
            maxUnlockFee: Math.max(...vehiclesWithPricing.map(v => v.pricing.unlockFee)),
            avgPerMinute: Math.round(vehiclesWithPricing.reduce((sum, v) => sum + v.pricing.perMinuteRate, 0) / vehiclesWithPricing.length * 100) / 100,
          } : null,
        },
        searchCriteria: {
          location: { latitude, longitude },
          vehicleType,
          maxDistance,
          minBattery,
        },
        recommendations: generateRecommendations(vehiclesWithPricing),
      },
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Find vehicles API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to find nearby vehicles',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, vehicleId, location } = body;

    if (!action || !vehicleId) {
      return NextResponse.json(
        { error: 'Action and vehicleId are required' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'reserve':
        result = await fleetManagementService.reserveVehicle(vehicleId, decoded.userId);
        break;

      case 'start_ride':
        if (!location || !location.latitude || !location.longitude) {
          return NextResponse.json(
            { error: 'Valid location is required to start ride' },
            { status: 400 }
          );
        }
        result = await fleetManagementService.startRide(vehicleId, decoded.userId, location);
        break;

      case 'end_ride':
        const { rideId, endLocation, rating, feedback } = body;
        if (!rideId || !endLocation) {
          return NextResponse.json(
            { error: 'RideId and endLocation are required to end ride' },
            { status: 400 }
          );
        }
        result = await fleetManagementService.endRide(rideId, endLocation, rating, feedback);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: reserve, start_ride, end_ride' },
          { status: 400 }
        );
    }

    const response = {
      success: true,
      data: result,
      action,
      timestamp: Date.now(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Vehicle action API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Vehicle action failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateDistance(point1: { latitude: number; longitude: number }, point2: { latitude: number; longitude: number }): number {
  const R = 6371; // Earth's radius in km
  const dLat = toRadians(point2.latitude - point1.latitude);
  const dLon = toRadians(point2.longitude - point1.longitude);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(toRadians(point1.latitude)) * Math.cos(toRadians(point2.latitude)) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

function generateRecommendations(vehicles: any[]): string[] {
  const recommendations: string[] = [];

  if (vehicles.length === 0) {
    recommendations.push('No vehicles available in your area. Try expanding your search radius.');
    return recommendations;
  }

  const bestVehicle = vehicles[0];
  
  if (bestVehicle.battery.level > 80) {
    recommendations.push(`${bestVehicle.model} has excellent battery life (${bestVehicle.battery.level}%)`);
  }

  if (bestVehicle.distance < 100) {
    recommendations.push(`Closest vehicle is just ${bestVehicle.distance}m away`);
  }

  if (bestVehicle.pricing.multiplier < 1) {
    recommendations.push(`Save money with current low-demand pricing`);
  } else if (bestVehicle.pricing.multiplier > 1.2) {
    recommendations.push(`High demand area - consider waiting for lower prices`);
  }

  const highBatteryVehicles = vehicles.filter(v => v.battery.level > 70);
  if (highBatteryVehicles.length > 1) {
    recommendations.push(`${highBatteryVehicles.length} vehicles with 70%+ battery available`);
  }

  return recommendations;
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
