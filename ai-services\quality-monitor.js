/**
 * Quality Monitoring Dashboard Service
 * Provides real-time code quality metrics and trend analysis
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class QualityMonitor extends EventEmitter {
  constructor() {
    super();
    this.metrics = new Map();
    this.alerts = [];
    this.thresholds = {
      quality_decline: 2.0,      // Alert if quality drops by 2 points
      security_issues: 3,        // Alert if more than 3 security issues
      performance_degradation: 1.5, // Alert if performance score drops by 1.5
      test_coverage_drop: 10     // Alert if coverage drops by 10%
    };
    this.monitoringInterval = null;
    this.isMonitoring = false;
  }

  /**
   * Start continuous quality monitoring
   */
  startMonitoring(intervalMinutes = 30) {
    if (this.isMonitoring) {
      console.log('⚠️  Quality monitoring is already running');
      return;
    }

    console.log(`🔍 Starting quality monitoring (every ${intervalMinutes} minutes)`);
    this.isMonitoring = true;

    this.monitoringInterval = setInterval(async () => {
      await this.performQualityCheck();
    }, intervalMinutes * 60 * 1000);

    // Perform initial check
    this.performQualityCheck();
  }

  /**
   * Stop quality monitoring
   */
  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      this.isMonitoring = false;
      console.log('⏹️  Quality monitoring stopped');
    }
  }

  /**
   * Perform comprehensive quality check
   */
  async performQualityCheck() {
    try {
      console.log('📊 Performing quality check...');
      
      const currentMetrics = await this.collectCurrentMetrics();
      const previousMetrics = this.getLatestMetrics();
      
      // Store current metrics
      this.storeMetrics(currentMetrics);
      
      // Analyze trends and detect issues
      const analysis = this.analyzeQualityTrends(currentMetrics, previousMetrics);
      
      // Check for alerts
      const newAlerts = this.checkForAlerts(analysis);
      
      if (newAlerts.length > 0) {
        this.handleAlerts(newAlerts);
      }

      // Emit monitoring event
      this.emit('quality-check-complete', {
        metrics: currentMetrics,
        analysis,
        alerts: newAlerts,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Quality check complete. Score: ${currentMetrics.overall_quality}/10`);

    } catch (error) {
      console.error('❌ Quality check failed:', error.message);
      this.emit('monitoring-error', error);
    }
  }

  /**
   * Collect current code quality metrics
   */
  async collectCurrentMetrics() {
    const metrics = {
      timestamp: new Date().toISOString(),
      overall_quality: 0,
      security_score: 0,
      performance_score: 0,
      maintainability: 0,
      test_coverage: 0,
      code_complexity: 0,
      technical_debt: 0,
      file_metrics: [],
      team_metrics: {},
      trends: {}
    };

    try {
      // Analyze project files
      const projectFiles = await this.getProjectFiles();
      const fileAnalyses = [];

      for (const file of projectFiles.slice(0, 20)) { // Limit for performance
        const analysis = await this.analyzeFile(file);
        fileAnalyses.push(analysis);
        metrics.file_metrics.push({
          file: file,
          quality: analysis.quality_score,
          complexity: analysis.complexity,
          issues: analysis.issues.length
        });
      }

      // Calculate aggregate metrics
      metrics.overall_quality = this.calculateAverageQuality(fileAnalyses);
      metrics.security_score = this.calculateSecurityScore(fileAnalyses);
      metrics.performance_score = this.calculatePerformanceScore(fileAnalyses);
      metrics.maintainability = this.calculateMaintainabilityScore(fileAnalyses);
      metrics.code_complexity = this.calculateComplexityScore(fileAnalyses);
      
      // Get test coverage (if available)
      metrics.test_coverage = await this.getTestCoverage();
      
      // Calculate technical debt
      metrics.technical_debt = this.calculateTechnicalDebt(fileAnalyses);

      return metrics;

    } catch (error) {
      console.error('❌ Failed to collect metrics:', error.message);
      return this.getDefaultMetrics();
    }
  }

  /**
   * Analyze quality trends and detect patterns
   */
  analyzeQualityTrends(current, previous) {
    if (!previous) {
      return {
        trend: 'baseline',
        changes: {},
        insights: ['Initial quality baseline established'],
        recommendations: []
      };
    }

    const changes = {
      quality: current.overall_quality - previous.overall_quality,
      security: current.security_score - previous.security_score,
      performance: current.performance_score - previous.performance_score,
      maintainability: current.maintainability - previous.maintainability,
      test_coverage: current.test_coverage - previous.test_coverage
    };

    const trend = this.determineTrend(changes);
    const insights = this.generateInsights(changes, current);
    const recommendations = this.generateRecommendations(changes, current);

    return {
      trend,
      changes,
      insights,
      recommendations,
      analysis_timestamp: new Date().toISOString()
    };
  }

  /**
   * Check for quality alerts based on thresholds
   */
  checkForAlerts(analysis) {
    const alerts = [];

    // Quality decline alert
    if (analysis.changes.quality && analysis.changes.quality <= -this.thresholds.quality_decline) {
      alerts.push({
        type: 'quality_decline',
        severity: 'high',
        message: `Code quality declined by ${Math.abs(analysis.changes.quality).toFixed(1)} points`,
        value: analysis.changes.quality,
        threshold: this.thresholds.quality_decline,
        timestamp: new Date().toISOString()
      });
    }

    // Performance degradation alert
    if (analysis.changes.performance && analysis.changes.performance <= -this.thresholds.performance_degradation) {
      alerts.push({
        type: 'performance_degradation',
        severity: 'medium',
        message: `Performance score decreased by ${Math.abs(analysis.changes.performance).toFixed(1)} points`,
        value: analysis.changes.performance,
        threshold: this.thresholds.performance_degradation,
        timestamp: new Date().toISOString()
      });
    }

    // Test coverage drop alert
    if (analysis.changes.test_coverage && analysis.changes.test_coverage <= -this.thresholds.test_coverage_drop) {
      alerts.push({
        type: 'test_coverage_drop',
        severity: 'medium',
        message: `Test coverage dropped by ${Math.abs(analysis.changes.test_coverage).toFixed(1)}%`,
        value: analysis.changes.test_coverage,
        threshold: this.thresholds.test_coverage_drop,
        timestamp: new Date().toISOString()
      });
    }

    return alerts;
  }

  /**
   * Handle quality alerts
   */
  handleAlerts(alerts) {
    for (const alert of alerts) {
      console.log(`🚨 QUALITY ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`);
      
      // Store alert
      this.alerts.push(alert);
      
      // Emit alert event
      this.emit('quality-alert', alert);
      
      // Send notifications (if configured)
      this.sendAlertNotification(alert);
    }
  }

  /**
   * Generate quality dashboard data
   */
  generateDashboardData() {
    const latestMetrics = this.getLatestMetrics();
    const historicalData = this.getHistoricalMetrics(30); // Last 30 data points
    
    return {
      current_metrics: latestMetrics,
      trends: this.calculateTrends(historicalData),
      alerts: this.getRecentAlerts(7), // Last 7 days
      insights: this.generateDashboardInsights(latestMetrics, historicalData),
      recommendations: this.generateDashboardRecommendations(latestMetrics),
      team_performance: this.getTeamPerformanceMetrics(),
      quality_gates: this.getQualityGateStatus(latestMetrics),
      generated_at: new Date().toISOString()
    };
  }

  /**
   * Get quality gate status
   */
  getQualityGateStatus(metrics) {
    if (!metrics) return { status: 'unknown', gates: {} };

    const gates = {
      quality_threshold: {
        status: metrics.overall_quality >= 7 ? 'passed' : 'failed',
        value: metrics.overall_quality,
        threshold: 7
      },
      security_threshold: {
        status: metrics.security_score >= 8 ? 'passed' : 'failed',
        value: metrics.security_score,
        threshold: 8
      },
      test_coverage: {
        status: metrics.test_coverage >= 80 ? 'passed' : 'failed',
        value: metrics.test_coverage,
        threshold: 80
      },
      technical_debt: {
        status: metrics.technical_debt <= 20 ? 'passed' : 'failed',
        value: metrics.technical_debt,
        threshold: 20
      }
    };

    const passedGates = Object.values(gates).filter(gate => gate.status === 'passed').length;
    const totalGates = Object.keys(gates).length;
    
    return {
      status: passedGates === totalGates ? 'passed' : 'failed',
      score: Math.round((passedGates / totalGates) * 100),
      gates,
      summary: `${passedGates}/${totalGates} quality gates passed`
    };
  }

  // Helper methods for metrics calculation
  calculateAverageQuality(analyses) {
    if (analyses.length === 0) return 0;
    const scores = analyses.map(a => a.quality_score || 7);
    return Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 10) / 10;
  }

  calculateSecurityScore(analyses) {
    if (analyses.length === 0) return 8;
    const securityIssues = analyses.reduce((total, a) => {
      return total + (a.security_issues ? a.security_issues.length : 0);
    }, 0);
    return Math.max(0, 10 - (securityIssues * 0.5));
  }

  calculatePerformanceScore(analyses) {
    if (analyses.length === 0) return 8;
    const scores = analyses.map(a => a.performance_score || 8);
    return Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 10) / 10;
  }

  calculateMaintainabilityScore(analyses) {
    if (analyses.length === 0) return 7;
    const scores = analyses.map(a => a.maintainability_score || 7);
    return Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 10) / 10;
  }

  calculateComplexityScore(analyses) {
    if (analyses.length === 0) return 5;
    const complexities = analyses.map(a => a.complexity || 5);
    return Math.round((complexities.reduce((a, b) => a + b, 0) / complexities.length) * 10) / 10;
  }

  calculateTechnicalDebt(analyses) {
    const issues = analyses.reduce((total, a) => total + (a.issues ? a.issues.length : 0), 0);
    return Math.min(100, issues * 2); // Rough technical debt calculation
  }

  determineTrend(changes) {
    const significantChanges = Object.values(changes).filter(change => Math.abs(change) > 0.5);
    const positiveChanges = significantChanges.filter(change => change > 0).length;
    const negativeChanges = significantChanges.filter(change => change < 0).length;

    if (positiveChanges > negativeChanges) return 'improving';
    if (negativeChanges > positiveChanges) return 'declining';
    return 'stable';
  }

  generateInsights(changes, current) {
    const insights = [];
    
    if (changes.quality > 1) {
      insights.push('Code quality is improving significantly');
    } else if (changes.quality < -1) {
      insights.push('Code quality needs attention');
    }

    if (current.test_coverage < 70) {
      insights.push('Test coverage is below recommended threshold');
    }

    if (current.technical_debt > 30) {
      insights.push('Technical debt is accumulating and should be addressed');
    }

    return insights;
  }

  generateRecommendations(changes, current) {
    const recommendations = [];

    if (current.overall_quality < 7) {
      recommendations.push('Focus on code quality improvements in upcoming sprints');
    }

    if (current.test_coverage < 80) {
      recommendations.push('Increase test coverage for better code reliability');
    }

    if (changes.performance < -1) {
      recommendations.push('Review recent changes for performance optimizations');
    }

    return recommendations;
  }

  // Storage and retrieval methods
  storeMetrics(metrics) {
    const key = new Date().toISOString();
    this.metrics.set(key, metrics);
    
    // Keep only last 100 entries
    if (this.metrics.size > 100) {
      const firstKey = this.metrics.keys().next().value;
      this.metrics.delete(firstKey);
    }
  }

  getLatestMetrics() {
    const keys = Array.from(this.metrics.keys()).sort().reverse();
    return keys.length > 0 ? this.metrics.get(keys[0]) : null;
  }

  getHistoricalMetrics(count = 30) {
    const keys = Array.from(this.metrics.keys()).sort().reverse().slice(0, count);
    return keys.map(key => this.metrics.get(key));
  }

  getRecentAlerts(days = 7) {
    const cutoff = new Date();
    cutoff.setDate(cutoff.getDate() - days);
    
    return this.alerts.filter(alert => 
      new Date(alert.timestamp) > cutoff
    ).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  // Placeholder methods for advanced features
  async getProjectFiles() {
    // Return list of project files to analyze
    return ['components/use-mobile.tsx', 'components/use-toast.ts'];
  }

  async analyzeFile(filePath) {
    // Placeholder for file analysis
    return {
      quality_score: 7 + Math.random() * 2,
      complexity: 5 + Math.random() * 3,
      issues: [],
      security_issues: [],
      performance_score: 8 + Math.random(),
      maintainability_score: 7 + Math.random() * 2
    };
  }

  async getTestCoverage() {
    // Placeholder for test coverage calculation
    return 75 + Math.random() * 20;
  }

  getDefaultMetrics() {
    return {
      timestamp: new Date().toISOString(),
      overall_quality: 7,
      security_score: 8,
      performance_score: 8,
      maintainability: 7,
      test_coverage: 80,
      code_complexity: 5,
      technical_debt: 15,
      file_metrics: [],
      team_metrics: {},
      trends: {}
    };
  }

  sendAlertNotification(alert) {
    // Placeholder for notification system
    console.log(`📧 Alert notification sent: ${alert.message}`);
  }

  calculateTrends(historicalData) {
    // Placeholder for trend calculation
    return { quality: 'stable', performance: 'improving' };
  }

  generateDashboardInsights(current, historical) {
    return ['Quality metrics are within acceptable ranges'];
  }

  generateDashboardRecommendations(metrics) {
    return ['Continue current development practices'];
  }

  getTeamPerformanceMetrics() {
    return { average_quality: 7.5, improvement_rate: 0.1 };
  }
}

module.exports = QualityMonitor;
