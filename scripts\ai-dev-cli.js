#!/usr/bin/env node

/**
 * AI-Powered Development CLI Tool
 * Command-line interface for AI development assistant features
 */

const { program } = require('commander');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const chalk = require('chalk');

class AIDevCLI {
  constructor() {
    this.mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:8080';
    this.setupCommands();
  }

  setupCommands() {
    program
      .name('ai-dev')
      .description('AI-Powered Development Assistant CLI')
      .version('1.0.0');

    // Analyze command
    program
      .command('analyze <file>')
      .description('Analyze code file for quality, issues, and suggestions')
      .option('-o, --output <format>', 'Output format (json|table)', 'table')
      .action(async (file, options) => {
        await this.analyzeFile(file, options);
      });

    // Generate tests command
    program
      .command('test <file>')
      .description('Generate comprehensive tests for a component')
      .option('-w, --write', 'Write test file to disk', false)
      .option('-o, --output <path>', 'Output path for test file')
      .action(async (file, options) => {
        await this.generateTests(file, options);
      });

    // Refactor command
    program
      .command('refactor <file>')
      .description('Get refactoring suggestions for code')
      .option('-a, --apply', 'Apply suggested refactoring (interactive)', false)
      .action(async (file, options) => {
        await this.suggestRefactoring(file, options);
      });

    // PR description command
    program
      .command('pr [branch]')
      .description('Generate PR description from git diff')
      .option('-b, --base <branch>', 'Base branch for comparison', 'main')
      .action(async (branch, options) => {
        await this.generatePRDescription(branch, options);
      });

    // Batch analyze command
    program
      .command('batch <pattern>')
      .description('Batch analyze files matching pattern')
      .option('-r, --recursive', 'Search recursively', false)
      .option('-e, --exclude <patterns>', 'Exclude patterns (comma-separated)')
      .action(async (pattern, options) => {
        await this.batchAnalyze(pattern, options);
      });

    // Health check command
    program
      .command('health')
      .description('Check AI services health')
      .action(async () => {
        await this.healthCheck();
      });

    // Review command
    program
      .command('review <pr-number>')
      .description('Review a pull request with AI')
      .option('-d, --detailed', 'Generate detailed review comments', false)
      .action(async (prNumber, options) => {
        await this.reviewPullRequest(prNumber, options);
      });

    // Quality dashboard command
    program
      .command('dashboard')
      .description('Show quality monitoring dashboard')
      .option('-j, --json', 'Output as JSON', false)
      .action(async (options) => {
        await this.showQualityDashboard(options);
      });

    // Monitor command
    program
      .command('monitor <action>')
      .description('Start or stop quality monitoring')
      .option('-i, --interval <minutes>', 'Monitoring interval in minutes', '30')
      .action(async (action, options) => {
        await this.controlQualityMonitoring(action, options);
      });

    // UI component generation command
    program
      .command('generate-ui <name>')
      .description('Generate optimized UI component with AI')
      .option('-d, --description <desc>', 'Component description')
      .option('-p, --props <props>', 'Component props (JSON string)')
      .option('-f, --functionality <func>', 'Component functionality')
      .option('-a, --accessible', 'Generate with accessibility focus', false)
      .action(async (name, options) => {
        await this.generateUIComponent(name, options);
      });

    // UI analysis command
    program
      .command('analyze-ui <file>')
      .description('Analyze UI component for optimization opportunities')
      .option('-o, --output <format>', 'Output format (json|table)', 'table')
      .action(async (file, options) => {
        await this.analyzeUIComponent(file, options);
      });

    // UX analysis command
    program
      .command('analyze-ux <flow-file>')
      .description('Analyze user experience flow')
      .option('-o, --output <format>', 'Output format (json|table)', 'table')
      .action(async (flowFile, options) => {
        await this.analyzeUXFlow(flowFile, options);
      });

    // Accessibility audit command
    program
      .command('audit-a11y <target>')
      .description('Audit accessibility compliance')
      .option('-t, --type <type>', 'Target type (component|page)', 'component')
      .option('-f, --fix', 'Generate automated fixes', false)
      .action(async (target, options) => {
        await this.auditAccessibility(target, options);
      });

    // Performance monitoring command
    program
      .command('perf <action>')
      .description('UI performance monitoring (start|stop|dashboard)')
      .option('-i, --interval <seconds>', 'Monitoring interval in seconds', '30')
      .option('-c, --components <list>', 'Components to monitor (comma-separated)')
      .action(async (action, options) => {
        await this.handlePerformanceMonitoring(action, options);
      });

    // Integration testing command
    program
      .command('test-integration <spec-file>')
      .description('Test backend integration')
      .option('-g, --generate', 'Generate integration tests', false)
      .action(async (specFile, options) => {
        await this.testBackendIntegration(specFile, options);
      });

    // Setup command
    program
      .command('setup')
      .description('Setup AI development assistant')
      .action(async () => {
        await this.setup();
      });
  }

  async analyzeFile(filePath, options) {
    try {
      console.log(chalk.blue(`🔍 Analyzing: ${filePath}`));
      
      const codeContent = await fs.readFile(filePath, 'utf8');
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/analyze`, {
        filePath,
        codeContent
      });

      if (response.data.success) {
        const analysis = response.data.analysis.analysis;
        
        if (options.output === 'json') {
          console.log(JSON.stringify(analysis, null, 2));
        } else {
          this.displayAnalysisTable(analysis, filePath);
        }
      } else {
        console.error(chalk.red('❌ Analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error analyzing file: ${error.message}`));
      process.exit(1);
    }
  }

  async generateTests(filePath, options) {
    try {
      console.log(chalk.blue(`🧪 Generating tests for: ${filePath}`));
      
      const codeContent = await fs.readFile(filePath, 'utf8');
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-tests`, {
        filePath,
        codeContent
      });

      if (response.data.success) {
        const testResult = response.data.testResult;
        
        console.log(chalk.green(`✅ Tests generated successfully`));
        console.log(chalk.cyan(`📊 Estimated coverage: ${testResult.coverage.estimated_coverage}%`));
        
        if (options.write) {
          const outputPath = options.output || testResult.testFilePath;
          await fs.writeFile(outputPath, testResult.testCode, 'utf8');
          console.log(chalk.green(`📝 Test file written to: ${outputPath}`));
        } else {
          console.log('\n' + chalk.yellow('Generated test code:'));
          console.log(testResult.testCode);
        }
      } else {
        console.error(chalk.red('❌ Test generation failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error generating tests: ${error.message}`));
      process.exit(1);
    }
  }

  async suggestRefactoring(filePath, options) {
    try {
      console.log(chalk.blue(`🔧 Analyzing refactoring opportunities: ${filePath}`));
      
      const codeContent = await fs.readFile(filePath, 'utf8');
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/suggest-refactoring`, {
        filePath,
        codeContent
      });

      if (response.data.success) {
        const suggestions = response.data.suggestions.suggestions;
        
        console.log(chalk.green(`💡 Found ${suggestions.length} refactoring opportunities:`));
        
        suggestions.forEach((suggestion, index) => {
          console.log(chalk.cyan(`\n${index + 1}. ${suggestion.description}`));
          if (suggestion.effort) {
            console.log(chalk.gray(`   Effort: ${suggestion.effort}`));
          }
        });

        if (options.apply) {
          console.log(chalk.yellow('\n🚧 Interactive refactoring not yet implemented'));
        }
      } else {
        console.error(chalk.red('❌ Refactoring analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error analyzing refactoring: ${error.message}`));
      process.exit(1);
    }
  }

  async generatePRDescription(branch, options) {
    try {
      const currentBranch = branch || this.getCurrentBranch();
      console.log(chalk.blue(`📝 Generating PR description for: ${currentBranch}`));
      
      const gitDiff = this.getGitDiff(options.base, currentBranch);
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-pr`, {
        gitDiff,
        branchName: currentBranch
      });

      if (response.data.success) {
        const prDescription = response.data.prDescription;
        
        console.log(chalk.green('\n✅ PR Description Generated:'));
        console.log(chalk.cyan(`\n📋 Title: ${prDescription.title}`));
        console.log(chalk.white('\n📝 Description:'));
        console.log(prDescription.description);
      } else {
        console.error(chalk.red('❌ PR description generation failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error generating PR description: ${error.message}`));
      process.exit(1);
    }
  }

  async batchAnalyze(pattern, options) {
    try {
      console.log(chalk.blue(`🔍 Batch analyzing files matching: ${pattern}`));
      
      const files = await this.findFiles(pattern, options);
      console.log(chalk.cyan(`Found ${files.length} files to analyze`));
      
      const fileContents = [];
      for (const file of files) {
        const content = await fs.readFile(file, 'utf8');
        fileContents.push({ filePath: file, codeContent: content });
      }
      
      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/batch-analyze`, {
        files: fileContents
      });

      if (response.data.success) {
        const results = response.data.results;
        const summary = response.data.summary;
        
        console.log(chalk.green(`\n✅ Batch analysis complete:`));
        console.log(chalk.cyan(`📊 Total: ${summary.total}, Successful: ${summary.successful}, Failed: ${summary.failed}`));
        
        results.forEach(result => {
          if (result.success) {
            console.log(chalk.green(`✅ ${result.filePath}: Quality ${result.analysis.quality_score}/10`));
          } else {
            console.log(chalk.red(`❌ ${result.filePath}: ${result.error}`));
          }
        });
      } else {
        console.error(chalk.red('❌ Batch analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error in batch analysis: ${error.message}`));
      process.exit(1);
    }
  }

  async healthCheck() {
    try {
      console.log(chalk.blue('🔍 Checking AI services health...'));
      
      const response = await axios.get(`${this.mcpServerUrl}/ai-dev-assistant/health`);
      
      if (response.data.success) {
        const health = response.data.health;
        const services = response.data.services;
        
        console.log(chalk.green('✅ AI Development Assistant is healthy'));
        console.log(chalk.cyan(`📊 Ollama status: ${health.status}`));
        console.log(chalk.cyan(`🤖 Code Analyzer: ${services.codeAnalyzer}`));
        console.log(chalk.cyan(`🧪 Test Generator: ${services.testGenerator}`));
        
        if (health.models) {
          console.log(chalk.cyan(`📚 Available models: ${health.models}`));
        }
      } else {
        console.error(chalk.red('❌ AI services are not healthy'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Health check failed: ${error.message}`));
      console.log(chalk.yellow('💡 Make sure MCP server is running: npm run dev (in mcp-server directory)'));
      process.exit(1);
    }
  }

  async reviewPullRequest(prNumber, options) {
    try {
      console.log(chalk.blue(`🔍 Reviewing PR #${prNumber}...`));

      // This would integrate with GitHub API to get PR data
      // For now, simulate PR review
      const prData = {
        files: [],
        diff: '',
        metadata: {
          title: `PR #${prNumber}`,
          number: prNumber
        }
      };

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/review-pr`, {
        prData
      });

      if (response.data.success) {
        const review = response.data.reviewResults;

        console.log(chalk.green(`\n✅ PR Review Complete:`));
        console.log(chalk.cyan(`📊 Overall Score: ${review.overall_score}/10`));
        console.log(chalk.cyan(`📁 Files Reviewed: ${review.files.length}`));
        console.log(chalk.cyan(`⚠️  Issues Found: ${review.blocking_issues.length}`));

        if (options.detailed && review.recommendations.length > 0) {
          console.log(chalk.yellow('\n💡 Recommendations:'));
          review.recommendations.forEach((rec, index) => {
            console.log(chalk.white(`  ${index + 1}. ${rec.description}`));
          });
        }
      } else {
        console.error(chalk.red('❌ PR review failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error reviewing PR: ${error.message}`));
      process.exit(1);
    }
  }

  async showQualityDashboard(options) {
    try {
      console.log(chalk.blue('📊 Loading quality dashboard...'));

      const response = await axios.get(`${this.mcpServerUrl}/ai-dev-assistant/quality-dashboard`);

      if (response.data.success) {
        const dashboard = response.data.dashboard;

        if (options.json) {
          console.log(JSON.stringify(dashboard, null, 2));
        } else {
          this.displayQualityDashboard(dashboard);
        }
      } else {
        console.error(chalk.red('❌ Failed to load dashboard'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error loading dashboard: ${error.message}`));
      process.exit(1);
    }
  }

  async controlQualityMonitoring(action, options) {
    try {
      if (!['start', 'stop'].includes(action)) {
        console.error(chalk.red('❌ Invalid action. Use "start" or "stop"'));
        process.exit(1);
      }

      console.log(chalk.blue(`${action === 'start' ? '▶️' : '⏹️'} ${action.charAt(0).toUpperCase() + action.slice(1)}ing quality monitoring...`));

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/quality-monitoring`, {
        action,
        intervalMinutes: parseInt(options.interval)
      });

      if (response.data.success) {
        console.log(chalk.green(`✅ ${response.data.message}`));
        if (action === 'start') {
          console.log(chalk.cyan(`📊 Monitoring interval: ${options.interval} minutes`));
        }
      } else {
        console.error(chalk.red('❌ Failed to control monitoring'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error controlling monitoring: ${error.message}`));
      process.exit(1);
    }
  }

  displayQualityDashboard(dashboard) {
    console.log(chalk.green('\n📊 Quality Dashboard'));
    console.log(chalk.cyan('=================='));

    if (dashboard.current_metrics) {
      const metrics = dashboard.current_metrics;
      console.log(chalk.white(`\n📈 Current Metrics:`));
      console.log(chalk.cyan(`  Overall Quality: ${metrics.overall_quality}/10`));
      console.log(chalk.cyan(`  Security Score: ${metrics.security_score}/10`));
      console.log(chalk.cyan(`  Performance: ${metrics.performance_score}/10`));
      console.log(chalk.cyan(`  Test Coverage: ${metrics.test_coverage}%`));
      console.log(chalk.cyan(`  Technical Debt: ${metrics.technical_debt}%`));
    }

    if (dashboard.quality_gates) {
      const gates = dashboard.quality_gates;
      console.log(chalk.white(`\n🚪 Quality Gates (${gates.score}%):`));
      Object.entries(gates.gates).forEach(([gate, status]) => {
        const emoji = status.status === 'passed' ? '✅' : '❌';
        console.log(chalk.white(`  ${emoji} ${gate}: ${status.value}/${status.threshold}`));
      });
    }

    if (dashboard.alerts && dashboard.alerts.length > 0) {
      console.log(chalk.yellow(`\n🚨 Recent Alerts (${dashboard.alerts.length}):`));
      dashboard.alerts.slice(0, 3).forEach(alert => {
        const emoji = alert.severity === 'high' ? '🔴' : alert.severity === 'medium' ? '🟡' : '🔵';
        console.log(chalk.white(`  ${emoji} ${alert.message}`));
      });
    }

    if (dashboard.insights && dashboard.insights.length > 0) {
      console.log(chalk.blue(`\n💡 Insights:`));
      dashboard.insights.forEach(insight => {
        console.log(chalk.white(`  • ${insight}`));
      });
    }
  }

  async setup() {
    console.log(chalk.blue('🚀 Setting up AI Development Assistant...'));

    // Check prerequisites
    console.log(chalk.cyan('📋 Checking prerequisites...'));

    try {
      // Check if Ollama is installed
      const { execSync } = require('child_process');
      execSync('ollama --version', { stdio: 'ignore' });
      console.log(chalk.green('✅ Ollama is installed'));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Ollama not found. Please install from https://ollama.ai'));
    }

    // Check MCP server
    try {
      await this.healthCheck();
    } catch (error) {
      console.log(chalk.yellow('⚠️  MCP server not running. Start with: npm run dev'));
    }

    console.log(chalk.green('\n✅ Setup complete! Use "ai-dev --help" to see available commands.'));
    console.log(chalk.cyan('\n🆕 Available commands:'));
    console.log(chalk.white('  • ai-dev review <pr-number>     - Review a pull request'));
    console.log(chalk.white('  • ai-dev dashboard              - Show quality dashboard'));
    console.log(chalk.white('  • ai-dev monitor start          - Start quality monitoring'));
    console.log(chalk.white('  • ai-dev generate-ui <name>     - Generate UI component'));
    console.log(chalk.white('  • ai-dev analyze-ui <file>      - Analyze UI component'));
    console.log(chalk.white('  • ai-dev analyze-ux <flow>      - Analyze UX flow'));
    console.log(chalk.white('  • ai-dev audit-a11y <target>    - Audit accessibility'));
    console.log(chalk.white('  • ai-dev perf dashboard         - Performance dashboard'));
    console.log(chalk.white('  • ai-dev test-integration <spec> - Test backend integration'));
  }

  async generateUIComponent(name, options) {
    try {
      console.log(chalk.blue(`🎨 Generating UI component: ${name}`));

      const requirements = {
        name,
        description: options.description || `${name} component`,
        props: options.props ? JSON.parse(options.props) : {},
        functionality: options.functionality || 'Basic component functionality',
        designSystem: 'Modern, accessible design'
      };

      if (options.accessible) {
        requirements.accessibility_focus = true;
      }

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-component`, {
        requirements
      });

      if (response.data.success) {
        const component = response.data.component;

        console.log(chalk.green(`\n✅ Component generated successfully:`));
        console.log(chalk.cyan(`📊 Quality Score: ${component.metadata.quality_score}/10`));
        console.log(chalk.cyan(`♿ Accessibility Score: ${component.metadata.accessibility_score}/10`));
        console.log(chalk.cyan(`⚡ Performance Score: ${component.metadata.performance_score}/10`));

        // Write component to file
        const fileName = `${name}.tsx`;
        await fs.writeFile(fileName, component.code, 'utf8');
        console.log(chalk.green(`📝 Component saved to: ${fileName}`));

        if (component.analysis.recommendations?.length > 0) {
          console.log(chalk.yellow('\n💡 Recommendations:'));
          component.analysis.recommendations.forEach((rec, index) => {
            console.log(chalk.white(`  ${index + 1}. ${rec.description}`));
          });
        }
      } else {
        console.error(chalk.red('❌ Component generation failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error generating component: ${error.message}`));
      process.exit(1);
    }
  }

  async analyzeUIComponent(filePath, options) {
    try {
      console.log(chalk.blue(`🔍 Analyzing UI component: ${filePath}`));

      const componentCode = await fs.readFile(filePath, 'utf8');

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/analyze-ui`, {
        filePath,
        componentCode
      });

      if (response.data.success) {
        const analysis = response.data.analysis.analysis;

        if (options.output === 'json') {
          console.log(JSON.stringify(analysis, null, 2));
        } else {
          this.displayUIAnalysis(analysis, filePath);
        }
      } else {
        console.error(chalk.red('❌ UI analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error analyzing UI component: ${error.message}`));
      process.exit(1);
    }
  }

  async analyzeUXFlow(flowFile, options) {
    try {
      console.log(chalk.blue(`🔍 Analyzing UX flow: ${flowFile}`));

      const flowData = JSON.parse(await fs.readFile(flowFile, 'utf8'));

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/analyze-ux`, {
        flowData
      });

      if (response.data.success) {
        const uxAnalysis = response.data.uxAnalysis;

        if (options.output === 'json') {
          console.log(JSON.stringify(uxAnalysis, null, 2));
        } else {
          this.displayUXAnalysis(uxAnalysis);
        }
      } else {
        console.error(chalk.red('❌ UX analysis failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error analyzing UX flow: ${error.message}`));
      process.exit(1);
    }
  }

  async auditAccessibility(target, options) {
    try {
      console.log(chalk.blue(`♿ Auditing accessibility: ${target}`));

      let auditTarget;
      if (options.type === 'component') {
        const code = await fs.readFile(target, 'utf8');
        auditTarget = {
          type: 'component',
          content: {
            name: path.basename(target, path.extname(target)),
            code
          }
        };
      } else {
        auditTarget = {
          type: 'page',
          url: target
        };
      }

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/audit-accessibility`, {
        auditTarget
      });

      if (response.data.success) {
        const audit = response.data.audit;

        console.log(chalk.green(`\n♿ Accessibility Audit Results:`));
        console.log(chalk.cyan(`📊 Accessibility Score: ${audit.audit.accessibility_score}/10`));

        // Display WCAG compliance
        const wcag = audit.audit.wcag_compliance;
        console.log(chalk.white(`\n📋 WCAG Compliance:`));
        console.log(chalk.white(`  Level A: ${wcag.level_a.violations.length} violations`));
        console.log(chalk.white(`  Level AA: ${wcag.level_aa.violations.length} violations`));
        console.log(chalk.white(`  Level AAA: ${wcag.level_aaa.violations.length} violations`));

        if (audit.audit.critical_issues.length > 0) {
          console.log(chalk.red(`\n🚨 Critical Issues (${audit.audit.critical_issues.length}):`));
          audit.audit.critical_issues.slice(0, 5).forEach((issue, index) => {
            console.log(chalk.red(`  ${index + 1}. ${issue.description || issue.issue}`));
          });
        }

        if (options.fix && audit.audit.automated_fixes.length > 0) {
          console.log(chalk.yellow(`\n🔧 Automated Fixes Available (${audit.audit.automated_fixes.length}):`));
          audit.audit.automated_fixes.forEach((fix, index) => {
            console.log(chalk.yellow(`  ${index + 1}. ${fix.explanation || fix.description}`));
          });
        }
      } else {
        console.error(chalk.red('❌ Accessibility audit failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error auditing accessibility: ${error.message}`));
      process.exit(1);
    }
  }

  async handlePerformanceMonitoring(action, options) {
    try {
      if (action === 'dashboard') {
        console.log(chalk.blue('📊 Loading performance dashboard...'));

        const response = await axios.get(`${this.mcpServerUrl}/ai-dev-assistant/ui-performance`);

        if (response.data.success) {
          this.displayPerformanceDashboard(response.data.dashboard);
        } else {
          console.error(chalk.red('❌ Failed to load performance dashboard'));
        }
      } else if (['start', 'stop'].includes(action)) {
        console.log(chalk.blue(`${action === 'start' ? '▶️' : '⏹️'} ${action.charAt(0).toUpperCase() + action.slice(1)}ing performance monitoring...`));

        const config = action === 'start' ? {
          interval: parseInt(options.interval) * 1000,
          components: options.components ? options.components.split(',').map(c => ({ name: c.trim() })) : [],
          pages: []
        } : {};

        const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/ui-performance/monitor`, {
          action,
          config
        });

        if (response.data.success) {
          console.log(chalk.green(`✅ ${response.data.message}`));
          if (action === 'start') {
            console.log(chalk.cyan(`📊 Monitoring interval: ${options.interval} seconds`));
          }
        } else {
          console.error(chalk.red('❌ Failed to control performance monitoring'));
        }
      } else {
        console.error(chalk.red('❌ Invalid action. Use: start, stop, or dashboard'));
        process.exit(1);
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error with performance monitoring: ${error.message}`));
      process.exit(1);
    }
  }

  async testBackendIntegration(specFile, options) {
    try {
      console.log(chalk.blue(`🔗 Testing backend integration: ${specFile}`));

      const integrationData = JSON.parse(await fs.readFile(specFile, 'utf8'));

      const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/test-integration`, {
        integrationData
      });

      if (response.data.success) {
        const analysis = response.data.integrationAnalysis;

        console.log(chalk.green(`\n✅ Integration Analysis Complete:`));
        console.log(chalk.cyan(`📊 Integration Health: ${analysis.integration_health}/10`));

        if (analysis.analysis.issues.length > 0) {
          console.log(chalk.yellow(`\n⚠️  Issues Found (${analysis.analysis.issues.length}):`));
          analysis.analysis.issues.slice(0, 5).forEach((issue, index) => {
            console.log(chalk.yellow(`  ${index + 1}. ${issue.description}`));
          });
        }

        if (analysis.analysis.recommendations.length > 0) {
          console.log(chalk.blue(`\n💡 Recommendations (${analysis.analysis.recommendations.length}):`));
          analysis.analysis.recommendations.forEach((rec, index) => {
            console.log(chalk.blue(`  ${index + 1}. ${rec.description}`));
          });
        }

        if (options.generate) {
          console.log(chalk.green('\n🧪 Integration test generation would be implemented here'));
        }
      } else {
        console.error(chalk.red('❌ Integration testing failed'));
      }

    } catch (error) {
      console.error(chalk.red(`❌ Error testing integration: ${error.message}`));
      process.exit(1);
    }
  }

  displayAnalysisTable(analysis, filePath) {
    console.log(chalk.green(`\n✅ Analysis Results for ${filePath}:`));
    console.log(chalk.cyan(`📊 Quality Score: ${analysis.quality_score}/10`));
    
    if (analysis.issues && analysis.issues.length > 0) {
      console.log(chalk.yellow(`\n⚠️  Issues Found (${analysis.issues.length}):`));
      analysis.issues.forEach((issue, index) => {
        const severity = issue.severity === 'high' ? chalk.red : 
                        issue.severity === 'medium' ? chalk.yellow : chalk.gray;
        console.log(severity(`  ${index + 1}. ${issue.description} (Line ${issue.line})`));
      });
    }
    
    if (analysis.optimizations && analysis.optimizations.length > 0) {
      console.log(chalk.blue(`\n🚀 Optimization Suggestions (${analysis.optimizations.length}):`));
      analysis.optimizations.forEach((opt, index) => {
        console.log(chalk.cyan(`  ${index + 1}. ${opt.description}`));
      });
    }
  }

  getCurrentBranch() {
    const { execSync } = require('child_process');
    return execSync('git branch --show-current', { encoding: 'utf8' }).trim();
  }

  getGitDiff(base, branch) {
    const { execSync } = require('child_process');
    return execSync(`git diff ${base}...${branch}`, { encoding: 'utf8' });
  }

  async findFiles(pattern, options) {
    const glob = require('glob');
    return new Promise((resolve, reject) => {
      glob(pattern, { 
        ignore: options.exclude ? options.exclude.split(',') : [],
        recursive: options.recursive 
      }, (err, files) => {
        if (err) reject(err);
        else resolve(files);
      });
    });
  }

  displayUIAnalysis(analysis, filePath) {
    console.log(chalk.green(`\n🎨 UI Analysis Results for ${filePath}:`));
    console.log(chalk.cyan(`📊 Quality Score: ${analysis.quality_score}/10`));
    console.log(chalk.cyan(`♿ Accessibility Score: ${analysis.accessibility_score}/10`));
    console.log(chalk.cyan(`⚡ Performance Score: ${analysis.performance_score}/10`));
    console.log(chalk.cyan(`🎯 UX Score: ${analysis.ux_score}/10`));

    if (analysis.issues && analysis.issues.length > 0) {
      console.log(chalk.yellow(`\n⚠️  Issues Found (${analysis.issues.length}):`));
      analysis.issues.slice(0, 5).forEach((issue, index) => {
        const severity = issue.severity === 'critical' ? chalk.red :
                        issue.severity === 'high' ? chalk.yellow : chalk.gray;
        console.log(severity(`  ${index + 1}. ${issue.description}`));
      });
    }

    if (analysis.optimizations && analysis.optimizations.length > 0) {
      console.log(chalk.blue(`\n🚀 Optimization Opportunities (${analysis.optimizations.length}):`));
      analysis.optimizations.slice(0, 3).forEach((opt, index) => {
        console.log(chalk.cyan(`  ${index + 1}. ${opt.description}`));
      });
    }
  }

  displayUXAnalysis(uxAnalysis) {
    console.log(chalk.green(`\n🎯 UX Analysis Results:`));
    const analysis = uxAnalysis.analysis;

    console.log(chalk.cyan(`📊 UX Score: ${analysis.ux_score}/10`));
    console.log(chalk.cyan(`🎯 Usability Score: ${analysis.usability_score}/10`));
    console.log(chalk.cyan(`💰 Conversion Score: ${analysis.conversion_score}/10`));
    console.log(chalk.cyan(`♿ Accessibility Score: ${analysis.accessibility_score}/10`));
    console.log(chalk.cyan(`📱 Mobile Score: ${analysis.mobile_score}/10`));

    if (uxAnalysis.actionable_insights && uxAnalysis.actionable_insights.length > 0) {
      console.log(chalk.blue(`\n💡 Actionable Insights:`));
      uxAnalysis.actionable_insights.forEach((insight, index) => {
        console.log(chalk.blue(`  ${index + 1}. ${insight.description}`));
        if (insight.actions && insight.actions.length > 0) {
          insight.actions.forEach(action => {
            console.log(chalk.gray(`     • ${action}`));
          });
        }
      });
    }

    if (uxAnalysis.priority_matrix) {
      const matrix = uxAnalysis.priority_matrix;
      if (matrix.high_impact_low_effort && matrix.high_impact_low_effort.length > 0) {
        console.log(chalk.green(`\n🎯 Quick Wins (High Impact, Low Effort):`));
        matrix.high_impact_low_effort.slice(0, 3).forEach((item, index) => {
          console.log(chalk.green(`  ${index + 1}. ${item.description}`));
        });
      }
    }
  }

  displayPerformanceDashboard(dashboard) {
    console.log(chalk.green('\n⚡ UI Performance Dashboard'));
    console.log(chalk.cyan('================================'));

    if (dashboard.current_performance) {
      const perf = dashboard.current_performance;
      console.log(chalk.white(`\n📊 Current Performance:`));
      console.log(chalk.cyan(`  Overall Score: ${perf.overall_score}/100`));
      console.log(chalk.cyan(`  Performance Grade: ${perf.performance_grade}`));

      if (perf.core_web_vitals) {
        const vitals = perf.core_web_vitals;
        console.log(chalk.white(`\n🎯 Core Web Vitals:`));
        console.log(chalk.cyan(`  LCP: ${Math.round(vitals.largest_contentful_paint)}ms`));
        console.log(chalk.cyan(`  FID: ${Math.round(vitals.first_input_delay)}ms`));
        console.log(chalk.cyan(`  CLS: ${vitals.cumulative_layout_shift?.toFixed(3)}`));
      }

      if (perf.critical_issues && perf.critical_issues.length > 0) {
        console.log(chalk.red(`\n🚨 Critical Issues (${perf.critical_issues.length}):`));
        perf.critical_issues.slice(0, 3).forEach((issue, index) => {
          console.log(chalk.red(`  ${index + 1}. ${issue.description || issue}`));
        });
      }
    }

    if (dashboard.trends) {
      const trends = dashboard.trends;
      console.log(chalk.white(`\n📈 Performance Trends:`));
      console.log(chalk.cyan(`  Trend: ${trends.performance_trend}`));

      if (trends.regression_alerts && trends.regression_alerts.length > 0) {
        console.log(chalk.yellow(`  ⚠️  Regressions: ${trends.regression_alerts.length} detected`));
      }
    }

    if (dashboard.recommendations) {
      const rec = dashboard.recommendations;
      if (rec.quick_wins && rec.quick_wins.length > 0) {
        console.log(chalk.green(`\n🎯 Quick Wins:`));
        rec.quick_wins.slice(0, 3).forEach((win, index) => {
          console.log(chalk.green(`  ${index + 1}. ${win.description || win}`));
        });
      }
    }
  }

  run() {
    program.parse();
  }
}

// Run CLI if called directly
if (require.main === module) {
  const cli = new AIDevCLI();
  cli.run();
}

module.exports = AIDevCLI;
