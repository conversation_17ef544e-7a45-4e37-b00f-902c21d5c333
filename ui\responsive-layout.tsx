'use client';

import { ReactNode, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ContainerProps {
  children: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

interface GridProps {
  children: ReactNode;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
  className?: string;
}

interface FlexProps {
  children: ReactNode;
  direction?: 'row' | 'col';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  wrap?: boolean;
  gap?: number;
  className?: string;
}

interface StackProps {
  children: ReactNode;
  spacing?: number;
  direction?: 'vertical' | 'horizontal';
  align?: 'start' | 'center' | 'end' | 'stretch';
  className?: string;
}

interface SidebarLayoutProps {
  children: ReactNode;
  sidebar: ReactNode;
  sidebarWidth?: 'sm' | 'md' | 'lg';
  sidebarPosition?: 'left' | 'right';
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  className?: string;
}

interface MasonryProps {
  children: ReactNode[];
  columns?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
  className?: string;
}

// Hook for responsive breakpoints
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = useState<'sm' | 'md' | 'lg' | 'xl' | '2xl'>('lg');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 640) setBreakpoint('sm');
      else if (width < 768) setBreakpoint('md');
      else if (width < 1024) setBreakpoint('lg');
      else if (width < 1280) setBreakpoint('xl');
      else setBreakpoint('2xl');
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
}

// Container Component
export function Container({ children, size = 'lg', className }: ContainerProps) {
  const sizes = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full',
  };

  return (
    <div className={cn('mx-auto px-4 sm:px-6 lg:px-8', sizes[size], className)}>
      {children}
    </div>
  );
}

// Responsive Grid Component
export function Grid({ children, cols = { default: 1, md: 2, lg: 3 }, gap = 6, className }: GridProps) {
  const getGridCols = () => {
    const classes = [];
    
    if (cols.default) classes.push(`grid-cols-${cols.default}`);
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`);
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`);
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`);
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`);
    
    return classes.join(' ');
  };

  return (
    <div className={cn('grid', getGridCols(), `gap-${gap}`, className)}>
      {children}
    </div>
  );
}

// Flexible Flex Component
export function Flex({ 
  children, 
  direction = 'row', 
  align = 'start', 
  justify = 'start', 
  wrap = false,
  gap = 0,
  className 
}: FlexProps) {
  const directions = {
    row: 'flex-row',
    col: 'flex-col',
  };

  const alignments = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const justifications = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  return (
    <div className={cn(
      'flex',
      directions[direction],
      alignments[align],
      justifications[justify],
      wrap && 'flex-wrap',
      gap > 0 && `gap-${gap}`,
      className
    )}>
      {children}
    </div>
  );
}

// Stack Component for consistent spacing
export function Stack({ 
  children, 
  spacing = 4, 
  direction = 'vertical', 
  align = 'stretch',
  className 
}: StackProps) {
  const directions = {
    vertical: 'flex-col',
    horizontal: 'flex-row',
  };

  const alignments = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch',
  };

  const spacingClass = direction === 'vertical' ? `space-y-${spacing}` : `space-x-${spacing}`;

  return (
    <div className={cn(
      'flex',
      directions[direction],
      alignments[align],
      spacingClass,
      className
    )}>
      {children}
    </div>
  );
}

// Sidebar Layout Component
export function SidebarLayout({ 
  children, 
  sidebar, 
  sidebarWidth = 'md',
  sidebarPosition = 'left',
  collapsible = false,
  defaultCollapsed = false,
  className 
}: SidebarLayoutProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const widths = {
    sm: 'w-64',
    md: 'w-80',
    lg: 'w-96',
  };

  const collapsedWidth = 'w-16';

  return (
    <div className={cn('flex h-screen bg-gray-50', className)}>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        'fixed inset-y-0 z-50 flex flex-col bg-white border-r border-gray-200 lg:static lg:inset-auto lg:z-auto',
        sidebarPosition === 'left' ? 'left-0' : 'right-0',
        collapsible && isCollapsed ? collapsedWidth : widths[sidebarWidth],
        'transform transition-transform duration-300 ease-in-out lg:transform-none',
        isMobileOpen ? 'translate-x-0' : sidebarPosition === 'left' ? '-translate-x-full lg:translate-x-0' : 'translate-x-full lg:translate-x-0'
      )}>
        {/* Collapse Button */}
        {collapsible && (
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="hidden lg:flex items-center justify-center w-8 h-8 m-2 rounded-md hover:bg-gray-100"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        )}
        
        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto">
          {sidebar}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Menu Button */}
        <div className="lg:hidden flex items-center justify-between p-4 border-b border-gray-200">
          <button
            onClick={() => setIsMobileOpen(true)}
            className="p-2 rounded-md hover:bg-gray-100"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}

// Masonry Layout Component
export function Masonry({ 
  children, 
  columns = { default: 1, sm: 2, md: 3, lg: 4 }, 
  gap = 4,
  className 
}: MasonryProps) {
  const breakpoint = useBreakpoint();
  
  const getColumnCount = () => {
    if (breakpoint === 'sm') return columns.sm || columns.default || 1;
    if (breakpoint === 'md') return columns.md || columns.sm || columns.default || 1;
    if (breakpoint === 'lg') return columns.lg || columns.md || columns.sm || columns.default || 1;
    if (breakpoint === 'xl' || breakpoint === '2xl') return columns.xl || columns.lg || columns.md || columns.sm || columns.default || 1;
    return columns.default || 1;
  };

  const columnCount = getColumnCount();
  const columnArrays: ReactNode[][] = Array.from({ length: columnCount }, () => []);

  // Distribute children across columns
  children.forEach((child, index) => {
    const columnIndex = index % columnCount;
    columnArrays[columnIndex].push(child);
  });

  return (
    <div className={cn('flex', `gap-${gap}`, className)}>
      {columnArrays.map((column, columnIndex) => (
        <div key={columnIndex} className={cn('flex-1 flex flex-col', `gap-${gap}`)}>
          {column.map((child, childIndex) => (
            <div key={childIndex}>{child}</div>
          ))}
        </div>
      ))}
    </div>
  );
}

// Responsive Show/Hide Components
export function ShowOn({ 
  breakpoint, 
  children 
}: { 
  breakpoint: 'sm' | 'md' | 'lg' | 'xl' | '2xl'; 
  children: ReactNode; 
}) {
  const breakpoints = {
    sm: 'block sm:hidden',
    md: 'hidden sm:block md:hidden',
    lg: 'hidden md:block lg:hidden',
    xl: 'hidden lg:block xl:hidden',
    '2xl': 'hidden xl:block',
  };

  return <div className={breakpoints[breakpoint]}>{children}</div>;
}

export function HideOn({ 
  breakpoint, 
  children 
}: { 
  breakpoint: 'sm' | 'md' | 'lg' | 'xl' | '2xl'; 
  children: ReactNode; 
}) {
  const breakpoints = {
    sm: 'hidden sm:block',
    md: 'block sm:hidden md:block',
    lg: 'block md:hidden lg:block',
    xl: 'block lg:hidden xl:block',
    '2xl': 'block xl:hidden',
  };

  return <div className={breakpoints[breakpoint]}>{children}</div>;
}

// Aspect Ratio Container
export function AspectRatio({ 
  ratio = '16/9', 
  children, 
  className 
}: { 
  ratio?: string; 
  children: ReactNode; 
  className?: string; 
}) {
  return (
    <div className={cn('relative w-full', className)} style={{ aspectRatio: ratio }}>
      <div className="absolute inset-0">
        {children}
      </div>
    </div>
  );
}

// Sticky Container
export function Sticky({ 
  children, 
  top = 0, 
  className 
}: { 
  children: ReactNode; 
  top?: number; 
  className?: string; 
}) {
  return (
    <div 
      className={cn('sticky', className)} 
      style={{ top: `${top}px` }}
    >
      {children}
    </div>
  );
}

// Center Component
export function Center({ 
  children, 
  className 
}: { 
  children: ReactNode; 
  className?: string; 
}) {
  return (
    <div className={cn('flex items-center justify-center', className)}>
      {children}
    </div>
  );
}
