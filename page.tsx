'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Car, 
  Bike, 
  Package, 
  Building2, 
  MapPin, 
  Clock, 
  Star,
  TrendingUp,
  Leaf,
  Shield,
  Zap,
  Users,
  Globe,
  Award,
  ArrowRight,
  Play,
  Download,
  Menu,
  X,
  Phone,
  Mail,
  ChevronDown
} from 'lucide-react';

export default function Homepage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeService, setActiveService] = useState('rides');
  const [animatedStats, setAnimatedStats] = useState({
    rides: 0,
    users: 0,
    cities: 0,
    carbon: 0
  });

  // Animate statistics on load
  useEffect(() => {
    const targets = { rides: 2500000, users: 150000, cities: 50, carbon: 45000 };
    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      
      setAnimatedStats({
        rides: Math.floor(targets.rides * progress),
        users: Math.floor(targets.users * progress),
        cities: Math.floor(targets.cities * progress),
        carbon: Math.floor(targets.carbon * progress)
      });

      if (currentStep >= steps) {
        clearInterval(interval);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, []);

  const services = [
    {
      id: 'rides',
      title: 'Smart Ride Sharing',
      description: 'AI-powered matching with real-time tracking and route optimization',
      icon: <Car className="h-8 w-8" />,
      color: 'blue',
      features: ['Real-time tracking', 'AI route optimization', 'Multiple vehicle types', 'Safety features'],
      image: '🚗'
    },
    {
      id: 'vehicles',
      title: 'Micro-Mobility',
      description: 'Bikes and scooters with IoT tracking and smart unlock technology',
      icon: <Bike className="h-8 w-8" />,
      color: 'green',
      features: ['IoT-enabled vehicles', 'Battery monitoring', 'Eco-friendly options', 'City-wide coverage'],
      image: '🛴'
    },
    {
      id: 'delivery',
      title: 'Delivery Ecosystem',
      description: 'Food, packages, and logistics with lightning-fast delivery',
      icon: <Package className="h-8 w-8" />,
      color: 'orange',
      features: ['Food delivery', 'Package shipping', 'Real-time tracking', 'Multiple payment options'],
      image: '📦'
    },
    {
      id: 'corporate',
      title: 'Corporate Solutions',
      description: 'Business travel management with analytics and cost optimization',
      icon: <Building2 className="h-8 w-8" />,
      color: 'purple',
      features: ['Employee management', 'Cost analytics', 'Policy compliance', 'Carbon reporting'],
      image: '🏢'
    }
  ];

  const testimonials = [
    {
      name: 'Priya Sharma',
      role: 'Software Engineer',
      company: 'TechCorp',
      rating: 5,
      text: 'MobilityHub has transformed my daily commute. The AI recommendations save me time and money every day!',
      avatar: '👩‍💻'
    },
    {
      name: 'Rajesh Kumar',
      role: 'Business Owner',
      company: 'Local Restaurant',
      rating: 5,
      text: 'The delivery service helped us reach more customers. Fast, reliable, and great customer support.',
      avatar: '👨‍💼'
    },
    {
      name: 'Anita Patel',
      role: 'HR Manager',
      company: 'Global Inc',
      rating: 5,
      text: 'Corporate features are amazing! We reduced travel costs by 30% while improving employee satisfaction.',
      avatar: '👩‍💼'
    }
  ];

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M+';
    if (num >= 1000) return (num / 1000).toFixed(0) + 'K+';
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">M</span>
              </div>
              <span className="text-xl font-bold text-gray-900">MobilityHub</span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#services" className="text-gray-700 hover:text-blue-600 transition-colors">Services</a>
              <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors">Features</a>
              <a href="#about" className="text-gray-700 hover:text-blue-600 transition-colors">About</a>
              <a href="#contact" className="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
              <Button variant="outline">Sign In</Button>
              <Button>Get Started</Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-t">
            <div className="px-4 py-2 space-y-2">
              <a href="#services" className="block py-2 text-gray-700">Services</a>
              <a href="#features" className="block py-2 text-gray-700">Features</a>
              <a href="#about" className="block py-2 text-gray-700">About</a>
              <a href="#contact" className="block py-2 text-gray-700">Contact</a>
              <div className="pt-2 space-y-2">
                <Button variant="outline" className="w-full">Sign In</Button>
                <Button className="w-full">Get Started</Button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <Badge className="mb-6 bg-blue-100 text-blue-800 px-4 py-2">
              🚀 The Future of Urban Mobility is Here
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              One Platform for
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {' '}All Your Mobility
              </span>
              <br />Needs
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Experience the next generation of transportation with AI-powered ride sharing, 
              micro-mobility, delivery services, and corporate solutions - all in one app.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 mb-12">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 px-8 py-4 text-lg">
                <Download className="h-5 w-5 mr-2" />
                Download App
              </Button>
              <Button size="lg" variant="outline" className="px-8 py-4 text-lg">
                <Play className="h-5 w-5 mr-2" />
                Watch Demo
              </Button>
            </div>

            {/* Quick Booking */}
            <Card className="max-w-2xl mx-auto shadow-lg">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input placeholder="From" className="pl-10" />
                  </div>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input placeholder="To" className="pl-10" />
                  </div>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    Book Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Statistics */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-blue-600 mb-2">
                {formatNumber(animatedStats.rides)}
              </div>
              <div className="text-gray-600">Total Rides</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">
                {formatNumber(animatedStats.users)}
              </div>
              <div className="text-gray-600">Happy Users</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-purple-600 mb-2">
                {animatedStats.cities}+
              </div>
              <div className="text-gray-600">Cities</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">
                {formatNumber(animatedStats.carbon)}kg
              </div>
              <div className="text-gray-600">CO₂ Saved</div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need in One Platform
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From daily commutes to business travel, we've got you covered with our comprehensive mobility ecosystem.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service) => (
              <Card 
                key={service.id}
                className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 ${
                  activeService === service.id ? 'ring-2 ring-blue-500 shadow-lg' : ''
                }`}
                onClick={() => setActiveService(service.id)}
              >
                <CardContent className="p-6 text-center">
                  <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-${service.color}-100 flex items-center justify-center`}>
                    <div className={`text-${service.color}-600`}>
                      {service.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{service.description}</p>
                  <div className="space-y-1">
                    {service.features.map((feature, index) => (
                      <div key={index} className="text-xs text-gray-500 flex items-center">
                        <div className="w-1 h-1 bg-green-500 rounded-full mr-2"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Choose MobilityHub?
            </h2>
            <p className="text-xl text-gray-600">
              Advanced technology meets exceptional user experience
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Zap className="h-8 w-8 text-yellow-500" />,
                title: 'AI-Powered Optimization',
                description: 'Smart route planning and cost optimization using advanced machine learning algorithms.'
              },
              {
                icon: <Shield className="h-8 w-8 text-blue-500" />,
                title: 'Safety First',
                description: 'Real-time tracking, emergency features, and verified drivers for your peace of mind.'
              },
              {
                icon: <Leaf className="h-8 w-8 text-green-500" />,
                title: 'Eco-Friendly',
                description: 'Carbon footprint tracking and offset options to make your travel sustainable.'
              },
              {
                icon: <Clock className="h-8 w-8 text-purple-500" />,
                title: 'Real-Time Updates',
                description: 'Live tracking, ETA updates, and instant notifications for all your trips.'
              },
              {
                icon: <Users className="h-8 w-8 text-pink-500" />,
                title: 'Community Driven',
                description: 'Join thousands of users making cities more connected and sustainable.'
              },
              {
                icon: <Globe className="h-8 w-8 text-indigo-500" />,
                title: 'Multi-City Coverage',
                description: 'Available in 50+ cities with plans for rapid expansion across the country.'
              }
            ].map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Our Users Say
            </h2>
            <p className="text-xl text-gray-600">
              Join thousands of satisfied customers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-700 mb-4">"{testimonial.text}"</p>
                  <div className="flex items-center">
                    <div className="text-2xl mr-3">{testimonial.avatar}</div>
                    <div>
                      <div className="font-semibold">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}, {testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Transform Your Mobility?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Join millions of users who have already made the switch to smarter transportation.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4">
              <Download className="h-5 w-5 mr-2" />
              Download Now
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4">
              Learn More
              <ArrowRight className="h-5 w-5 ml-2" />
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">M</span>
                </div>
                <span className="text-xl font-bold">MobilityHub</span>
              </div>
              <p className="text-gray-400 mb-4">
                The future of urban mobility, powered by AI and designed for sustainability.
              </p>
              <div className="flex space-x-4">
                <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                  📱 iOS
                </Button>
                <Button size="sm" variant="ghost" className="text-gray-400 hover:text-white">
                  🤖 Android
                </Button>
              </div>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Services</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Ride Sharing</a></li>
                <li><a href="#" className="hover:text-white">Micro-Mobility</a></li>
                <li><a href="#" className="hover:text-white">Delivery</a></li>
                <li><a href="#" className="hover:text-white">Corporate</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">About Us</a></li>
                <li><a href="#" className="hover:text-white">Careers</a></li>
                <li><a href="#" className="hover:text-white">Press</a></li>
                <li><a href="#" className="hover:text-white">Blog</a></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold mb-4">Contact</h3>
              <ul className="space-y-2 text-gray-400">
                <li className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  <EMAIL>
                </li>
                <li className="flex items-center">
                  <Phone className="h-4 w-4 mr-2" />
                  +91 80000 12345
                </li>
                <li>
                  <MapPin className="h-4 w-4 mr-2 inline" />
                  Bangalore, India
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 MobilityHub. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
              <a href="#" className="text-gray-400 hover:text-white text-sm">Terms of Service</a>
              <a href="#" className="text-gray-400 hover:text-white text-sm">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
