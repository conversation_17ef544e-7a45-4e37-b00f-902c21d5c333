# Testing Requirements for AI-Powered Two-Wheeler Sharing Platform
# Install with: pip install -r requirements.txt

# Core testing dependencies
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0

# Data processing and analysis
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# HTTP client and API testing
requests>=2.28.0
httpx>=0.24.0

# JSON processing and validation
jsonschema>=4.17.0
pydantic>=1.10.0

# Performance testing
locust>=2.14.0
memory-profiler>=0.60.0

# Database testing
pymongo>=4.3.0
redis>=4.5.0

# ML model testing
scikit-learn>=1.2.0
tensorflow>=2.12.0
torch>=2.0.0

# Computer vision testing (for autonomous operations)
opencv-python>=4.7.0
pillow>=9.5.0

# Time series and datetime handling
python-dateutil>=2.8.0
pytz>=2023.3

# Logging and monitoring
structlog>=23.1.0
prometheus-client>=0.16.0

# Configuration and environment
python-dotenv>=1.0.0
pyyaml>=6.0

# Reporting and visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.14.0

# Security testing
cryptography>=40.0.0
jwt>=1.3.1

# Development and debugging
ipython>=8.12.0
ipdb>=0.13.0

# Type checking
mypy>=1.3.0
types-requests>=2.28.0
