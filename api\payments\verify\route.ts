import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { RazorpayService } from '@/lib/services/razorpayService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature,
      payment_method 
    } = body;

    // Validate required fields
    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return NextResponse.json(
        { error: 'Missing required payment verification parameters' },
        { status: 400 }
      );
    }

    // Verify payment signature
    const isValidSignature = RazorpayService.verifyPaymentSignature({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
    });

    if (!isValidSignature) {
      return NextResponse.json(
        { error: 'Invalid payment signature' },
        { status: 400 }
      );
    }

    // Process successful payment
    const result = await RazorpayService.processSuccessfulPayment({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      paymentMethod: payment_method,
    });

    return NextResponse.json({
      success: true,
      message: 'Payment verified and processed successfully',
      data: {
        paymentId: result.payment._id,
        transactionId: result.transaction._id,
        amount: result.payment.amount,
        status: result.payment.status,
      },
    });

  } catch (error) {
    console.error('Payment verification error:', error);
    
    // Try to process as failed payment if we have order ID
    const body = await request.json().catch(() => ({}));
    if (body.razorpay_order_id) {
      try {
        await RazorpayService.processFailedPayment(
          body.razorpay_order_id,
          error instanceof Error ? error.message : 'Payment verification failed'
        );
      } catch (failedError) {
        console.error('Error processing failed payment:', failedError);
      }
    }

    return NextResponse.json(
      { 
        error: 'Payment verification failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
