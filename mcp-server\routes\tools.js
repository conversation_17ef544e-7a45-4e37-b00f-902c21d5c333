const express = require('express');
const router = express.Router();
const axios = require('axios');
const Joi = require('joi');

// Tool execution endpoint
router.post('/execute', async (req, res) => {
  const { tool, parameters } = req.body;
  const io = req.app.get('io');
  const logger = req.app.get('logger');

  try {
    let result;
    
    switch (tool) {
      case 'notify_driver':
        result = await notifyDriver(parameters, io, logger);
        break;
      case 'assign_task':
        result = await assignTask(parameters, io, logger);
        break;
      case 'update_rewards':
        result = await updateRewards(parameters, logger);
        break;
      case 'calculate_pricing':
        result = await calculatePricing(parameters, logger);
        break;
      case 'predict_demand_hotspots':
        result = await predictDemandHotspots(parameters, logger);
        break;
      case 'recommend_driver_positioning':
        result = await recommendDriverPositioning(parameters, logger);
        break;
      case 'optimize_fleet_distribution':
        result = await optimizeFleetDistribution(parameters, logger);
        break;
      case 'predict_maintenance_needs':
        result = await predictMaintenanceNeeds(parameters, logger);
        break;
      default:
        return res.status(400).json({ error: 'Unknown tool' });
    }

    res.json({ success: true, result });
  } catch (error) {
    logger.error('Tool execution error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Notify driver tool
async function notifyDriver(params, io, logger) {
  const schema = Joi.object({
    driverId: Joi.string().required(),
    message: Joi.string().required(),
    taskId: Joi.string(),
    priority: Joi.string().valid('low', 'medium', 'high').default('medium')
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { driverId, message, taskId, priority } = value;

  // Send real-time notification via Socket.io
  io.to(`driver_${driverId}`).emit('notification', {
    id: Date.now().toString(),
    message,
    taskId,
    priority,
    timestamp: new Date().toISOString(),
    type: 'task_assignment'
  });

  // Store notification in database (implement as needed)
  logger.info(`Notification sent to driver ${driverId}: ${message}`);

  return {
    notificationId: Date.now().toString(),
    driverId,
    message,
    status: 'sent'
  };
}

// Assign task tool
async function assignTask(params, io, logger) {
  const schema = Joi.object({
    taskId: Joi.string().required(),
    driverId: Joi.string().required(),
    userId: Joi.string().required(),
    route: Joi.object(),
    estimatedEarnings: Joi.number()
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { taskId, driverId, userId, route, estimatedEarnings } = value;

  // Create task assignment
  const assignment = {
    taskId,
    driverId,
    userId,
    route,
    estimatedEarnings,
    status: 'assigned',
    assignedAt: new Date().toISOString()
  };

  // Notify driver
  io.to(`driver_${driverId}`).emit('task_assigned', assignment);
  
  // Notify user
  io.to(`user_${userId}`).emit('driver_assigned', {
    taskId,
    driverId,
    estimatedArrival: route?.estimatedArrival
  });

  logger.info(`Task ${taskId} assigned to driver ${driverId} for user ${userId}`);

  return assignment;
}

// Update rewards tool
async function updateRewards(params, logger) {
  const schema = Joi.object({
    userId: Joi.string().required(),
    points: Joi.number().required(),
    reason: Joi.string().required(),
    multiplier: Joi.number().default(1)
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { userId, points, reason, multiplier } = value;
  const finalPoints = Math.round(points * multiplier);

  // Update user rewards (implement database logic)
  const rewardUpdate = {
    userId,
    points: finalPoints,
    reason,
    multiplier,
    timestamp: new Date().toISOString()
  };

  logger.info(`Awarded ${finalPoints} points to user ${userId} for: ${reason}`);

  return rewardUpdate;
}

// Calculate pricing tool
async function calculatePricing(params, logger) {
  const schema = Joi.object({
    distance: Joi.number().required(),
    duration: Joi.number(),
    demand: Joi.string().valid('low', 'medium', 'high').default('medium'),
    weather: Joi.string(),
    timeOfDay: Joi.string()
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { distance, duration, demand, weather, timeOfDay } = value;

  // Base pricing: ₹5 per km
  let basePrice = distance * 5;
  
  // Demand multiplier
  const demandMultipliers = { low: 0.8, medium: 1.0, high: 1.5 };
  basePrice *= demandMultipliers[demand];

  // Weather adjustment
  if (weather === 'rain' || weather === 'storm') {
    basePrice *= 1.2;
  }

  // Time of day adjustment
  if (timeOfDay === 'peak') {
    basePrice *= 1.3;
  } else if (timeOfDay === 'night') {
    basePrice *= 1.1;
  }

  const pricing = {
    basePrice: Math.round(distance * 5),
    finalPrice: Math.round(basePrice),
    distance,
    demand,
    weather,
    timeOfDay,
    driverEarnings: Math.round(basePrice * 0.6), // 60% to driver
    platformFee: Math.round(basePrice * 0.4)     // 40% platform fee
  };

  logger.info(`Calculated pricing for ${distance}km ride: ₹${pricing.finalPrice}`);

  return pricing;
}

// Predict demand hotspots tool
async function predictDemandHotspots(params, logger) {
  const schema = Joi.object({
    time_horizon: Joi.number().default(60),
    location_filter: Joi.string().optional()
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { time_horizon, location_filter } = value;

  try {
    // Call ML service for demand hotspot prediction
    const response = await axios.post('http://ml-service:8081/analyze/demand-hotspots', {
      time_horizon,
      location_filter
    });

    const hotspots = response.data.hotspots || [];

    logger.info(`Predicted ${hotspots.length} demand hotspots`);

    return {
      hotspots,
      time_horizon,
      prediction_timestamp: new Date().toISOString(),
      total_hotspots: hotspots.length,
      high_priority_hotspots: hotspots.filter(h => h.hotspot_level === 'high').length
    };
  } catch (error) {
    logger.error(`Demand hotspot prediction error: ${error.message}`);
    // Fallback to basic hotspot prediction
    return {
      hotspots: [
        {
          location: 'Bandra Kurla Complex',
          latitude: 19.0596,
          longitude: 72.8656,
          hotspot_score: 12.5,
          hotspot_level: 'high',
          recommendation: 'Deploy 3-5 additional drivers immediately'
        }
      ],
      time_horizon,
      prediction_timestamp: new Date().toISOString(),
      fallback: true
    };
  }
}

// Recommend driver positioning tool
async function recommendDriverPositioning(params, logger) {
  const schema = Joi.object({
    current_drivers: Joi.array().required(),
    time_horizon: Joi.number().default(60),
    max_recommendations: Joi.number().default(10)
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { current_drivers, time_horizon, max_recommendations } = value;

  try {
    // Call ML service for fleet positioning optimization
    const response = await axios.post('http://ml-service:8081/optimize/fleet-positioning', {
      current_drivers,
      time_horizon
    });

    const optimization = response.data;
    const recommendations = optimization.recommendations.slice(0, max_recommendations);

    logger.info(`Generated ${recommendations.length} driver positioning recommendations`);

    return {
      recommendations,
      efficiency_score: optimization.efficiency_score,
      demand_hotspots: optimization.demand_hotspots,
      total_drivers_analyzed: current_drivers.length,
      repositioning_needed: recommendations.length,
      optimization_timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error(`Driver positioning recommendation error: ${error.message}`);
    // Fallback recommendation
    return {
      recommendations: [
        {
          driver_id: current_drivers[0]?.id || 'driver_1',
          recommended_location: {
            area: 'Bandra Kurla Complex',
            latitude: 19.0596,
            longitude: 72.8656
          },
          reason: 'High demand area - fallback recommendation',
          priority: 80
        }
      ],
      efficiency_score: 65.0,
      fallback: true
    };
  }
}

// Optimize fleet distribution tool
async function optimizeFleetDistribution(params, logger) {
  const schema = Joi.object({
    total_drivers: Joi.number().required(),
    target_areas: Joi.array().optional(),
    optimization_goal: Joi.string().valid('efficiency', 'coverage', 'revenue').default('efficiency')
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { total_drivers, target_areas, optimization_goal } = value;

  try {
    // Get demand predictions for optimization
    const demandResponse = await axios.get('http://ml-service:8081/predict/demand/batch');
    const predictions = demandResponse.data.predictions || [];

    // Calculate optimal distribution
    const distribution = calculateOptimalDistribution(predictions, total_drivers, optimization_goal);

    logger.info(`Optimized fleet distribution for ${total_drivers} drivers`);

    return {
      optimal_distribution: distribution,
      optimization_goal,
      total_drivers,
      coverage_score: calculateCoverageScore(distribution),
      efficiency_score: calculateDistributionEfficiency(distribution, predictions),
      recommendations: generateDistributionRecommendations(distribution)
    };
  } catch (error) {
    logger.error(`Fleet distribution optimization error: ${error.message}`);
    throw error;
  }
}

// Predict maintenance needs tool
async function predictMaintenanceNeeds(params, logger) {
  const schema = Joi.object({
    vehicle_ids: Joi.array().optional(),
    prediction_horizon_days: Joi.number().default(7),
    urgency_threshold: Joi.string().valid('low', 'medium', 'high').default('medium')
  });

  const { error, value } = schema.validate(params);
  if (error) throw new Error(error.details[0].message);

  const { vehicle_ids, prediction_horizon_days, urgency_threshold } = value;

  try {
    // Call ML service for maintenance prediction
    const response = await axios.get('http://ml-service:8081/predict/maintenance', {
      params: {
        vehicle_ids: vehicle_ids?.join(','),
        horizon_days: prediction_horizon_days,
        threshold: urgency_threshold
      }
    });

    const predictions = response.data.predictions || [];

    logger.info(`Predicted maintenance needs for ${predictions.length} vehicles`);

    return {
      maintenance_predictions: predictions,
      prediction_horizon_days,
      urgency_threshold,
      vehicles_needing_maintenance: predictions.filter(p => p.urgency !== 'low').length,
      estimated_downtime_hours: predictions.reduce((sum, p) => sum + (p.estimated_downtime || 0), 0),
      total_estimated_cost: predictions.reduce((sum, p) => sum + (p.estimated_cost || 0), 0)
    };
  } catch (error) {
    logger.error(`Maintenance prediction error: ${error.message}`);
    // Fallback maintenance prediction
    return {
      maintenance_predictions: [
        {
          vehicle_id: 'vehicle_001',
          maintenance_type: 'routine_service',
          urgency: 'medium',
          estimated_days_until_needed: 5,
          estimated_cost: 2500,
          estimated_downtime: 4
        }
      ],
      fallback: true
    };
  }
}

// Helper functions for fleet distribution optimization
function calculateOptimalDistribution(predictions, totalDrivers, goal) {
  const distribution = {};

  // Group predictions by location
  const locationDemand = {};
  predictions.forEach(pred => {
    if (!locationDemand[pred.location]) {
      locationDemand[pred.location] = 0;
    }
    locationDemand[pred.location] += pred.predicted_demand;
  });

  // Calculate total demand
  const totalDemand = Object.values(locationDemand).reduce((sum, demand) => sum + demand, 0);

  // Distribute drivers based on optimization goal
  Object.keys(locationDemand).forEach(location => {
    let allocation;

    switch (goal) {
      case 'efficiency':
        // Allocate based on demand proportion
        allocation = Math.round((locationDemand[location] / totalDemand) * totalDrivers);
        break;
      case 'coverage':
        // Ensure minimum coverage for all areas
        allocation = Math.max(1, Math.round((locationDemand[location] / totalDemand) * totalDrivers));
        break;
      case 'revenue':
        // Allocate more to high-revenue areas
        allocation = Math.round((locationDemand[location] / totalDemand) * totalDrivers * 1.2);
        break;
      default:
        allocation = Math.round((locationDemand[location] / totalDemand) * totalDrivers);
    }

    distribution[location] = {
      recommended_drivers: Math.max(1, allocation),
      predicted_demand: locationDemand[location],
      demand_ratio: locationDemand[location] / totalDemand
    };
  });

  return distribution;
}

function calculateCoverageScore(distribution) {
  const locations = Object.keys(distribution);
  const coveredLocations = locations.filter(loc => distribution[loc].recommended_drivers > 0);
  return (coveredLocations.length / locations.length) * 100;
}

function calculateDistributionEfficiency(distribution, predictions) {
  let totalEfficiency = 0;
  let locationCount = 0;

  Object.keys(distribution).forEach(location => {
    const drivers = distribution[location].recommended_drivers;
    const demand = distribution[location].predicted_demand;

    if (demand > 0) {
      const ratio = drivers / demand;
      const efficiency = Math.min(100, Math.max(0, 100 - Math.abs(ratio - 1) * 50));
      totalEfficiency += efficiency;
      locationCount++;
    }
  });

  return locationCount > 0 ? totalEfficiency / locationCount : 0;
}

function generateDistributionRecommendations(distribution) {
  const recommendations = [];

  Object.keys(distribution).forEach(location => {
    const data = distribution[location];
    const ratio = data.recommended_drivers / data.predicted_demand;

    if (ratio < 0.5) {
      recommendations.push({
        location,
        type: 'increase_drivers',
        message: `Increase drivers in ${location} - high demand, low supply`,
        priority: 'high'
      });
    } else if (ratio > 2) {
      recommendations.push({
        location,
        type: 'reduce_drivers',
        message: `Consider reducing drivers in ${location} - low demand, high supply`,
        priority: 'low'
      });
    }
  });

  return recommendations;
}

module.exports = router;
