'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  MapPin, 
  Clock, 
  CreditCard, 
  CheckCircle, 
  XCircle,
  Timer,
  Navigation,
  Phone,
  Star
} from 'lucide-react';

interface RideRequest {
  requestId: string;
  ride: {
    _id: string;
    pickupLocation: {
      address: string;
      landmark?: string;
    };
    dropoffLocation: {
      address: string;
      landmark?: string;
    };
    estimatedDistance: number;
    estimatedDuration: number;
    finalAmount: number;
    rideType: string;
    paymentMethod: string;
    specialInstructions?: string;
    rider: {
      firstName: string;
      lastName: string;
      phone: string;
      profileImage?: string;
    };
  };
  expiresAt: string;
  timeRemaining: number;
}

export default function DriverRequests() {
  const { user, token } = useAuth();
  const [requests, setRequests] = useState<RideRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isAvailable, setIsAvailable] = useState(true);

  // Fetch pending ride requests
  const fetchRequests = async () => {
    try {
      const response = await fetch('/api/rides/request-driver', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setRequests(data.requests || []);
      } else {
        setError('Failed to fetch ride requests');
      }
    } catch (error) {
      setError('Network error while fetching requests');
    } finally {
      setLoading(false);
    }
  };

  // Respond to ride request
  const respondToRequest = async (rideId: string, action: 'accept' | 'reject', requestId: string) => {
    try {
      const response = await fetch('/api/rides/respond', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ rideId, action, requestId }),
      });

      if (response.ok) {
        // Remove the request from the list
        setRequests(prev => prev.filter(req => req.requestId !== requestId));
        
        if (action === 'accept') {
          // Show success message or redirect to ride management
          alert('Ride accepted successfully!');
        }
      } else {
        const data = await response.json();
        setError(data.error || `Failed to ${action} ride request`);
      }
    } catch (error) {
      setError('Network error while responding to request');
    }
  };

  // Auto-refresh requests every 10 seconds
  useEffect(() => {
    if (user?.role === 'driver' && isAvailable) {
      fetchRequests();
      const interval = setInterval(fetchRequests, 10000);
      return () => clearInterval(interval);
    }
  }, [user, isAvailable]);

  // Update countdown timers
  useEffect(() => {
    const interval = setInterval(() => {
      setRequests(prev => 
        prev.map(request => ({
          ...request,
          timeRemaining: Math.max(0, Math.floor((new Date(request.expiresAt).getTime() - Date.now()) / 1000))
        })).filter(request => request.timeRemaining > 0)
      );
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTimeRemaining = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (user?.role !== 'driver') {
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold">Ride Requests</h2>
            <p className="text-gray-600">Incoming ride requests from nearby riders</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Available:</span>
              <Button
                variant={isAvailable ? "default" : "outline"}
                size="sm"
                onClick={() => setIsAvailable(!isAvailable)}
              >
                {isAvailable ? 'Online' : 'Offline'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!isAvailable && (
        <Alert className="mb-6">
          <AlertDescription>
            You are currently offline. Turn on availability to receive ride requests.
          </AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : requests.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Timer className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">No ride requests at the moment</p>
            <p className="text-gray-400 text-sm mt-2">
              {isAvailable ? 'Stay online to receive ride requests from nearby riders' : 'Turn on availability to start receiving requests'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {requests.map((request) => (
            <Card key={request.requestId} className="border-l-4 border-l-blue-500">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg flex items-center">
                      <Navigation className="w-5 h-5 mr-2" />
                      New Ride Request
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {request.ride.estimatedDistance} km • ~{request.ride.estimatedDuration} min • ₹{request.ride.finalAmount}
                    </CardDescription>
                  </div>
                  <div className="text-right">
                    <Badge variant="outline" className="text-red-600 border-red-600">
                      <Timer className="w-3 h-3 mr-1" />
                      {formatTimeRemaining(request.timeRemaining)}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Rider Information */}
                <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                  <Avatar>
                    <AvatarImage src={request.ride.rider.profileImage} />
                    <AvatarFallback>
                      {request.ride.rider.firstName.charAt(0)}{request.ride.rider.lastName.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium">{request.ride.rider.firstName} {request.ride.rider.lastName}</p>
                    <div className="flex items-center space-x-2">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span className="text-sm">4.8</span>
                      <span className="text-sm text-gray-500">• Rider</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    <Phone className="w-4 h-4 mr-1" />
                    Call
                  </Button>
                </div>

                {/* Route Information */}
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-4 h-4 text-green-500 mt-1" />
                    <div>
                      <p className="font-medium">Pickup</p>
                      <p className="text-sm text-gray-600">{request.ride.pickupLocation.address}</p>
                      {request.ride.pickupLocation.landmark && (
                        <p className="text-xs text-gray-500">{request.ride.pickupLocation.landmark}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <MapPin className="w-4 h-4 text-red-500 mt-1" />
                    <div>
                      <p className="font-medium">Destination</p>
                      <p className="text-sm text-gray-600">{request.ride.dropoffLocation.address}</p>
                      {request.ride.dropoffLocation.landmark && (
                        <p className="text-xs text-gray-500">{request.ride.dropoffLocation.landmark}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Ride Details */}
                <div className="grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Type</p>
                    <p className="font-medium capitalize">{request.ride.rideType}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Payment</p>
                    <p className="font-medium">{request.ride.paymentMethod.toUpperCase()}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-500">Earnings</p>
                    <p className="font-medium text-green-600">₹{Math.round(request.ride.finalAmount * 0.8)}</p>
                  </div>
                </div>

                {/* Special Instructions */}
                {request.ride.specialInstructions && (
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <p className="text-sm font-medium text-yellow-800">Special Instructions:</p>
                    <p className="text-sm text-yellow-700">{request.ride.specialInstructions}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Button 
                    onClick={() => respondToRequest(request.ride._id, 'accept', request.requestId)}
                    className="flex-1"
                    size="lg"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Accept Ride
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => respondToRequest(request.ride._id, 'reject', request.requestId)}
                    size="lg"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Decline
                  </Button>
                </div>

                {/* Additional Info */}
                <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>Requested {new Date(request.ride.pickupLocation.address).toLocaleTimeString()}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CreditCard className="w-3 h-3" />
                    <span>Platform fee: ₹{Math.round(request.ride.finalAmount * 0.2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
