/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    appDir: true,
  },
  env: {
    NEXT_PUBLIC_MCP_SERVER_URL: process.env.MCP_SERVER_URL || 'http://localhost:8080',
    NEXT_PUBLIC_N8N_WEBHOOK_URL: process.env.N8N_WEBHOOK_URL || 'http://localhost:5678/webhook',
  },
  async rewrites() {
    return [
      {
        source: '/webhook/:path*',
        destination: 'http://localhost:5678/webhook/:path*',
      },
      {
        source: '/mcp/:path*',
        destination: 'http://localhost:8080/:path*',
      },
    ]
  },
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },
}

module.exports = nextConfig
