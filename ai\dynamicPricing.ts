// Dynamic Pricing Engine with AI/ML
import { apmService } from '../monitoring/apm';
import { Location } from './routeOptimization';

export interface PricingRequest {
  pickup: Location;
  dropoff: Location;
  distance: number;
  estimatedDuration: number;
  requestTime: Date;
  rideType: 'standard' | 'premium' | 'shared';
  userId?: string;
  driverAvailability?: {
    nearbyDrivers: number;
    averageDistance: number;
  };
}

export interface PricingResponse {
  baseFare: number;
  distanceFare: number;
  timeFare: number;
  surgePricing: {
    multiplier: number;
    reason: string;
    isActive: boolean;
  };
  demandPricing: {
    adjustment: number;
    demandLevel: 'low' | 'medium' | 'high' | 'peak';
  };
  weatherPricing: {
    adjustment: number;
    condition: string;
  };
  finalAmount: number;
  breakdown: {
    base: number;
    distance: number;
    time: number;
    surge: number;
    demand: number;
    weather: number;
    taxes: number;
    platformFee: number;
  };
  confidence: number;
  validUntil: Date;
  alternatives?: PricingResponse[];
}

export interface DemandData {
  currentDemand: number;
  historicalAverage: number;
  trendDirection: 'increasing' | 'decreasing' | 'stable';
  peakHours: number[];
  seasonalFactor: number;
  eventImpact?: {
    type: 'concert' | 'sports' | 'festival' | 'conference';
    impact: number;
    location: Location;
    radius: number;
  };
}

export interface SupplyData {
  availableDrivers: number;
  averageResponseTime: number;
  utilizationRate: number;
  driverDensity: number;
  supplyTrend: 'increasing' | 'decreasing' | 'stable';
}

export interface MarketConditions {
  demand: DemandData;
  supply: SupplyData;
  competition: {
    averagePrice: number;
    priceRange: { min: number; max: number };
    marketShare: number;
  };
  externalFactors: {
    weather: {
      condition: string;
      severity: number;
      impact: number;
    };
    traffic: {
      congestionLevel: number;
      incidents: number;
      impact: number;
    };
    events: Array<{
      type: string;
      impact: number;
      location: Location;
      timeRange: { start: Date; end: Date };
    }>;
  };
}

class DynamicPricingEngine {
  private basePricing = {
    baseFare: 30, // INR
    perKmRate: 10, // INR per km
    perMinuteRate: 1.5, // INR per minute
    platformFeeRate: 0.05, // 5%
    taxRate: 0.05, // 5% GST
  };

  private surgeThresholds = {
    low: 1.0,
    medium: 1.2,
    high: 1.5,
    peak: 2.0,
    extreme: 3.0,
  };

  private mlModelEndpoint: string;
  private cache: Map<string, PricingResponse> = new Map();
  private cacheExpiry: number = 2 * 60 * 1000; // 2 minutes

  constructor() {
    this.mlModelEndpoint = process.env.PRICING_ML_ENDPOINT || 'http://localhost:8001';
  }

  /**
   * Calculate dynamic pricing for a ride request
   */
  async calculatePrice(request: PricingRequest): Promise<PricingResponse> {
    const timer = apmService.startTimer('dynamic_pricing');
    
    try {
      // Check cache first
      const cacheKey = this.generateCacheKey(request);
      const cachedPrice = this.getCachedPrice(cacheKey);
      if (cachedPrice) {
        timer.end(true);
        return cachedPrice;
      }

      // Get market conditions
      const marketConditions = await this.getMarketConditions(request);
      
      // Calculate base pricing
      const basePrice = this.calculateBasePrice(request);
      
      // Apply AI-powered dynamic adjustments
      const dynamicAdjustments = await this.calculateDynamicAdjustments(request, marketConditions);
      
      // Combine all pricing factors
      const finalPricing = this.combinePricingFactors(basePrice, dynamicAdjustments, request);
      
      // Apply business rules and constraints
      const constrainedPricing = this.applyPricingConstraints(finalPricing, request);
      
      // Generate alternatives
      const alternatives = await this.generatePricingAlternatives(request, marketConditions);
      
      const response: PricingResponse = {
        ...constrainedPricing,
        alternatives,
        validUntil: new Date(Date.now() + this.cacheExpiry),
      };

      // Cache the result
      this.cachePrice(cacheKey, response);
      
      // Record metrics
      apmService.recordBusinessMetric({
        event: 'price_calculated',
        value: response.finalAmount,
        timestamp: Date.now(),
        properties: {
          surgeMultiplier: response.surgePricing.multiplier,
          demandLevel: response.demandPricing.demandLevel,
          confidence: response.confidence,
        },
      });

      timer.end(true);
      return response;
      
    } catch (error) {
      timer.end(false);
      console.error('Dynamic pricing calculation failed:', error);
      
      // Fallback to basic pricing
      return this.getFallbackPricing(request);
    }
  }

  /**
   * Calculate base price without dynamic adjustments
   */
  private calculateBasePrice(request: PricingRequest) {
    const baseFare = this.basePricing.baseFare;
    const distanceFare = request.distance * this.basePricing.perKmRate;
    const timeFare = request.estimatedDuration * this.basePricing.perMinuteRate;
    
    // Apply ride type multiplier
    const rideTypeMultiplier = this.getRideTypeMultiplier(request.rideType);
    
    const subtotal = (baseFare + distanceFare + timeFare) * rideTypeMultiplier;
    const platformFee = subtotal * this.basePricing.platformFeeRate;
    const taxes = subtotal * this.basePricing.taxRate;
    
    return {
      baseFare,
      distanceFare,
      timeFare,
      subtotal,
      platformFee,
      taxes,
      total: subtotal + platformFee + taxes,
    };
  }

  /**
   * Calculate AI-powered dynamic adjustments
   */
  private async calculateDynamicAdjustments(
    request: PricingRequest, 
    marketConditions: MarketConditions
  ) {
    try {
      // Call ML model for pricing predictions
      const mlPrediction = await this.callPricingML(request, marketConditions);
      
      if (mlPrediction) {
        return mlPrediction;
      }
    } catch (error) {
      console.error('ML pricing prediction failed:', error);
    }

    // Fallback to rule-based adjustments
    return this.calculateRuleBasedAdjustments(request, marketConditions);
  }

  /**
   * Call ML model for pricing predictions
   */
  private async callPricingML(request: PricingRequest, marketConditions: MarketConditions) {
    const features = this.extractPricingFeatures(request, marketConditions);
    
    const response = await fetch(`${this.mlModelEndpoint}/predict-price`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ML_API_KEY}`,
      },
      body: JSON.stringify({
        features,
        basePrice: this.calculateBasePrice(request).total,
      }),
    });

    if (response.ok) {
      return await response.json();
    }
    
    return null;
  }

  /**
   * Extract features for ML model
   */
  private extractPricingFeatures(request: PricingRequest, marketConditions: MarketConditions) {
    const now = request.requestTime;
    
    return {
      // Temporal features
      hourOfDay: now.getHours(),
      dayOfWeek: now.getDay(),
      isWeekend: now.getDay() === 0 || now.getDay() === 6,
      isRushHour: this.isRushHour(now),
      
      // Geographic features
      pickupLatitude: request.pickup.latitude,
      pickupLongitude: request.pickup.longitude,
      dropoffLatitude: request.dropoff.latitude,
      dropoffLongitude: request.dropoff.longitude,
      distance: request.distance,
      estimatedDuration: request.estimatedDuration,
      
      // Market features
      demandLevel: this.getDemandLevel(marketConditions.demand),
      supplyLevel: this.getSupplyLevel(marketConditions.supply),
      demandSupplyRatio: marketConditions.demand.currentDemand / marketConditions.supply.availableDrivers,
      
      // External factors
      weatherSeverity: marketConditions.externalFactors.weather.severity,
      trafficCongestion: marketConditions.externalFactors.traffic.congestionLevel,
      eventImpact: marketConditions.externalFactors.events.reduce((sum, event) => sum + event.impact, 0),
      
      // Competition
      competitorAvgPrice: marketConditions.competition.averagePrice,
      marketShare: marketConditions.competition.marketShare,
      
      // User features
      rideType: request.rideType,
      nearbyDrivers: request.driverAvailability?.nearbyDrivers || 0,
    };
  }

  /**
   * Rule-based pricing adjustments fallback
   */
  private calculateRuleBasedAdjustments(request: PricingRequest, marketConditions: MarketConditions) {
    const adjustments = {
      surge: this.calculateSurgeMultiplier(marketConditions),
      demand: this.calculateDemandAdjustment(marketConditions.demand),
      weather: this.calculateWeatherAdjustment(marketConditions.externalFactors.weather),
      traffic: this.calculateTrafficAdjustment(marketConditions.externalFactors.traffic),
      competition: this.calculateCompetitionAdjustment(marketConditions.competition),
      confidence: 0.7, // Rule-based confidence
    };

    return adjustments;
  }

  /**
   * Calculate surge pricing multiplier
   */
  private calculateSurgeMultiplier(marketConditions: MarketConditions): number {
    const demandSupplyRatio = marketConditions.demand.currentDemand / marketConditions.supply.availableDrivers;
    
    if (demandSupplyRatio >= 3.0) return this.surgeThresholds.extreme;
    if (demandSupplyRatio >= 2.5) return this.surgeThresholds.peak;
    if (demandSupplyRatio >= 2.0) return this.surgeThresholds.high;
    if (demandSupplyRatio >= 1.5) return this.surgeThresholds.medium;
    
    return this.surgeThresholds.low;
  }

  /**
   * Calculate demand-based adjustment
   */
  private calculateDemandAdjustment(demand: DemandData): number {
    const demandRatio = demand.currentDemand / demand.historicalAverage;
    
    if (demandRatio >= 1.5) return 0.3; // 30% increase
    if (demandRatio >= 1.3) return 0.2; // 20% increase
    if (demandRatio >= 1.1) return 0.1; // 10% increase
    if (demandRatio <= 0.7) return -0.1; // 10% decrease
    
    return 0; // No adjustment
  }

  /**
   * Calculate weather-based adjustment
   */
  private calculateWeatherAdjustment(weather: any): number {
    const weatherMultipliers = {
      clear: 0,
      cloudy: 0.05,
      rain: 0.15,
      heavy_rain: 0.25,
      storm: 0.35,
      fog: 0.2,
      snow: 0.3,
    };
    
    return weatherMultipliers[weather.condition] || 0;
  }

  /**
   * Calculate traffic-based adjustment
   */
  private calculateTrafficAdjustment(traffic: any): number {
    if (traffic.congestionLevel >= 0.8) return 0.2; // Heavy traffic
    if (traffic.congestionLevel >= 0.6) return 0.1; // Moderate traffic
    if (traffic.congestionLevel >= 0.4) return 0.05; // Light traffic
    
    return 0;
  }

  /**
   * Calculate competition-based adjustment
   */
  private calculateCompetitionAdjustment(competition: any): number {
    // Adjust pricing based on competitor prices
    const competitorAvg = competition.averagePrice;
    const ourEstimate = 100; // This would be our current estimate
    
    const priceDiff = (competitorAvg - ourEstimate) / ourEstimate;
    
    // Stay competitive but don't go below cost
    return Math.max(Math.min(priceDiff * 0.5, 0.2), -0.15);
  }

  /**
   * Combine all pricing factors
   */
  private combinePricingFactors(basePrice: any, adjustments: any, request: PricingRequest): PricingResponse {
    const surgeMultiplier = adjustments.surge || 1.0;
    const demandAdjustment = adjustments.demand || 0;
    const weatherAdjustment = adjustments.weather || 0;
    const trafficAdjustment = adjustments.traffic || 0;
    
    // Apply surge pricing
    const surgeAmount = (basePrice.subtotal * (surgeMultiplier - 1));
    
    // Apply other adjustments
    const demandAmount = basePrice.subtotal * demandAdjustment;
    const weatherAmount = basePrice.subtotal * weatherAdjustment;
    const trafficAmount = basePrice.subtotal * trafficAdjustment;
    
    const finalAmount = basePrice.total + surgeAmount + demandAmount + weatherAmount + trafficAmount;
    
    return {
      baseFare: basePrice.baseFare,
      distanceFare: basePrice.distanceFare,
      timeFare: basePrice.timeFare,
      surgePricing: {
        multiplier: surgeMultiplier,
        reason: this.getSurgeReason(surgeMultiplier),
        isActive: surgeMultiplier > 1.0,
      },
      demandPricing: {
        adjustment: demandAdjustment,
        demandLevel: this.getDemandLevelFromAdjustment(demandAdjustment),
      },
      weatherPricing: {
        adjustment: weatherAdjustment,
        condition: 'clear', // Would come from weather data
      },
      finalAmount: Math.round(finalAmount * 100) / 100, // Round to 2 decimal places
      breakdown: {
        base: basePrice.baseFare,
        distance: basePrice.distanceFare,
        time: basePrice.timeFare,
        surge: surgeAmount,
        demand: demandAmount,
        weather: weatherAmount,
        taxes: basePrice.taxes,
        platformFee: basePrice.platformFee,
      },
      confidence: adjustments.confidence || 0.8,
      validUntil: new Date(Date.now() + this.cacheExpiry),
    };
  }

  /**
   * Apply pricing constraints and business rules
   */
  private applyPricingConstraints(pricing: PricingResponse, request: PricingRequest): PricingResponse {
    // Minimum fare constraint
    const minimumFare = 50; // INR
    if (pricing.finalAmount < minimumFare) {
      pricing.finalAmount = minimumFare;
    }
    
    // Maximum surge constraint
    const maxSurgeMultiplier = 3.0;
    if (pricing.surgePricing.multiplier > maxSurgeMultiplier) {
      pricing.surgePricing.multiplier = maxSurgeMultiplier;
      // Recalculate final amount
      const baseAmount = pricing.baseFare + pricing.distanceFare + pricing.timeFare;
      const surgeAmount = baseAmount * (maxSurgeMultiplier - 1);
      pricing.finalAmount = baseAmount + surgeAmount + pricing.breakdown.taxes + pricing.breakdown.platformFee;
    }
    
    // Round to nearest rupee
    pricing.finalAmount = Math.round(pricing.finalAmount);
    
    return pricing;
  }

  /**
   * Generate alternative pricing options
   */
  private async generatePricingAlternatives(
    request: PricingRequest, 
    marketConditions: MarketConditions
  ): Promise<PricingResponse[]> {
    const alternatives: PricingResponse[] = [];
    
    // Shared ride option (if not already shared)
    if (request.rideType !== 'shared') {
      const sharedRequest = { ...request, rideType: 'shared' as const };
      const sharedPricing = await this.calculatePrice(sharedRequest);
      alternatives.push(sharedPricing);
    }
    
    // Premium option (if not already premium)
    if (request.rideType !== 'premium') {
      const premiumRequest = { ...request, rideType: 'premium' as const };
      const premiumPricing = await this.calculatePrice(premiumRequest);
      alternatives.push(premiumPricing);
    }
    
    return alternatives;
  }

  /**
   * Utility methods
   */
  private getRideTypeMultiplier(rideType: string): number {
    const multipliers = {
      standard: 1.0,
      premium: 1.5,
      shared: 0.7,
    };
    return multipliers[rideType] || 1.0;
  }

  private isRushHour(time: Date): boolean {
    const hour = time.getHours();
    return (hour >= 7 && hour <= 10) || (hour >= 17 && hour <= 20);
  }

  private getDemandLevel(demand: DemandData): number {
    return demand.currentDemand / demand.historicalAverage;
  }

  private getSupplyLevel(supply: SupplyData): number {
    return supply.availableDrivers / 100; // Normalize to expected supply
  }

  private getSurgeReason(multiplier: number): string {
    if (multiplier >= 2.5) return 'Very high demand';
    if (multiplier >= 2.0) return 'High demand';
    if (multiplier >= 1.5) return 'Increased demand';
    if (multiplier >= 1.2) return 'Moderate demand';
    return 'Normal pricing';
  }

  private getDemandLevelFromAdjustment(adjustment: number): 'low' | 'medium' | 'high' | 'peak' {
    if (adjustment >= 0.25) return 'peak';
    if (adjustment >= 0.15) return 'high';
    if (adjustment >= 0.05) return 'medium';
    return 'low';
  }

  private generateCacheKey(request: PricingRequest): string {
    const key = `${request.pickup.latitude},${request.pickup.longitude}-${request.dropoff.latitude},${request.dropoff.longitude}-${request.rideType}-${Math.floor(Date.now() / 60000)}`;
    return Buffer.from(key).toString('base64');
  }

  private getCachedPrice(key: string): PricingResponse | null {
    const cached = this.cache.get(key);
    if (cached && cached.validUntil > new Date()) {
      return cached;
    }
    return null;
  }

  private cachePrice(key: string, pricing: PricingResponse): void {
    this.cache.set(key, pricing);
    
    // Clean up old cache entries
    if (this.cache.size > 500) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  private async getMarketConditions(request: PricingRequest): Promise<MarketConditions> {
    // This would integrate with real data sources
    return {
      demand: {
        currentDemand: 150,
        historicalAverage: 100,
        trendDirection: 'increasing',
        peakHours: [8, 9, 18, 19],
        seasonalFactor: 1.1,
      },
      supply: {
        availableDrivers: 80,
        averageResponseTime: 5,
        utilizationRate: 0.7,
        driverDensity: 0.8,
        supplyTrend: 'stable',
      },
      competition: {
        averagePrice: 95,
        priceRange: { min: 80, max: 120 },
        marketShare: 0.35,
      },
      externalFactors: {
        weather: {
          condition: 'clear',
          severity: 0,
          impact: 0,
        },
        traffic: {
          congestionLevel: 0.6,
          incidents: 2,
          impact: 0.1,
        },
        events: [],
      },
    };
  }

  private getFallbackPricing(request: PricingRequest): PricingResponse {
    const basePrice = this.calculateBasePrice(request);
    
    return {
      baseFare: basePrice.baseFare,
      distanceFare: basePrice.distanceFare,
      timeFare: basePrice.timeFare,
      surgePricing: {
        multiplier: 1.0,
        reason: 'Normal pricing',
        isActive: false,
      },
      demandPricing: {
        adjustment: 0,
        demandLevel: 'medium',
      },
      weatherPricing: {
        adjustment: 0,
        condition: 'clear',
      },
      finalAmount: basePrice.total,
      breakdown: {
        base: basePrice.baseFare,
        distance: basePrice.distanceFare,
        time: basePrice.timeFare,
        surge: 0,
        demand: 0,
        weather: 0,
        taxes: basePrice.taxes,
        platformFee: basePrice.platformFee,
      },
      confidence: 0.6,
      validUntil: new Date(Date.now() + this.cacheExpiry),
    };
  }
}

// Export singleton instance
export const dynamicPricingEngine = new DynamicPricingEngine();
export default dynamicPricingEngine;
