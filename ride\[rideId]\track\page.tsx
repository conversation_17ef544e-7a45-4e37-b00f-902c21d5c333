'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/lib/contexts/AuthContext';
import { useWebSocket } from '@/lib/hooks/useWebSocket';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import LocationTracker from '@/components/tracking/LocationTracker';
import RideChat from '@/components/chat/RideChat';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin, 
  Navigation, 
  Clock, 
  Phone, 
  AlertTriangle,
  Car,
  User,
  MessageCircle,
  Target
} from 'lucide-react';

interface RideData {
  _id: string;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled';
  pickupLocation: {
    address: string;
    coordinates: { latitude: number; longitude: number };
    landmark?: string;
  };
  dropoffLocation: {
    address: string;
    coordinates: { latitude: number; longitude: number };
    landmark?: string;
  };
  estimatedDistance: number;
  estimatedDuration: number;
  finalAmount: number;
  rideType: string;
  paymentMethod: string;
  rider: {
    _id: string;
    firstName: string;
    lastName: string;
    phone: string;
    profileImage?: string;
  };
  driver?: {
    _id: string;
    firstName: string;
    lastName: string;
    phone: string;
    profileImage?: string;
    driverProfile: {
      rating: number;
      vehicleModel?: string;
      vehicleColor?: string;
    };
  };
  requestedAt: string;
  acceptedAt?: string;
  startedAt?: string;
  completedAt?: string;
}

export default function RideTrackingPage() {
  const params = useParams();
  const rideId = params.rideId as string;
  const { user, token } = useAuth();
  const { 
    joinRide, 
    leaveRide, 
    updateRideStatus, 
    sendEmergencyAlert,
    isConnected 
  } = useWebSocket();
  
  const [rideData, setRideData] = useState<RideData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentLocation, setCurrentLocation] = useState<GeolocationPosition | null>(null);

  // Fetch ride data
  useEffect(() => {
    const fetchRideData = async () => {
      try {
        const response = await fetch(`/api/rides/${rideId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setRideData(data.ride);
        } else {
          setError('Failed to load ride data');
        }
      } catch (error) {
        setError('Network error while loading ride');
      } finally {
        setLoading(false);
      }
    };

    if (rideId && token) {
      fetchRideData();
    }
  }, [rideId, token]);

  // Join ride room for real-time updates
  useEffect(() => {
    if (rideId) {
      joinRide(rideId);
      return () => leaveRide(rideId);
    }
  }, [rideId]);

  const handleStatusUpdate = (status: string) => {
    if (rideId) {
      updateRideStatus(rideId, status, currentLocation ? {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        accuracy: currentLocation.coords.accuracy,
        timestamp: currentLocation.timestamp
      } : undefined);
    }
  };

  const handleEmergencyAlert = () => {
    if (rideId && currentLocation) {
      sendEmergencyAlert(rideId, {
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        accuracy: currentLocation.coords.accuracy,
        timestamp: currentLocation.timestamp
      }, 'Emergency assistance requested');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'accepted': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canUpdateStatus = (currentStatus: string, newStatus: string) => {
    const validTransitions: { [key: string]: string[] } = {
      'accepted': ['in_progress', 'cancelled'],
      'in_progress': ['completed', 'cancelled'],
    };
    return validTransitions[currentStatus]?.includes(newStatus) || false;
  };

  const getOtherUser = () => {
    if (!rideData || !user) return null;
    
    if (user.role === 'rider') {
      return rideData.driver ? {
        _id: rideData.driver._id,
        firstName: rideData.driver.firstName,
        lastName: rideData.driver.lastName,
        role: 'driver' as const,
        profileImage: rideData.driver.profileImage,
        phone: rideData.driver.phone,
      } : null;
    } else {
      return {
        _id: rideData.rider._id,
        firstName: rideData.rider.firstName,
        lastName: rideData.rider.lastName,
        role: 'rider' as const,
        profileImage: rideData.rider.profileImage,
        phone: rideData.rider.phone,
      };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !rideData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error || 'Ride not found'}</AlertDescription>
        </Alert>
      </div>
    );
  }

  const otherUser = getOtherUser();

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Track Ride</h1>
                  <p className="mt-2 text-gray-600">Real-time ride tracking and communication</p>
                </div>
                <Badge className={getStatusColor(rideData.status)}>
                  {rideData.status.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Ride Info & Controls */}
              <div className="space-y-6">
                {/* Ride Details */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Navigation className="w-5 h-5 mr-2" />
                      Ride Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-start space-x-3">
                        <MapPin className="w-4 h-4 text-green-500 mt-1" />
                        <div>
                          <p className="font-medium">Pickup</p>
                          <p className="text-sm text-gray-600">{rideData.pickupLocation.address}</p>
                          {rideData.pickupLocation.landmark && (
                            <p className="text-xs text-gray-500">{rideData.pickupLocation.landmark}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-start space-x-3">
                        <MapPin className="w-4 h-4 text-red-500 mt-1" />
                        <div>
                          <p className="font-medium">Destination</p>
                          <p className="text-sm text-gray-600">{rideData.dropoffLocation.address}</p>
                          {rideData.dropoffLocation.landmark && (
                            <p className="text-xs text-gray-500">{rideData.dropoffLocation.landmark}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Distance</p>
                        <p className="font-medium">{rideData.estimatedDistance} km</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Duration</p>
                        <p className="font-medium">~{rideData.estimatedDuration} min</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Fare</p>
                        <p className="font-medium">₹{rideData.finalAmount}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Driver/Rider Info */}
                {otherUser && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        {user?.role === 'rider' ? <Car className="w-5 h-5 mr-2" /> : <User className="w-5 h-5 mr-2" />}
                        {user?.role === 'rider' ? 'Your Driver' : 'Your Rider'}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          {otherUser.firstName.charAt(0)}{otherUser.lastName.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <p className="font-medium">{otherUser.firstName} {otherUser.lastName}</p>
                          {rideData.driver && (
                            <div className="text-sm text-gray-600">
                              ⭐ {rideData.driver.driverProfile.rating} • {rideData.driver.driverProfile.vehicleModel}
                            </div>
                          )}
                        </div>
                        <Button variant="outline" size="sm">
                          <Phone className="w-4 h-4 mr-1" />
                          Call
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Action Buttons */}
                <Card>
                  <CardHeader>
                    <CardTitle>Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {user?.role === 'driver' && canUpdateStatus(rideData.status, 'in_progress') && (
                      <Button 
                        onClick={() => handleStatusUpdate('in_progress')}
                        className="w-full"
                      >
                        Start Ride
                      </Button>
                    )}

                    {user?.role === 'driver' && canUpdateStatus(rideData.status, 'completed') && (
                      <Button 
                        onClick={() => handleStatusUpdate('completed')}
                        className="w-full"
                      >
                        Complete Ride
                      </Button>
                    )}

                    {canUpdateStatus(rideData.status, 'cancelled') && (
                      <Button 
                        variant="outline"
                        onClick={() => handleStatusUpdate('cancelled')}
                        className="w-full"
                      >
                        Cancel Ride
                      </Button>
                    )}

                    <Button 
                      variant="destructive"
                      onClick={handleEmergencyAlert}
                      className="w-full"
                    >
                      <AlertTriangle className="w-4 h-4 mr-2" />
                      Emergency Alert
                    </Button>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Tracking & Chat */}
              <div className="space-y-6">
                <Tabs defaultValue="tracking" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="tracking">
                      <Target className="w-4 h-4 mr-2" />
                      Tracking
                    </TabsTrigger>
                    <TabsTrigger value="chat">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Chat
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="tracking" className="mt-6">
                    <LocationTracker
                      rideId={rideId}
                      isActive={rideData.status === 'in_progress'}
                      onLocationUpdate={setCurrentLocation}
                    />
                  </TabsContent>

                  <TabsContent value="chat" className="mt-6">
                    {otherUser ? (
                      <RideChat
                        rideId={rideId}
                        otherUser={otherUser}
                        onEmergencyAlert={handleEmergencyAlert}
                      />
                    ) : (
                      <Card>
                        <CardContent className="text-center py-8">
                          <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500">Chat will be available once a driver is assigned</p>
                        </CardContent>
                      </Card>
                    )}
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
