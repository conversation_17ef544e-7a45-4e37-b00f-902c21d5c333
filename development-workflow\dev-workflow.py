#!/usr/bin/env python3
"""
Development Workflow Automation for AI-Powered Two-Wheeler Sharing Platform
Handles feature flags, refactoring, PR creation, and development tasks
"""

import json
import os
import subprocess
import argparse
import requests
from datetime import datetime
from typing import Dict, List, Optional
import yaml

class DevelopmentWorkflow:
    def __init__(self, config_path: str = "development-workflow/feature-flags-config.json"):
        self.config_path = config_path
        self.github_token = os.getenv('GITHUB_TOKEN')
        self.linear_token = os.getenv('LINEAR_TOKEN')
        self.supabase_url = os.getenv('SUPABASE_URL')
        self.supabase_key = os.getenv('SUPABASE_ANON_KEY')
        
    def load_feature_flags(self) -> Dict:
        """Load feature flags configuration"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Feature flags config not found at {self.config_path}")
            return {}
    
    def update_feature_flag(self, flag_name: str, enabled: bool, environment: str = "all") -> bool:
        """Update feature flag configuration"""
        config = self.load_feature_flags()
        
        if flag_name not in config.get('feature_flags', {}):
            print(f"Feature flag '{flag_name}' not found")
            return False
        
        if environment == "all":
            for env in config['feature_flags'][flag_name]['environments']:
                config['feature_flags'][flag_name]['environments'][env] = enabled
        else:
            if environment in config['feature_flags'][flag_name]['environments']:
                config['feature_flags'][flag_name]['environments'][environment] = enabled
            else:
                print(f"Environment '{environment}' not found for flag '{flag_name}'")
                return False
        
        config['feature_flags'][flag_name]['enabled'] = enabled
        config['metadata']['last_updated'] = datetime.now().isoformat() + 'Z'
        
        # Save updated configuration
        with open(self.config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"✅ Feature flag '{flag_name}' updated to {enabled} for {environment}")
        return True
    
    def create_feature_flag_pr(self, flag_changes: List[Dict]) -> str:
        """Create a pull request for feature flag changes"""
        branch_name = f"feature-flags/update-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        
        # Create new branch
        subprocess.run(['git', 'checkout', '-b', branch_name], check=True)
        
        # Stage and commit changes
        subprocess.run(['git', 'add', self.config_path], check=True)
        
        commit_message = "feat: Update feature flags configuration\n\n"
        for change in flag_changes:
            commit_message += f"- {change['flag']}: {change['old_value']} → {change['new_value']}\n"
        
        subprocess.run(['git', 'commit', '-m', commit_message], check=True)
        
        # Push branch
        subprocess.run(['git', 'push', 'origin', branch_name], check=True)
        
        # Create PR via GitHub API
        if self.github_token:
            pr_data = {
                "title": "Update Feature Flags Configuration",
                "body": self._generate_feature_flag_pr_description(flag_changes),
                "head": branch_name,
                "base": "main"
            }
            
            response = requests.post(
                f"https://api.github.com/repos/{self._get_repo_name()}/pulls",
                headers={"Authorization": f"token {self.github_token}"},
                json=pr_data
            )
            
            if response.status_code == 201:
                pr_url = response.json()['html_url']
                print(f"✅ Pull request created: {pr_url}")
                return pr_url
            else:
                print(f"❌ Failed to create PR: {response.text}")
        
        return f"Branch created: {branch_name}"
    
    def refactor_code(self, source_file: str, target_file: str, functions: List[str]) -> bool:
        """Move functions between files while maintaining conventions"""
        try:
            # Read source file
            with open(source_file, 'r') as f:
                source_content = f.read()
            
            # Extract functions (simplified - would need proper AST parsing)
            extracted_functions = []
            remaining_content = source_content
            
            for func_name in functions:
                # This is a simplified extraction - in practice, use AST
                func_start = source_content.find(f"def {func_name}(")
                if func_start != -1:
                    # Find function end (simplified)
                    lines = source_content[func_start:].split('\n')
                    func_lines = [lines[0]]
                    indent_level = len(lines[0]) - len(lines[0].lstrip())
                    
                    for i, line in enumerate(lines[1:], 1):
                        if line.strip() and (len(line) - len(line.lstrip())) <= indent_level and not line.startswith(' '):
                            break
                        func_lines.append(line)
                    
                    extracted_functions.append('\n'.join(func_lines))
                    remaining_content = remaining_content.replace('\n'.join(func_lines), '')
            
            # Update source file
            with open(source_file, 'w') as f:
                f.write(remaining_content)
            
            # Update target file
            if os.path.exists(target_file):
                with open(target_file, 'r') as f:
                    target_content = f.read()
            else:
                target_content = ""
            
            # Add extracted functions to target file
            with open(target_file, 'w') as f:
                f.write(target_content + '\n\n' + '\n\n'.join(extracted_functions))
            
            print(f"✅ Moved {len(functions)} functions from {source_file} to {target_file}")
            return True
            
        except Exception as e:
            print(f"❌ Refactoring failed: {e}")
            return False
    
    def create_feature_pr_from_issue(self, issue_number: int, feature_description: str) -> str:
        """Create a new feature PR from GitHub issue"""
        branch_name = f"feature/issue-{issue_number}-{datetime.now().strftime('%Y%m%d')}"
        
        # Create new branch
        subprocess.run(['git', 'checkout', '-b', branch_name], check=True)
        
        # Create initial feature implementation structure
        feature_dir = f"features/issue-{issue_number}"
        os.makedirs(feature_dir, exist_ok=True)
        
        # Create initial files
        with open(f"{feature_dir}/README.md", 'w') as f:
            f.write(f"# Feature Implementation - Issue #{issue_number}\n\n{feature_description}\n")
        
        with open(f"{feature_dir}/implementation.py", 'w') as f:
            f.write(f'"""\nFeature implementation for issue #{issue_number}\n{feature_description}\n"""\n\n# TODO: Implement feature\npass\n')
        
        with open(f"{feature_dir}/tests.py", 'w') as f:
            f.write(f'"""\nTests for feature implementation - issue #{issue_number}\n"""\n\nimport unittest\n\nclass TestFeature(unittest.TestCase):\n    def test_placeholder(self):\n        # TODO: Implement tests\n        pass\n')
        
        # Commit initial structure
        subprocess.run(['git', 'add', feature_dir], check=True)
        subprocess.run(['git', 'commit', '-m', f'feat: Initial implementation for issue #{issue_number}'], check=True)
        subprocess.run(['git', 'push', 'origin', branch_name], check=True)
        
        # Create draft PR
        if self.github_token:
            pr_data = {
                "title": f"[DRAFT] Feature implementation for issue #{issue_number}",
                "body": f"Implements feature described in issue #{issue_number}\n\n{feature_description}\n\n- [ ] Implementation complete\n- [ ] Tests added\n- [ ] Documentation updated",
                "head": branch_name,
                "base": "main",
                "draft": True
            }
            
            response = requests.post(
                f"https://api.github.com/repos/{self._get_repo_name()}/pulls",
                headers={"Authorization": f"token {self.github_token}"},
                json=pr_data
            )
            
            if response.status_code == 201:
                pr_url = response.json()['html_url']
                print(f"✅ Draft PR created: {pr_url}")
                return pr_url
        
        return f"Branch created: {branch_name}"
    
    def query_supabase_table(self, table_name: str, limit: int = 10) -> Dict:
        """Query Supabase table contents"""
        if not self.supabase_url or not self.supabase_key:
            print("❌ Supabase credentials not configured")
            return {}
        
        try:
            response = requests.get(
                f"{self.supabase_url}/rest/v1/{table_name}?limit={limit}",
                headers={
                    "apikey": self.supabase_key,
                    "Authorization": f"Bearer {self.supabase_key}"
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Retrieved {len(data)} records from {table_name}")
                return data
            else:
                print(f"❌ Failed to query table: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ Supabase query failed: {e}")
            return {}
    
    def create_linear_ticket(self, title: str, description: str, team_id: str) -> str:
        """Create a Linear ticket"""
        if not self.linear_token:
            print("❌ Linear token not configured")
            return ""
        
        query = """
        mutation IssueCreate($input: IssueCreateInput!) {
            issueCreate(input: $input) {
                success
                issue {
                    id
                    identifier
                    url
                }
            }
        }
        """
        
        variables = {
            "input": {
                "title": title,
                "description": description,
                "teamId": team_id
            }
        }
        
        try:
            response = requests.post(
                "https://api.linear.app/graphql",
                headers={"Authorization": f"Bearer {self.linear_token}"},
                json={"query": query, "variables": variables}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data['data']['issueCreate']['success']:
                    issue = data['data']['issueCreate']['issue']
                    print(f"✅ Linear ticket created: {issue['identifier']} - {issue['url']}")
                    return issue['url']
            
            print(f"❌ Failed to create Linear ticket: {response.text}")
            return ""
            
        except Exception as e:
            print(f"❌ Linear ticket creation failed: {e}")
            return ""
    
    def generate_test_coverage(self, file_path: str) -> str:
        """Generate unit tests for a file"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Extract function names (simplified)
            import re
            functions = re.findall(r'def (\w+)\(', content)
            
            test_file_path = file_path.replace('.py', '_test.py')
            
            test_content = f'''"""
Unit tests for {file_path}
Generated automatically by development workflow
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from {os.path.basename(file_path).replace('.py', '')} import *

class Test{os.path.basename(file_path).replace('.py', '').title()}(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        pass
    
    def tearDown(self):
        """Clean up after each test method."""
        pass

'''
            
            for func in functions:
                if not func.startswith('_'):  # Skip private functions
                    test_content += f'''
    def test_{func}(self):
        """Test {func} function."""
        # TODO: Implement test for {func}
        self.assertTrue(True)  # Placeholder
'''
            
            test_content += '''

if __name__ == '__main__':
    unittest.main()
'''
            
            with open(test_file_path, 'w') as f:
                f.write(test_content)
            
            print(f"✅ Test file generated: {test_file_path}")
            return test_file_path
            
        except Exception as e:
            print(f"❌ Test generation failed: {e}")
            return ""
    
    def _generate_feature_flag_pr_description(self, flag_changes: List[Dict]) -> str:
        """Generate PR description for feature flag changes"""
        description = "## Feature Flag Configuration Update\n\n"
        description += "This PR updates feature flag configurations as follows:\n\n"
        
        for change in flag_changes:
            description += f"- **{change['flag']}**: `{change['old_value']}` → `{change['new_value']}`\n"
        
        description += "\n### Testing\n"
        description += "- [ ] Feature flags tested in development environment\n"
        description += "- [ ] Rollout plan documented\n"
        description += "- [ ] Rollback plan prepared\n"
        
        return description
    
    def _get_repo_name(self) -> str:
        """Get repository name from git remote"""
        try:
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'], 
                                  capture_output=True, text=True, check=True)
            url = result.stdout.strip()
            # Extract repo name from URL
            if 'github.com' in url:
                return url.split('github.com/')[-1].replace('.git', '')
            return "owner/repo"  # Fallback
        except:
            return "owner/repo"  # Fallback

def main():
    parser = argparse.ArgumentParser(description='Development Workflow Automation')
    parser.add_argument('command', choices=[
        'update-flag', 'create-flag-pr', 'refactor', 'create-feature-pr',
        'query-db', 'create-ticket', 'generate-tests'
    ])
    parser.add_argument('--flag', help='Feature flag name')
    parser.add_argument('--enabled', type=bool, help='Enable/disable flag')
    parser.add_argument('--environment', default='all', help='Target environment')
    parser.add_argument('--source', help='Source file for refactoring')
    parser.add_argument('--target', help='Target file for refactoring')
    parser.add_argument('--functions', nargs='+', help='Functions to move')
    parser.add_argument('--issue', type=int, help='GitHub issue number')
    parser.add_argument('--description', help='Feature description')
    parser.add_argument('--table', help='Supabase table name')
    parser.add_argument('--title', help='Ticket title')
    parser.add_argument('--team-id', help='Linear team ID')
    parser.add_argument('--file', help='File path for test generation')
    
    args = parser.parse_args()
    
    workflow = DevelopmentWorkflow()
    
    if args.command == 'update-flag':
        workflow.update_feature_flag(args.flag, args.enabled, args.environment)
    elif args.command == 'create-flag-pr':
        # This would need flag changes data
        workflow.create_feature_flag_pr([])
    elif args.command == 'refactor':
        workflow.refactor_code(args.source, args.target, args.functions)
    elif args.command == 'create-feature-pr':
        workflow.create_feature_pr_from_issue(args.issue, args.description)
    elif args.command == 'query-db':
        data = workflow.query_supabase_table(args.table)
        print(json.dumps(data, indent=2))
    elif args.command == 'create-ticket':
        workflow.create_linear_ticket(args.title, args.description, args.team_id)
    elif args.command == 'generate-tests':
        workflow.generate_test_coverage(args.file)

if __name__ == '__main__':
    main()
