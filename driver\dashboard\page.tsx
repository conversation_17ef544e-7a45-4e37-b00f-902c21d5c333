import ProtectedRoute from '@/components/auth/ProtectedRoute';
import UserProfile from '@/components/dashboard/UserProfile';
import RideManagement from '@/components/rides/RideManagement';
import DriverRequests from '@/components/rides/DriverRequests';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function DriverDashboardPage() {
  return (
    <ProtectedRoute requiredRole="driver">
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-6">
              <h1 className="text-3xl font-bold text-gray-900">Driver Dashboard</h1>
              <p className="mt-2 text-gray-600">Manage your rides and earnings</p>
            </div>
          </div>
        </div>
        <div className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <Tabs defaultValue="requests" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="requests">Ride Requests</TabsTrigger>
                <TabsTrigger value="rides">My Rides</TabsTrigger>
                <TabsTrigger value="profile">Profile</TabsTrigger>
              </TabsList>
              <TabsContent value="requests" className="mt-6">
                <DriverRequests />
              </TabsContent>
              <TabsContent value="rides" className="mt-6">
                <RideManagement />
              </TabsContent>
              <TabsContent value="profile" className="mt-6">
                <UserProfile />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export const metadata = {
  title: 'Driver Dashboard - Two Wheeler Sharing',
  description: 'Your driver dashboard',
};
