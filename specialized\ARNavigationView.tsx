'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Navigation, 
  RotateCcw, 
  Volume2, 
  VolumeX,
  Zap,
  AlertTriangle,
  MapPin,
  Clock,
  Compass,
  Camera,
  Settings,
  Maximize,
  Minimize
} from 'lucide-react';

interface ARNavigationViewProps {
  destination: string;
  currentSpeed: number;
  isARMode: boolean;
}

export default function ARNavigationView({ 
  destination, 
  currentSpeed, 
  isARMode 
}: ARNavigationViewProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentInstruction, setCurrentInstruction] = useState('Turn right in 200m');
  const [nextInstruction, setNextInstruction] = useState('Continue straight for 1.2km');
  const [eta, setEta] = useState('8 min');
  const [distance, setDistance] = useState('2.3 km');

  const navigationData = {
    currentStreet: 'MG Road',
    nextStreet: 'Brigade Road',
    speedLimit: 40,
    hazards: [
      { type: 'construction', distance: 500, severity: 'medium' },
      { type: 'traffic', distance: 800, severity: 'high' }
    ],
    landmarks: [
      { name: 'Metro Station', distance: 150, side: 'right' },
      { name: 'Shopping Mall', distance: 300, side: 'left' }
    ]
  };

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50' : 'max-w-md mx-auto'} bg-black text-white`}>
      {/* AR Camera View Background */}
      <div className="relative h-screen bg-gradient-to-b from-blue-900 via-blue-800 to-blue-900">
        {/* Simulated AR Camera Feed */}
        <div className="absolute inset-0 opacity-30">
          <div className="h-full bg-gradient-to-b from-gray-800 to-gray-900"></div>
        </div>

        {/* AR Overlay Elements */}
        <div className="absolute inset-0">
          {/* Speed and Status Bar */}
          <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
            <Card className="bg-black/70 border-white/20">
              <CardContent className="p-2">
                <div className="text-center">
                  <div className="text-2xl font-bold">{currentSpeed}</div>
                  <div className="text-xs opacity-75">km/h</div>
                </div>
              </CardContent>
            </Card>

            <div className="flex space-x-2">
              <Button 
                size="sm" 
                variant="ghost" 
                className="bg-black/70 text-white border-white/20"
                onClick={() => setIsMuted(!isMuted)}
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                className="bg-black/70 text-white border-white/20"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* AR Direction Arrow */}
          <div className="absolute top-1/3 left-1/2 transform -translate-x-1/2">
            <div className="relative">
              {/* Main Arrow */}
              <div className="w-16 h-16 bg-blue-500/80 rounded-full flex items-center justify-center border-4 border-white/50 shadow-lg">
                <Navigation className="h-8 w-8 text-white transform rotate-90" />
              </div>
              
              {/* Distance Indicator */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-blue-500/90 text-white border-white/30">
                  200m
                </Badge>
              </div>
            </div>
          </div>

          {/* AR Lane Guidance */}
          <div className="absolute bottom-1/3 left-1/2 transform -translate-x-1/2">
            <div className="flex space-x-2">
              {[1, 2, 3].map((lane, index) => (
                <div 
                  key={lane}
                  className={`w-12 h-20 border-2 rounded ${
                    index === 1 
                      ? 'border-green-400 bg-green-400/20' 
                      : 'border-white/30 bg-white/10'
                  }`}
                >
                  {index === 1 && (
                    <div className="h-full flex items-center justify-center">
                      <Navigation className="h-6 w-6 text-green-400 transform rotate-90" />
                    </div>
                  )}
                </div>
              ))}
            </div>
            <div className="text-center mt-2">
              <Badge className="bg-green-500/90 text-white">
                Keep Right Lane
              </Badge>
            </div>
          </div>

          {/* Hazard Warnings */}
          {navigationData.hazards.map((hazard, index) => (
            <div 
              key={index}
              className="absolute top-1/2 right-4 transform -translate-y-1/2"
              style={{ top: `${40 + index * 15}%` }}
            >
              <Card className="bg-red-500/80 border-red-300/50">
                <CardContent className="p-2">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-white" />
                    <div className="text-xs">
                      <div className="font-medium capitalize">{hazard.type}</div>
                      <div className="opacity-75">{hazard.distance}m ahead</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}

          {/* Landmark Indicators */}
          {navigationData.landmarks.map((landmark, index) => (
            <div 
              key={index}
              className={`absolute top-1/4 ${landmark.side === 'right' ? 'right-4' : 'left-4'}`}
              style={{ top: `${25 + index * 10}%` }}
            >
              <Card className="bg-blue-500/70 border-blue-300/50">
                <CardContent className="p-2">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-white" />
                    <div className="text-xs">
                      <div className="font-medium">{landmark.name}</div>
                      <div className="opacity-75">{landmark.distance}m</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}

          {/* Bottom Navigation Panel */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-4">
            {/* Current Instruction */}
            <Card className="mb-4 bg-black/70 border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                    <Navigation className="h-5 w-5 text-white transform rotate-90" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-lg">{currentInstruction}</div>
                    <div className="text-sm opacity-75">Then {nextInstruction}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Trip Info */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <Card className="bg-black/70 border-white/20">
                <CardContent className="p-3 text-center">
                  <Clock className="h-4 w-4 mx-auto mb-1 text-green-400" />
                  <div className="text-sm font-medium">{eta}</div>
                  <div className="text-xs opacity-75">ETA</div>
                </CardContent>
              </Card>
              
              <Card className="bg-black/70 border-white/20">
                <CardContent className="p-3 text-center">
                  <Navigation className="h-4 w-4 mx-auto mb-1 text-blue-400" />
                  <div className="text-sm font-medium">{distance}</div>
                  <div className="text-xs opacity-75">Remaining</div>
                </CardContent>
              </Card>
              
              <Card className="bg-black/70 border-white/20">
                <CardContent className="p-3 text-center">
                  <Zap className="h-4 w-4 mx-auto mb-1 text-yellow-400" />
                  <div className="text-sm font-medium">{navigationData.speedLimit}</div>
                  <div className="text-xs opacity-75">Speed Limit</div>
                </CardContent>
              </Card>
            </div>

            {/* Control Buttons */}
            <div className="flex justify-between items-center">
              <Button 
                variant="ghost" 
                size="sm"
                className="bg-black/70 text-white border-white/20"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>

              <div className="flex space-x-2">
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="bg-black/70 text-white border-white/20"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
                
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="bg-black/70 text-white border-white/20"
                >
                  <Camera className="h-4 w-4" />
                </Button>
              </div>

              <Button 
                variant="destructive" 
                size="sm"
                className="bg-red-600/80"
              >
                End Navigation
              </Button>
            </div>
          </div>

          {/* Compass */}
          <div className="absolute top-20 right-4">
            <Card className="bg-black/70 border-white/20">
              <CardContent className="p-2">
                <div className="relative w-12 h-12">
                  <Compass className="h-12 w-12 text-white" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-xs font-bold">N</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Street Names */}
          <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2">
            <div className="text-center space-y-2">
              <Badge className="bg-black/70 text-white border-white/30 text-lg px-4 py-2">
                {navigationData.currentStreet}
              </Badge>
              <div className="text-sm opacity-75">
                → {navigationData.nextStreet}
              </div>
            </div>
          </div>

          {/* Speed Warning */}
          {currentSpeed > navigationData.speedLimit && (
            <div className="absolute top-16 left-1/2 transform -translate-x-1/2">
              <Card className="bg-red-500/90 border-red-300/50 animate-pulse">
                <CardContent className="p-2">
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="h-4 w-4 text-white" />
                    <span className="text-sm font-medium">Speed Limit Exceeded</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* AR Mode Toggle */}
        {!isARMode && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <Card className="bg-white text-black">
              <CardContent className="p-6 text-center">
                <Camera className="h-12 w-12 mx-auto mb-4 text-blue-600" />
                <h3 className="text-lg font-semibold mb-2">Enable AR Navigation</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Point your camera at the road for enhanced navigation experience
                </p>
                <Button className="w-full">
                  Enable AR Mode
                </Button>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
