import { NextRequest, NextResponse } from 'next/server'

// Mock database - replace with actual MongoDB connection
let rides: any[] = []

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const driverId = searchParams.get('driverId')
  const status = searchParams.get('status')

  try {
    let filteredRides = rides

    if (userId) {
      filteredRides = filteredRides.filter(ride => ride.userId === userId)
    }

    if (driverId) {
      filteredRides = filteredRides.filter(ride => ride.driverId === driverId)
    }

    if (status) {
      filteredRides = filteredRides.filter(ride => ride.status === status)
    }

    return NextResponse.json({
      success: true,
      rides: filteredRides,
      total: filteredRides.length
    })
  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Failed to fetch rides' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const rideData = await request.json()
    
    // Trigger n8n workflow for ride matching
    const workflowResponse = await fetch('http://localhost:5678/webhook/ride-request', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        rideRequest: {
          userId: rideData.userId,
          pickup: rideData.pickup,
          destination: rideData.destination,
          distance: rideData.distance,
          requestedTime: rideData.requestedTime,
          userRating: rideData.userRating || 4.5,
          specialRequirements: rideData.specialRequirements,
          demand: rideData.demand || 'medium',
          weather: rideData.weather,
          timeOfDay: rideData.timeOfDay
        },
        availableDrivers: await getAvailableDrivers(rideData.pickup)
      })
    })

    if (!workflowResponse.ok) {
      throw new Error('Workflow execution failed')
    }

    const workflowResult = await workflowResponse.json()

    // Create ride record
    const newRide = {
      id: `ride_${Date.now()}`,
      ...rideData,
      ...workflowResult,
      status: 'assigned',
      createdAt: new Date().toISOString(),
      estimatedPickupTime: workflowResult.estimatedPickupTime || 15
    }

    rides.push(newRide)

    return NextResponse.json({
      success: true,
      ride: newRide,
      message: 'Ride request processed successfully'
    })
  } catch (error) {
    console.error('Ride creation error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create ride request' },
      { status: 500 }
    )
  }
}

// Helper function to get available drivers
async function getAvailableDrivers(pickupLocation: string) {
  // Mock available drivers - replace with actual database query
  return [
    {
      id: 'driver_1',
      name: 'Rahul S.',
      currentLocation: 'Andheri East',
      distanceFromPickup: 2.5,
      rating: 4.8,
      vehicleType: 'Motorcycle',
      earningsToday: 450,
      ridesCompletedToday: 8,
      isAvailable: true
    },
    {
      id: 'driver_2',
      name: 'Priya M.',
      currentLocation: 'Powai',
      distanceFromPickup: 4.2,
      rating: 4.9,
      vehicleType: 'Electric Scooter',
      earningsToday: 320,
      ridesCompletedToday: 6,
      isAvailable: true
    },
    {
      id: 'driver_3',
      name: 'Amit K.',
      currentLocation: 'Malad West',
      distanceFromPickup: 6.1,
      rating: 4.7,
      vehicleType: 'Scooter',
      earningsToday: 280,
      ridesCompletedToday: 5,
      isAvailable: true
    }
  ]
}
