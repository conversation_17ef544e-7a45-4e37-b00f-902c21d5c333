/**
 * Ride Optimization Utilities
 * Refactored from various components for better organization
 */

export interface Location {
  latitude: number;
  longitude: number;
}

export interface RideRequest {
  id: string;
  userId: string;
  pickup: Location;
  destination: Location;
  requestTime: Date;
  preferredArrivalTime?: Date;
  passengerCount: number;
  specialRequirements?: string[];
}

export interface Driver {
  id: string;
  name: string;
  location: Location;
  isAvailable: boolean;
  rating: number;
  vehicleType: string;
  estimatedArrivalTime?: number; // in minutes
}

export interface OptimizationResult {
  assignedDriver: Driver;
  estimatedPickupTime: number;
  estimatedTotalTime: number;
  optimizationScore: number;
  route: Location[];
  carbonFootprint: number;
}

/**
 * Calculate the great circle distance between two points
 */
export function calculateDistance(point1: Location, point2: Location): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(point2.latitude - point1.latitude);
  const dLon = toRadians(point2.longitude - point1.longitude);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(point1.latitude)) * Math.cos(toRadians(point2.latitude)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculate estimated travel time based on distance and traffic
 */
export function calculateTravelTime(
  distance: number, 
  trafficFactor: number = 1.0,
  vehicleType: string = 'scooter'
): number {
  const baseSpeed = getBaseSpeed(vehicleType);
  const adjustedSpeed = baseSpeed / trafficFactor;
  return (distance / adjustedSpeed) * 60; // Convert to minutes
}

/**
 * Get base speed for different vehicle types
 */
function getBaseSpeed(vehicleType: string): number {
  const speeds = {
    scooter: 25, // km/h
    bike: 15,
    electric_scooter: 30,
    motorcycle: 40
  };
  return speeds[vehicleType as keyof typeof speeds] || 25;
}

/**
 * Calculate carbon footprint for a ride
 */
export function calculateCarbonFootprint(
  distance: number,
  vehicleType: string = 'electric_scooter'
): number {
  const emissionFactors = {
    electric_scooter: 0.02, // kg CO2 per km
    scooter: 0.08,
    bike: 0.0, // Human-powered
    motorcycle: 0.12
  };
  
  const factor = emissionFactors[vehicleType as keyof typeof emissionFactors] || 0.02;
  return distance * factor;
}

/**
 * Find the optimal driver for a ride request
 */
export function findOptimalDriver(
  rideRequest: RideRequest,
  availableDrivers: Driver[],
  trafficFactor: number = 1.0
): OptimizationResult | null {
  if (availableDrivers.length === 0) {
    return null;
  }

  let bestDriver: Driver | null = null;
  let bestScore = -1;
  let bestPickupTime = 0;
  let bestTotalTime = 0;

  for (const driver of availableDrivers) {
    if (!driver.isAvailable) continue;

    // Calculate distances
    const pickupDistance = calculateDistance(driver.location, rideRequest.pickup);
    const rideDistance = calculateDistance(rideRequest.pickup, rideRequest.destination);
    
    // Calculate times
    const pickupTime = calculateTravelTime(pickupDistance, trafficFactor, driver.vehicleType);
    const rideTime = calculateTravelTime(rideDistance, trafficFactor, driver.vehicleType);
    const totalTime = pickupTime + rideTime;

    // Calculate optimization score (higher is better)
    const distanceScore = 1 / (pickupDistance + 1); // Closer is better
    const ratingScore = driver.rating / 5; // Normalize rating
    const timeScore = 1 / (totalTime + 1); // Faster is better
    
    const score = (distanceScore * 0.4) + (ratingScore * 0.3) + (timeScore * 0.3);

    if (score > bestScore) {
      bestScore = score;
      bestDriver = driver;
      bestPickupTime = pickupTime;
      bestTotalTime = totalTime;
    }
  }

  if (!bestDriver) {
    return null;
  }

  const rideDistance = calculateDistance(rideRequest.pickup, rideRequest.destination);
  const carbonFootprint = calculateCarbonFootprint(rideDistance, bestDriver.vehicleType);

  return {
    assignedDriver: bestDriver,
    estimatedPickupTime: bestPickupTime,
    estimatedTotalTime: bestTotalTime,
    optimizationScore: bestScore,
    route: [bestDriver.location, rideRequest.pickup, rideRequest.destination],
    carbonFootprint
  };
}

/**
 * Calculate surge pricing based on demand and supply
 */
export function calculateSurgePricing(
  basePrice: number,
  demandLevel: number, // 0-1 scale
  supplyLevel: number, // 0-1 scale
  weatherFactor: number = 1.0
): { surgeMultiplier: number; finalPrice: number } {
  // Calculate demand-supply ratio
  const demandSupplyRatio = demandLevel / Math.max(supplyLevel, 0.1);
  
  // Apply weather factor
  const adjustedRatio = demandSupplyRatio * weatherFactor;
  
  // Calculate surge multiplier (1.0 to 3.0)
  const surgeMultiplier = Math.min(Math.max(adjustedRatio, 1.0), 3.0);
  
  const finalPrice = basePrice * surgeMultiplier;
  
  return { surgeMultiplier, finalPrice };
}

/**
 * Optimize route with multiple waypoints
 */
export function optimizeMultiWaypointRoute(waypoints: Location[]): Location[] {
  if (waypoints.length <= 2) {
    return waypoints;
  }

  // Simple nearest neighbor algorithm for TSP
  const optimized = [waypoints[0]];
  const remaining = waypoints.slice(1);
  let current = waypoints[0];

  while (remaining.length > 0) {
    let nearestIndex = 0;
    let nearestDistance = calculateDistance(current, remaining[0]);

    for (let i = 1; i < remaining.length; i++) {
      const distance = calculateDistance(current, remaining[i]);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestIndex = i;
      }
    }

    const nearest = remaining.splice(nearestIndex, 1)[0];
    optimized.push(nearest);
    current = nearest;
  }

  return optimized;
}

/**
 * Calculate ETA with real-time traffic data
 */
export function calculateETAWithTraffic(
  origin: Location,
  destination: Location,
  vehicleType: string = 'scooter',
  currentTraffic: number = 1.0
): { eta: number; distance: number; route: Location[] } {
  const distance = calculateDistance(origin, destination);
  const eta = calculateTravelTime(distance, currentTraffic, vehicleType);
  
  return {
    eta,
    distance,
    route: [origin, destination] // Simplified - would use actual routing service
  };
}

/**
 * Validate ride request data
 */
export function validateRideRequest(request: Partial<RideRequest>): string[] {
  const errors: string[] = [];

  if (!request.userId) {
    errors.push('User ID is required');
  }

  if (!request.pickup) {
    errors.push('Pickup location is required');
  } else {
    if (typeof request.pickup.latitude !== 'number' || 
        typeof request.pickup.longitude !== 'number') {
      errors.push('Invalid pickup coordinates');
    }
  }

  if (!request.destination) {
    errors.push('Destination location is required');
  } else {
    if (typeof request.destination.latitude !== 'number' || 
        typeof request.destination.longitude !== 'number') {
      errors.push('Invalid destination coordinates');
    }
  }

  if (request.passengerCount && (request.passengerCount < 1 || request.passengerCount > 4)) {
    errors.push('Passenger count must be between 1 and 4');
  }

  return errors;
}

/**
 * Format time duration for display
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${Math.round(minutes)} min`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = Math.round(minutes % 60);
  
  if (remainingMinutes === 0) {
    return `${hours} hr`;
  }
  
  return `${hours} hr ${remainingMinutes} min`;
}

/**
 * Format distance for display
 */
export function formatDistance(kilometers: number): string {
  if (kilometers < 1) {
    return `${Math.round(kilometers * 1000)} m`;
  }
  
  return `${kilometers.toFixed(1)} km`;
}

/**
 * Calculate ride cost based on distance, time, and surge
 */
export function calculateRideCost(
  distance: number,
  duration: number,
  surgeMultiplier: number = 1.0,
  vehicleType: string = 'scooter'
): { baseCost: number; surgeCost: number; totalCost: number } {
  const rates = {
    scooter: { base: 20, perKm: 8, perMin: 2 },
    bike: { base: 15, perKm: 5, perMin: 1 },
    electric_scooter: { base: 25, perKm: 10, perMin: 2.5 },
    motorcycle: { base: 30, perKm: 12, perMin: 3 }
  };

  const rate = rates[vehicleType as keyof typeof rates] || rates.scooter;
  const baseCost = rate.base + (distance * rate.perKm) + (duration * rate.perMin);
  const surgeCost = baseCost * surgeMultiplier;
  
  return {
    baseCost: Math.round(baseCost),
    surgeCost: Math.round(surgeCost),
    totalCost: Math.round(surgeCost)
  };
}
