import Razorpay from 'razorpay';
import crypto from 'crypto';
import { Payment } from '../models/Payment';
import { Wallet } from '../models/Wallet';
import { Transaction } from '../models/Transaction';
import connectDB from '../mongodb';

// Razorpay configuration
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || '',
  key_secret: process.env.RAZORPAY_KEY_SECRET || '',
});

export interface CreateOrderParams {
  amount: number; // Amount in paise (₹1 = 100 paise)
  currency?: string;
  receipt?: string;
  notes?: Record<string, any>;
  userId: string;
  rideId?: string;
  description?: string;
}

export interface PaymentVerificationParams {
  razorpay_order_id: string;
  razorpay_payment_id: string;
  razorpay_signature: string;
}

export class RazorpayService {
  
  /**
   * Create a new payment order
   */
  static async createOrder(params: CreateOrderParams) {
    try {
      await connectDB();

      const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Create Razorpay order
      const razorpayOrder = await razorpay.orders.create({
        amount: params.amount,
        currency: params.currency || 'INR',
        receipt: params.receipt || orderId,
        notes: params.notes || {},
      });

      // Calculate fees
      const fees = this.calculateFees(params.amount / 100); // Convert paise to rupees

      // Create payment record in database
      const payment = new Payment({
        orderId,
        razorpayOrderId: razorpayOrder.id,
        userId: params.userId,
        rideId: params.rideId,
        amount: params.amount / 100, // Store in rupees
        currency: params.currency || 'INR',
        paymentMethod: 'card', // Will be updated when payment is completed
        paymentProvider: 'razorpay',
        status: 'created',
        metadata: {
          description: params.description,
          notes: params.notes,
        },
        fees,
      });

      await payment.save();

      return {
        success: true,
        orderId: razorpayOrder.id,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
        paymentId: payment._id,
        key: process.env.RAZORPAY_KEY_ID,
      };
    } catch (error) {
      console.error('Error creating Razorpay order:', error);
      throw new Error('Failed to create payment order');
    }
  }

  /**
   * Verify payment signature
   */
  static verifyPaymentSignature(params: PaymentVerificationParams): boolean {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = params;
      
      const body = razorpay_order_id + '|' + razorpay_payment_id;
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || '')
        .update(body.toString())
        .digest('hex');

      return expectedSignature === razorpay_signature;
    } catch (error) {
      console.error('Error verifying payment signature:', error);
      return false;
    }
  }

  /**
   * Process successful payment
   */
  static async processSuccessfulPayment(params: PaymentVerificationParams & { paymentMethod?: string }) {
    try {
      await connectDB();

      const { razorpay_order_id, razorpay_payment_id, razorpay_signature, paymentMethod } = params;

      // Find payment record
      const payment = await Payment.findOne({ razorpayOrderId: razorpay_order_id });
      if (!payment) {
        throw new Error('Payment record not found');
      }

      // Verify signature
      if (!this.verifyPaymentSignature(params)) {
        throw new Error('Invalid payment signature');
      }

      // Get payment details from Razorpay
      const razorpayPayment = await razorpay.payments.fetch(razorpay_payment_id);

      // Update payment record
      payment.razorpayPaymentId = razorpay_payment_id;
      payment.razorpaySignature = razorpay_signature;
      payment.paymentMethod = paymentMethod || razorpayPayment.method;
      payment.status = 'completed';
      payment.completedAt = new Date();
      payment.metadata.customerEmail = razorpayPayment.email;
      payment.metadata.customerPhone = razorpayPayment.contact;

      await payment.save();

      // Create transaction record
      const transaction = new Transaction({
        userId: payment.userId,
        rideId: payment.rideId,
        type: 'payment',
        category: payment.rideId ? 'ride_payment' : 'wallet_topup',
        amount: payment.amount,
        currency: payment.currency,
        paymentMethod: payment.paymentMethod,
        paymentGateway: 'razorpay',
        transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        gatewayTransactionId: razorpay_payment_id,
        status: 'completed',
        description: payment.rideId ? 'Ride payment' : 'Wallet top-up',
        metadata: {
          paymentId: payment._id,
          razorpayOrderId: razorpay_order_id,
        },
        processedAt: new Date(),
        completedAt: new Date(),
      });

      await transaction.save();

      // If it's a wallet top-up, update wallet balance
      if (!payment.rideId) {
        await this.updateWalletBalance(payment.userId.toString(), payment.amount, 'credit');
      }

      return {
        success: true,
        payment,
        transaction,
      };
    } catch (error) {
      console.error('Error processing successful payment:', error);
      throw error;
    }
  }

  /**
   * Process failed payment
   */
  static async processFailedPayment(razorpayOrderId: string, failureReason: string) {
    try {
      await connectDB();

      const payment = await Payment.findOne({ razorpayOrderId });
      if (!payment) {
        throw new Error('Payment record not found');
      }

      payment.status = 'failed';
      payment.failureReason = failureReason;
      await payment.save();

      return { success: true, payment };
    } catch (error) {
      console.error('Error processing failed payment:', error);
      throw error;
    }
  }

  /**
   * Create refund
   */
  static async createRefund(paymentId: string, amount?: number, reason?: string) {
    try {
      await connectDB();

      const payment = await Payment.findById(paymentId);
      if (!payment || !payment.razorpayPaymentId) {
        throw new Error('Payment not found or not completed');
      }

      if (!payment.canRefund()) {
        throw new Error('Payment cannot be refunded');
      }

      const refundAmount = amount || payment.amount * 100; // Convert to paise

      // Create refund in Razorpay
      const refund = await razorpay.payments.refund(payment.razorpayPaymentId, {
        amount: refundAmount,
        notes: { reason: reason || 'Customer request' },
      });

      // Update payment record
      payment.refundId = refund.id;
      payment.refundAmount = refundAmount / 100; // Store in rupees
      payment.refundStatus = 'pending';
      payment.status = 'refunded';
      await payment.save();

      // Create refund transaction
      const transaction = new Transaction({
        userId: payment.userId,
        rideId: payment.rideId,
        type: 'refund',
        category: 'ride_payment',
        amount: refundAmount / 100,
        currency: payment.currency,
        paymentMethod: payment.paymentMethod,
        paymentGateway: 'razorpay',
        transactionId: `refund_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        gatewayTransactionId: refund.id,
        status: 'completed',
        description: `Refund for payment ${payment.orderId}`,
        metadata: {
          originalPaymentId: payment._id,
          refundReason: reason,
        },
        processedAt: new Date(),
        completedAt: new Date(),
      });

      await transaction.save();

      return {
        success: true,
        refund,
        transaction,
      };
    } catch (error) {
      console.error('Error creating refund:', error);
      throw error;
    }
  }

  /**
   * Update wallet balance
   */
  static async updateWalletBalance(userId: string, amount: number, type: 'credit' | 'debit') {
    try {
      const wallet = await Wallet.findOne({ userId });
      if (!wallet) {
        throw new Error('Wallet not found');
      }

      const balanceBefore = wallet.balance;
      
      if (type === 'credit') {
        wallet.balance += amount;
        wallet.totalEarned += amount;
      } else {
        if (wallet.balance < amount) {
          throw new Error('Insufficient wallet balance');
        }
        wallet.balance -= amount;
        wallet.totalSpent += amount;
      }

      wallet.lastTransactionAt = new Date();
      await wallet.save();

      return {
        success: true,
        balanceBefore,
        balanceAfter: wallet.balance,
        wallet,
      };
    } catch (error) {
      console.error('Error updating wallet balance:', error);
      throw error;
    }
  }

  /**
   * Calculate payment fees
   */
  static calculateFees(amount: number) {
    const platformFeeRate = 0.02; // 2% platform fee
    const paymentGatewayFeeRate = 0.024; // 2.4% Razorpay fee
    const gstRate = 0.18; // 18% GST on fees

    const platformFee = amount * platformFeeRate;
    const paymentGatewayFee = amount * paymentGatewayFeeRate;
    const gst = (platformFee + paymentGatewayFee) * gstRate;
    const total = platformFee + paymentGatewayFee + gst;

    return {
      platformFee: Math.round(platformFee * 100) / 100,
      paymentGatewayFee: Math.round(paymentGatewayFee * 100) / 100,
      gst: Math.round(gst * 100) / 100,
      total: Math.round(total * 100) / 100,
    };
  }

  /**
   * Get payment status
   */
  static async getPaymentStatus(orderId: string) {
    try {
      await connectDB();
      
      const payment = await Payment.findOne({ orderId }).populate('userId', 'firstName lastName email');
      return payment;
    } catch (error) {
      console.error('Error getting payment status:', error);
      throw error;
    }
  }
}

export default RazorpayService;
