#!/bin/bash

# Development Workflow Script for Two-Wheeler Sharing Platform
# Provides convenient commands for common development tasks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FEATURE_FLAGS_CONFIG="$PROJECT_ROOT/development-workflow/feature-flags-config.json"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_deps=()
    
    # Check for required commands
    for cmd in node npm git curl jq; do
        if ! command -v $cmd &> /dev/null; then
            missing_deps+=($cmd)
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install the missing dependencies and try again"
        exit 1
    fi
    
    log_success "All dependencies are installed"
}

# Setup development environment
setup_dev_env() {
    log_info "Setting up development environment..."
    
    # Install Node.js dependencies
    if [ -f "$PROJECT_ROOT/package.json" ]; then
        log_info "Installing Node.js dependencies..."
        cd "$PROJECT_ROOT" && npm install
    fi
    
    # Install Python dependencies (if any)
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        log_info "Installing Python dependencies..."
        pip install -r "$PROJECT_ROOT/requirements.txt"
    fi
    
    # Setup Git hooks
    if [ -d "$PROJECT_ROOT/.git" ]; then
        log_info "Setting up Git hooks..."
        # Create pre-commit hook
        cat > "$PROJECT_ROOT/.git/hooks/pre-commit" << 'EOF'
#!/bin/bash
# Run linting and tests before commit
npm run lint
npm run type-check
npm test -- --watchAll=false
EOF
        chmod +x "$PROJECT_ROOT/.git/hooks/pre-commit"
    fi
    
    log_success "Development environment setup complete"
}

# Feature flag management
update_feature_flag() {
    local flag_name="$1"
    local enabled="$2"
    local environment="${3:-all}"
    
    if [ -z "$flag_name" ] || [ -z "$enabled" ]; then
        log_error "Usage: update_feature_flag <flag_name> <true|false> [environment]"
        return 1
    fi
    
    log_info "Updating feature flag: $flag_name to $enabled for $environment"
    
    if [ ! -f "$FEATURE_FLAGS_CONFIG" ]; then
        log_error "Feature flags configuration file not found"
        return 1
    fi
    
    # Update the JSON file using jq
    local temp_file=$(mktemp)
    if [ "$environment" = "all" ]; then
        jq ".feature_flags.${flag_name}.enabled = ${enabled} | 
            .feature_flags.${flag_name}.environments.development = ${enabled} |
            .feature_flags.${flag_name}.environments.staging = ${enabled} |
            .feature_flags.${flag_name}.environments.production = ${enabled} |
            .metadata.last_updated = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" \
            "$FEATURE_FLAGS_CONFIG" > "$temp_file"
    else
        jq ".feature_flags.${flag_name}.environments.${environment} = ${enabled} |
            .metadata.last_updated = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"" \
            "$FEATURE_FLAGS_CONFIG" > "$temp_file"
    fi
    
    mv "$temp_file" "$FEATURE_FLAGS_CONFIG"
    log_success "Feature flag updated successfully"
}

# List all feature flags
list_feature_flags() {
    log_info "Current feature flags configuration:"
    
    if [ -f "$FEATURE_FLAGS_CONFIG" ]; then
        jq -r '.feature_flags | to_entries[] | 
               "\(.key): \(.value.enabled) (dev:\(.value.environments.development), staging:\(.value.environments.staging), prod:\(.value.environments.production))"' \
               "$FEATURE_FLAGS_CONFIG"
    else
        log_error "Feature flags configuration file not found"
        return 1
    fi
}

# Create a new feature branch
create_feature_branch() {
    local issue_number="$1"
    local description="$2"
    
    if [ -z "$issue_number" ]; then
        log_error "Usage: create_feature_branch <issue_number> [description]"
        return 1
    fi
    
    local branch_name="feature/issue-$issue_number"
    if [ -n "$description" ]; then
        # Convert description to branch-friendly format
        local desc_slug=$(echo "$description" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
        branch_name="feature/issue-$issue_number-$desc_slug"
    fi
    
    log_info "Creating feature branch: $branch_name"
    
    # Ensure we're on main branch and up to date
    git checkout main
    git pull origin main
    
    # Create and checkout new branch
    git checkout -b "$branch_name"
    
    log_success "Feature branch created: $branch_name"
}

# Run tests
run_tests() {
    local test_type="${1:-all}"
    
    log_info "Running tests: $test_type"
    
    cd "$PROJECT_ROOT"
    
    case $test_type in
        "unit")
            log_info "Running unit tests..."
            npm test -- --watchAll=false
            ;;
        "e2e")
            log_info "Running end-to-end tests..."
            npm run test:e2e
            ;;
        "coverage")
            log_info "Running tests with coverage..."
            npm test -- --coverage --watchAll=false
            ;;
        "all")
            log_info "Running all tests..."
            npm test -- --coverage --watchAll=false
            npm run test:e2e
            ;;
        *)
            log_error "Unknown test type: $test_type"
            log_info "Available types: unit, e2e, coverage, all"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        log_success "Tests completed successfully"
    else
        log_error "Tests failed"
        return 1
    fi
}

# Code quality checks
run_quality_checks() {
    log_info "Running code quality checks..."
    
    cd "$PROJECT_ROOT"
    
    # TypeScript compilation
    log_info "Checking TypeScript compilation..."
    npx tsc --noEmit
    
    # Linting
    log_info "Running ESLint..."
    npx eslint . --ext .js,.ts,.tsx --max-warnings 0
    
    # Formatting check
    log_info "Checking code formatting..."
    npx prettier --check .
    
    # Dependency audit
    log_info "Running security audit..."
    npm audit --audit-level=moderate
    
    log_success "Code quality checks completed"
}

# Build the application
build_app() {
    local environment="${1:-production}"
    
    log_info "Building application for $environment..."
    
    cd "$PROJECT_ROOT"
    
    # Set environment
    export NODE_ENV="$environment"
    
    # Build the application
    npm run build
    
    if [ $? -eq 0 ]; then
        log_success "Build completed successfully"
    else
        log_error "Build failed"
        return 1
    fi
}

# Start development server
start_dev_server() {
    log_info "Starting development server..."
    
    cd "$PROJECT_ROOT"
    
    # Start the development server
    npm run dev
}

# Generate documentation
generate_docs() {
    log_info "Generating documentation..."
    
    cd "$PROJECT_ROOT"
    
    # Generate TypeScript documentation
    if command -v typedoc &> /dev/null; then
        log_info "Generating TypeScript documentation..."
        npx typedoc --out docs/api --entryPoints . --exclude node_modules
    fi
    
    # Update README with recent changes
    log_info "Updating README..."
    node -e "
    const fs = require('fs');
    const { execSync } = require('child_process');
    
    // Get recent commits
    const commits = execSync('git log --oneline -5').toString().trim().split('\n');
    
    const readmeContent = \`# Two-Wheeler Sharing Platform
    
## 🚀 Recent Updates
\${commits.map(commit => \`- \${commit}\`).join('\n')}

## 🛠️ Development Workflow

### Quick Start
\\\`\\\`\\\`bash
# Setup development environment
./scripts/dev-workflow.sh setup

# Start development server
./scripts/dev-workflow.sh start

# Run tests
./scripts/dev-workflow.sh test

# Update feature flags
./scripts/dev-workflow.sh update_flag ar_navigation true
\\\`\\\`\\\`

### Available Commands
- \\\`npm run dev\\\` - Start development server
- \\\`npm run build\\\` - Build for production
- \\\`npm run test\\\` - Run test suite
- \\\`npm run lint\\\` - Run linting
- \\\`npm run type-check\\\` - Run TypeScript checks

## 📊 Feature Flags
Current feature flags can be managed through the development workflow:
- AI Enhanced Ride Matching: ✅ Enabled
- Autonomous Vehicle Control: ✅ Enabled  
- AR Navigation: ✅ Enabled
- Voice AI Commands: ✅ Enabled
- Carbon Neutrality Optimization: ✅ Enabled

## 🧪 Testing
- Unit tests: Jest + React Testing Library
- E2E tests: Cypress/Playwright
- Performance tests: Lighthouse
- Security tests: npm audit

## 📚 Documentation
- Component Documentation: Storybook
- API Documentation: TypeDoc
- Development Guide: This README
\`;
    
    fs.writeFileSync('README.md', readmeContent);
    "
    
    log_success "Documentation generated"
}

# Create a pull request
create_pull_request() {
    local title="$1"
    local description="$2"
    
    if [ -z "$title" ]; then
        log_error "Usage: create_pull_request <title> [description]"
        return 1
    fi
    
    log_info "Creating pull request: $title"
    
    # Get current branch
    local current_branch=$(git branch --show-current)
    
    if [ "$current_branch" = "main" ]; then
        log_error "Cannot create PR from main branch"
        return 1
    fi
    
    # Push current branch
    git push origin "$current_branch"
    
    # Create PR using GitHub CLI if available
    if command -v gh &> /dev/null; then
        if [ -n "$description" ]; then
            gh pr create --title "$title" --body "$description"
        else
            gh pr create --title "$title"
        fi
        log_success "Pull request created"
    else
        log_info "GitHub CLI not available. Please create PR manually"
        log_info "Branch pushed: $current_branch"
    fi
}

# Main command dispatcher
main() {
    local command="$1"
    shift
    
    case $command in
        "setup")
            check_dependencies
            setup_dev_env
            ;;
        "update_flag")
            update_feature_flag "$@"
            ;;
        "list_flags")
            list_feature_flags
            ;;
        "create_branch")
            create_feature_branch "$@"
            ;;
        "test")
            run_tests "$@"
            ;;
        "quality")
            run_quality_checks
            ;;
        "build")
            build_app "$@"
            ;;
        "start")
            start_dev_server
            ;;
        "docs")
            generate_docs
            ;;
        "pr")
            create_pull_request "$@"
            ;;
        "help"|"--help"|"-h"|"")
            echo "Development Workflow Commands"
            echo ""
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  setup                           Setup development environment"
            echo "  update_flag <name> <bool> [env] Update feature flag"
            echo "  list_flags                      List all feature flags"
            echo "  create_branch <issue> [desc]    Create feature branch"
            echo "  test [type]                     Run tests (unit|e2e|coverage|all)"
            echo "  quality                         Run code quality checks"
            echo "  build [env]                     Build application"
            echo "  start                           Start development server"
            echo "  docs                           Generate documentation"
            echo "  pr <title> [description]       Create pull request"
            echo "  help                           Show this help message"
            ;;
        *)
            log_error "Unknown command: $command"
            log_info "Use '$0 help' to see available commands"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
