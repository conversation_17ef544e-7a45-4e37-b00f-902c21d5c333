import pymongo
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import os

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self):
        self.client = None
        self.db = None
        self._connect()
    
    def _connect(self):
        """Connect to MongoDB"""
        try:
            mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://mongodb:27017/two-wheeler-sharing')
            self.client = pymongo.MongoClient(mongodb_uri)
            self.db = self.client.get_default_database()
            
            # Test connection
            self.client.admin.command('ping')
            logger.info("Connected to MongoDB successfully")
            
        except Exception as e:
            logger.error(f"MongoDB connection error: {e}")
            # Use mock data if connection fails
            self.client = None
            self.db = None
    
    async def get_rides_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """Get rides within date range"""
        try:
            if not self.db:
                return self._get_mock_rides_data()
            
            rides = list(self.db.rides.find({
                'createdAt': {
                    '$gte': start_date,
                    '$lte': end_date
                }
            }))
            
            return rides
            
        except Exception as e:
            logger.error(f"Error fetching rides: {e}")
            return self._get_mock_rides_data()
    
    async def get_driver_locations(self) -> List[Dict]:
        """Get current driver locations"""
        try:
            if not self.db:
                return self._get_mock_driver_locations()
            
            drivers = list(self.db.drivers.find({
                'isOnline': True,
                'isAvailable': True
            }, {
                'id': 1,
                'name': 1,
                'location': 1,
                'rating': 1,
                'earningsToday': 1,
                'ridesCompletedToday': 1
            }))
            
            return drivers
            
        except Exception as e:
            logger.error(f"Error fetching driver locations: {e}")
            return self._get_mock_driver_locations()
    
    async def get_demand_history(self, location: str, days: int = 30) -> List[Dict]:
        """Get historical demand data for a location"""
        try:
            if not self.db:
                return self._get_mock_demand_history(location, days)
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # Aggregate rides by hour and location to calculate demand
            pipeline = [
                {
                    '$match': {
                        'pickup': {'$regex': location, '$options': 'i'},
                        'createdAt': {'$gte': start_date, '$lte': end_date}
                    }
                },
                {
                    '$group': {
                        '_id': {
                            'date': {'$dateToString': {'format': '%Y-%m-%d', 'date': '$createdAt'}},
                            'hour': {'$hour': '$createdAt'}
                        },
                        'demand': {'$sum': 1}
                    }
                },
                {
                    '$sort': {'_id.date': 1, '_id.hour': 1}
                }
            ]
            
            demand_data = list(self.db.rides.aggregate(pipeline))
            return demand_data
            
        except Exception as e:
            logger.error(f"Error fetching demand history: {e}")
            return self._get_mock_demand_history(location, days)
    
    async def get_vehicle_maintenance_data(self, vehicle_ids: Optional[List[str]] = None) -> List[Dict]:
        """Get vehicle maintenance and usage data"""
        try:
            if not self.db:
                return self._get_mock_maintenance_data()
            
            query = {}
            if vehicle_ids:
                query['vehicleId'] = {'$in': vehicle_ids}
            
            vehicles = list(self.db.vehicles.find(query, {
                'vehicleId': 1,
                'model': 1,
                'year': 1,
                'mileage': 1,
                'lastMaintenanceDate': 1,
                'maintenanceHistory': 1,
                'usageStats': 1,
                'performanceMetrics': 1
            }))
            
            return vehicles
            
        except Exception as e:
            logger.error(f"Error fetching vehicle data: {e}")
            return self._get_mock_maintenance_data()
    
    async def save_prediction_result(self, prediction_type: str, result: Dict):
        """Save prediction result to database"""
        try:
            if not self.db:
                logger.warning("Database not available, skipping prediction save")
                return
            
            prediction_doc = {
                'type': prediction_type,
                'result': result,
                'timestamp': datetime.now(),
                'model_version': '1.0'
            }
            
            self.db.predictions.insert_one(prediction_doc)
            logger.debug(f"Saved {prediction_type} prediction result")
            
        except Exception as e:
            logger.error(f"Error saving prediction result: {e}")
    
    async def get_pricing_history(self, days: int = 7) -> List[Dict]:
        """Get historical pricing data"""
        try:
            if not self.db:
                return self._get_mock_pricing_history()
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            pricing_data = list(self.db.rides.find({
                'createdAt': {'$gte': start_date, '$lte': end_date},
                'status': 'completed'
            }, {
                'distance': 1,
                'finalFare': 1,
                'surgeMultiplier': 1,
                'createdAt': 1,
                'pickup': 1,
                'destination': 1,
                'weather': 1,
                'demandLevel': 1
            }))
            
            return pricing_data
            
        except Exception as e:
            logger.error(f"Error fetching pricing history: {e}")
            return self._get_mock_pricing_history()
    
    def _get_mock_rides_data(self) -> List[Dict]:
        """Generate mock rides data for testing"""
        import random
        from datetime import datetime, timedelta
        
        mock_rides = []
        locations = ['Andheri East', 'BKC', 'Powai', 'Malad West', 'Lower Parel']
        
        for i in range(100):
            ride_date = datetime.now() - timedelta(days=random.randint(0, 30))
            mock_rides.append({
                'id': f'ride_{i}',
                'pickup': random.choice(locations),
                'destination': random.choice(locations),
                'distance': random.uniform(2, 20),
                'fare': random.uniform(25, 150),
                'createdAt': ride_date,
                'status': 'completed',
                'userId': f'user_{random.randint(1, 50)}',
                'driverId': f'driver_{random.randint(1, 20)}'
            })
        
        return mock_rides
    
    def _get_mock_driver_locations(self) -> List[Dict]:
        """Generate mock driver location data"""
        import random
        
        mock_drivers = []
        locations = [
            {'name': 'Andheri East', 'lat': 19.1136, 'lon': 72.8697},
            {'name': 'BKC', 'lat': 19.0596, 'lon': 72.8656},
            {'name': 'Powai', 'lat': 19.1197, 'lon': 72.9056},
            {'name': 'Malad West', 'lat': 19.1875, 'lon': 72.8304}
        ]
        
        for i in range(20):
            location = random.choice(locations)
            mock_drivers.append({
                'id': f'driver_{i}',
                'name': f'Driver {i}',
                'latitude': location['lat'] + random.uniform(-0.01, 0.01),
                'longitude': location['lon'] + random.uniform(-0.01, 0.01),
                'rating': random.uniform(4.0, 5.0),
                'earningsToday': random.randint(200, 800),
                'ridesCompletedToday': random.randint(3, 15),
                'isAvailable': True
            })
        
        return mock_drivers
    
    def _get_mock_demand_history(self, location: str, days: int) -> List[Dict]:
        """Generate mock demand history data"""
        import random
        
        mock_data = []
        for day in range(days):
            date = datetime.now() - timedelta(days=day)
            for hour in range(24):
                # Simulate demand patterns
                base_demand = 5
                if hour in [8, 9, 18, 19]:  # Peak hours
                    base_demand = 15
                elif hour in [7, 10, 17, 20]:
                    base_demand = 10
                
                demand = max(0, base_demand + random.randint(-3, 3))
                
                mock_data.append({
                    '_id': {
                        'date': date.strftime('%Y-%m-%d'),
                        'hour': hour
                    },
                    'demand': demand
                })
        
        return mock_data
    
    def _get_mock_maintenance_data(self) -> List[Dict]:
        """Generate mock vehicle maintenance data"""
        import random
        
        mock_vehicles = []
        for i in range(50):
            last_maintenance = datetime.now() - timedelta(days=random.randint(10, 90))
            
            mock_vehicles.append({
                'vehicleId': f'vehicle_{i:03d}',
                'model': random.choice(['Hero Splendor', 'Honda Activa', 'Ather 450X']),
                'year': random.randint(2018, 2023),
                'mileage': random.randint(5000, 50000),
                'lastMaintenanceDate': last_maintenance,
                'maintenanceHistory': [
                    {
                        'date': last_maintenance,
                        'type': 'routine_service',
                        'cost': random.randint(1500, 3000)
                    }
                ],
                'usageStats': {
                    'dailyKm': random.randint(50, 200),
                    'ridesPerDay': random.randint(8, 25)
                },
                'performanceMetrics': {
                    'fuelEfficiency': random.uniform(40, 60),
                    'breakdownCount': random.randint(0, 3)
                }
            })
        
        return mock_vehicles
    
    def _get_mock_pricing_history(self) -> List[Dict]:
        """Generate mock pricing history data"""
        import random
        
        mock_pricing = []
        for i in range(200):
            ride_date = datetime.now() - timedelta(days=random.randint(0, 7))
            distance = random.uniform(2, 20)
            base_fare = distance * 5
            surge = random.uniform(1.0, 2.5)
            
            mock_pricing.append({
                'distance': distance,
                'finalFare': base_fare * surge,
                'surgeMultiplier': surge,
                'createdAt': ride_date,
                'pickup': random.choice(['Andheri East', 'BKC', 'Powai']),
                'destination': random.choice(['Andheri East', 'BKC', 'Powai']),
                'weather': random.choice(['clear', 'cloudy', 'rain']),
                'demandLevel': random.choice(['low', 'medium', 'high'])
            })
        
        return mock_pricing
    
    def close(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("Database connection closed")
