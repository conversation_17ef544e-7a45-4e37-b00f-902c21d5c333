// Real-Time Communication System with WebSocket, Voice, and Video
import { io, Socket } from 'socket.io-client';

interface ChatMessage {
  id: string;
  tripId: string;
  senderId: string;
  receiverId: string;
  type: 'text' | 'voice' | 'location' | 'image' | 'quick-reply' | 'system';
  content: string;
  metadata?: {
    location?: { lat: number; lng: number };
    duration?: number; // for voice messages
    imageUrl?: string;
    quickReplyOptions?: string[];
  };
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
  isEncrypted: boolean;
}

interface VoiceCallSession {
  id: string;
  tripId: string;
  participants: string[];
  status: 'initiating' | 'ringing' | 'active' | 'ended';
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  quality: 'excellent' | 'good' | 'fair' | 'poor';
}

interface LocationUpdate {
  userId: string;
  tripId: string;
  location: { lat: number; lng: number };
  heading: number;
  speed: number;
  accuracy: number;
  timestamp: Date;
}

interface SafetyAlert {
  id: string;
  tripId: string;
  userId: string;
  type: 'emergency' | 'route-deviation' | 'speed-alert' | 'breakdown' | 'safety-concern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  location: { lat: number; lng: number };
  timestamp: Date;
  autoResolved: boolean;
}

class RealTimeCommunication {
  private socket: Socket | null = null;
  private currentTripId: string | null = null;
  private userId: string | null = null;
  private messageHandlers: Map<string, Function> = new Map();
  private locationUpdateInterval: NodeJS.Timeout | null = null;
  private voiceRecorder: MediaRecorder | null = null;
  private audioContext: AudioContext | null = null;

  constructor() {
    this.initializeSocket();
    this.setupMessageHandlers();
  }

  // Initialize WebSocket connection
  private initializeSocket(): void {
    this.socket = io(process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://localhost:3001', {
      transports: ['websocket'],
      upgrade: true,
      rememberUpgrade: true,
    });

    this.socket.on('connect', () => {
      console.log('Connected to real-time communication server');
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from real-time communication server');
      this.handleReconnection();
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }

  // Join a trip communication channel
  async joinTrip(tripId: string, userId: string, userRole: 'rider' | 'driver'): Promise<void> {
    this.currentTripId = tripId;
    this.userId = userId;

    if (this.socket) {
      this.socket.emit('join-trip', {
        tripId,
        userId,
        userRole,
        timestamp: new Date(),
      });

      // Start location sharing
      this.startLocationSharing();

      // Set up trip-specific message handlers
      this.setupTripHandlers();
    }
  }

  // Leave trip communication channel
  async leaveTrip(): Promise<void> {
    if (this.socket && this.currentTripId) {
      this.socket.emit('leave-trip', {
        tripId: this.currentTripId,
        userId: this.userId,
        timestamp: new Date(),
      });

      this.stopLocationSharing();
      this.currentTripId = null;
      this.userId = null;
    }
  }

  // Send text message
  async sendMessage(
    receiverId: string,
    content: string,
    type: 'text' | 'quick-reply' = 'text'
  ): Promise<ChatMessage> {
    const message: ChatMessage = {
      id: this.generateMessageId(),
      tripId: this.currentTripId!,
      senderId: this.userId!,
      receiverId,
      type,
      content,
      timestamp: new Date(),
      status: 'sent',
      isEncrypted: true,
    };

    // Encrypt message content
    message.content = await this.encryptMessage(content);

    if (this.socket) {
      this.socket.emit('send-message', message);
    }

    return message;
  }

  // Send voice message
  async sendVoiceMessage(receiverId: string, audioBlob: Blob): Promise<ChatMessage> {
    const audioUrl = await this.uploadAudio(audioBlob);
    const duration = await this.getAudioDuration(audioBlob);

    const message: ChatMessage = {
      id: this.generateMessageId(),
      tripId: this.currentTripId!,
      senderId: this.userId!,
      receiverId,
      type: 'voice',
      content: audioUrl,
      metadata: { duration },
      timestamp: new Date(),
      status: 'sent',
      isEncrypted: true,
    };

    if (this.socket) {
      this.socket.emit('send-message', message);
    }

    return message;
  }

  // Send location
  async sendLocation(receiverId: string): Promise<ChatMessage> {
    const location = await this.getCurrentLocation();

    const message: ChatMessage = {
      id: this.generateMessageId(),
      tripId: this.currentTripId!,
      senderId: this.userId!,
      receiverId,
      type: 'location',
      content: 'Shared location',
      metadata: { location },
      timestamp: new Date(),
      status: 'sent',
      isEncrypted: false, // Location data is not encrypted for emergency purposes
    };

    if (this.socket) {
      this.socket.emit('send-message', message);
    }

    return message;
  }

  // Start voice call
  async initiateVoiceCall(receiverId: string): Promise<VoiceCallSession> {
    const callSession: VoiceCallSession = {
      id: this.generateCallId(),
      tripId: this.currentTripId!,
      participants: [this.userId!, receiverId],
      status: 'initiating',
      quality: 'excellent',
    };

    if (this.socket) {
      this.socket.emit('initiate-call', callSession);
    }

    return callSession;
  }

  // Answer voice call
  async answerCall(callId: string): Promise<void> {
    if (this.socket) {
      this.socket.emit('answer-call', {
        callId,
        userId: this.userId,
        timestamp: new Date(),
      });
    }
  }

  // End voice call
  async endCall(callId: string): Promise<void> {
    if (this.socket) {
      this.socket.emit('end-call', {
        callId,
        userId: this.userId,
        timestamp: new Date(),
      });
    }
  }

  // Send safety alert
  async sendSafetyAlert(
    type: SafetyAlert['type'],
    severity: SafetyAlert['severity'],
    message: string
  ): Promise<SafetyAlert> {
    const location = await this.getCurrentLocation();

    const alert: SafetyAlert = {
      id: this.generateAlertId(),
      tripId: this.currentTripId!,
      userId: this.userId!,
      type,
      severity,
      message,
      location,
      timestamp: new Date(),
      autoResolved: false,
    };

    if (this.socket) {
      this.socket.emit('safety-alert', alert);
    }

    // If critical, also notify emergency services
    if (severity === 'critical') {
      await this.notifyEmergencyServices(alert);
    }

    return alert;
  }

  // Start voice recording
  async startVoiceRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.voiceRecorder = new MediaRecorder(stream);
      
      const audioChunks: Blob[] = [];
      
      this.voiceRecorder.ondataavailable = (event) => {
        audioChunks.push(event.data);
      };

      this.voiceRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
        this.onVoiceRecordingComplete?.(audioBlob);
      };

      this.voiceRecorder.start();
    } catch (error) {
      console.error('Failed to start voice recording:', error);
      throw new Error('Microphone access denied');
    }
  }

  // Stop voice recording
  async stopVoiceRecording(): Promise<void> {
    if (this.voiceRecorder && this.voiceRecorder.state === 'recording') {
      this.voiceRecorder.stop();
      
      // Stop all tracks to release microphone
      this.voiceRecorder.stream?.getTracks().forEach(track => track.stop());
    }
  }

  // Start location sharing
  private startLocationSharing(): void {
    if (navigator.geolocation) {
      this.locationUpdateInterval = setInterval(async () => {
        try {
          const location = await this.getCurrentLocation();
          const locationUpdate: LocationUpdate = {
            userId: this.userId!,
            tripId: this.currentTripId!,
            location,
            heading: 0, // Would get from device compass
            speed: 0, // Would calculate from GPS
            accuracy: 10, // GPS accuracy in meters
            timestamp: new Date(),
          };

          if (this.socket) {
            this.socket.emit('location-update', locationUpdate);
          }
        } catch (error) {
          console.error('Failed to get location:', error);
        }
      }, 5000); // Update every 5 seconds
    }
  }

  // Stop location sharing
  private stopLocationSharing(): void {
    if (this.locationUpdateInterval) {
      clearInterval(this.locationUpdateInterval);
      this.locationUpdateInterval = null;
    }
  }

  // Setup message handlers
  private setupMessageHandlers(): void {
    if (!this.socket) return;

    this.socket.on('message-received', async (message: ChatMessage) => {
      // Decrypt message if encrypted
      if (message.isEncrypted) {
        message.content = await this.decryptMessage(message.content);
      }
      
      this.messageHandlers.get('message-received')?.(message);
    });

    this.socket.on('call-incoming', (callSession: VoiceCallSession) => {
      this.messageHandlers.get('call-incoming')?.(callSession);
    });

    this.socket.on('call-answered', (callSession: VoiceCallSession) => {
      this.messageHandlers.get('call-answered')?.(callSession);
    });

    this.socket.on('call-ended', (callSession: VoiceCallSession) => {
      this.messageHandlers.get('call-ended')?.(callSession);
    });

    this.socket.on('location-update', (update: LocationUpdate) => {
      this.messageHandlers.get('location-update')?.(update);
    });

    this.socket.on('safety-alert', (alert: SafetyAlert) => {
      this.messageHandlers.get('safety-alert')?.(alert);
    });

    this.socket.on('typing-indicator', (data: { userId: string; isTyping: boolean }) => {
      this.messageHandlers.get('typing-indicator')?.(data);
    });
  }

  // Setup trip-specific handlers
  private setupTripHandlers(): void {
    // Auto-send quick replies for common scenarios
    this.setupQuickReplies();
    
    // Monitor for route deviations
    this.setupRouteMonitoring();
    
    // Setup emergency detection
    this.setupEmergencyDetection();
  }

  // Register event handlers
  onMessageReceived(handler: (message: ChatMessage) => void): void {
    this.messageHandlers.set('message-received', handler);
  }

  onCallIncoming(handler: (call: VoiceCallSession) => void): void {
    this.messageHandlers.set('call-incoming', handler);
  }

  onLocationUpdate(handler: (update: LocationUpdate) => void): void {
    this.messageHandlers.set('location-update', handler);
  }

  onSafetyAlert(handler: (alert: SafetyAlert) => void): void {
    this.messageHandlers.set('safety-alert', handler);
  }

  onVoiceRecordingComplete?: (audioBlob: Blob) => void;

  // Utility methods
  private async getCurrentLocation(): Promise<{ lat: number; lng: number }> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          });
        },
        (error) => reject(error),
        { enableHighAccuracy: true, timeout: 10000 }
      );
    });
  }

  private async encryptMessage(content: string): Promise<string> {
    // Implement end-to-end encryption
    // For demo purposes, return base64 encoded content
    return btoa(content);
  }

  private async decryptMessage(encryptedContent: string): Promise<string> {
    // Implement decryption
    // For demo purposes, return base64 decoded content
    try {
      return atob(encryptedContent);
    } catch {
      return encryptedContent; // Return as-is if not encrypted
    }
  }

  private async uploadAudio(audioBlob: Blob): Promise<string> {
    // Upload audio to cloud storage and return URL
    // For demo purposes, create a local URL
    return URL.createObjectURL(audioBlob);
  }

  private async getAudioDuration(audioBlob: Blob): Promise<number> {
    return new Promise((resolve) => {
      const audio = new Audio();
      audio.onloadedmetadata = () => {
        resolve(audio.duration);
      };
      audio.src = URL.createObjectURL(audioBlob);
    });
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private handleReconnection(): void {
    // Implement reconnection logic
    setTimeout(() => {
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    }, 5000);
  }

  private setupQuickReplies(): void {
    // Setup common quick reply messages
  }

  private setupRouteMonitoring(): void {
    // Monitor for route deviations and alert
  }

  private setupEmergencyDetection(): void {
    // Detect emergency situations (sudden stops, crashes, etc.)
  }

  private async notifyEmergencyServices(alert: SafetyAlert): Promise<void> {
    // Notify emergency services for critical alerts
    console.log('Emergency services notified:', alert);
  }
}

export { RealTimeCommunication };
export type { 
  ChatMessage, 
  VoiceCallSession, 
  LocationUpdate, 
  SafetyAlert 
};
