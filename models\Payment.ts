import mongoose, { Schema, Document } from 'mongoose';

export interface IPayment extends Document {
  orderId: string;
  paymentId?: string;
  razorpayOrderId?: string;
  razorpayPaymentId?: string;
  razorpaySignature?: string;
  userId: mongoose.Types.ObjectId;
  rideId?: mongoose.Types.ObjectId;
  walletId?: mongoose.Types.ObjectId;
  amount: number;
  currency: string;
  paymentMethod: 'card' | 'upi' | 'netbanking' | 'wallet' | 'emi' | 'bnpl';
  paymentProvider: 'razorpay' | 'stripe' | 'paytm' | 'phonepe';
  status: 'created' | 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  failureReason?: string;
  refundId?: string;
  refundAmount?: number;
  refundStatus?: 'pending' | 'processed' | 'failed';
  metadata: {
    customerEmail?: string;
    customerPhone?: string;
    description?: string;
    notes?: Record<string, any>;
  };
  fees: {
    platformFee: number;
    paymentGatewayFee: number;
    gst: number;
    total: number;
  };
  settlement: {
    settlementId?: string;
    settledAt?: Date;
    settledAmount?: number;
  };
  webhookEvents: Array<{
    eventType: string;
    eventData: any;
    receivedAt: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

const PaymentSchema: Schema = new Schema({
  orderId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  paymentId: {
    type: String,
    sparse: true,
    index: true
  },
  razorpayOrderId: {
    type: String,
    sparse: true
  },
  razorpayPaymentId: {
    type: String,
    sparse: true
  },
  razorpaySignature: {
    type: String,
    sparse: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  rideId: {
    type: Schema.Types.ObjectId,
    ref: 'Ride',
    sparse: true,
    index: true
  },
  walletId: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    sparse: true,
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    required: true,
    default: 'INR',
    enum: ['INR', 'USD']
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: ['card', 'upi', 'netbanking', 'wallet', 'emi', 'bnpl'],
    index: true
  },
  paymentProvider: {
    type: String,
    required: true,
    enum: ['razorpay', 'stripe', 'paytm', 'phonepe'],
    default: 'razorpay'
  },
  status: {
    type: String,
    required: true,
    enum: ['created', 'pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'created',
    index: true
  },
  failureReason: {
    type: String,
    default: null
  },
  refundId: {
    type: String,
    default: null
  },
  refundAmount: {
    type: Number,
    default: 0,
    min: 0
  },
  refundStatus: {
    type: String,
    enum: ['pending', 'processed', 'failed'],
    default: null
  },
  metadata: {
    customerEmail: String,
    customerPhone: String,
    description: String,
    notes: {
      type: Schema.Types.Mixed,
      default: {}
    }
  },
  fees: {
    platformFee: {
      type: Number,
      default: 0,
      min: 0
    },
    paymentGatewayFee: {
      type: Number,
      default: 0,
      min: 0
    },
    gst: {
      type: Number,
      default: 0,
      min: 0
    },
    total: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  settlement: {
    settlementId: String,
    settledAt: Date,
    settledAmount: Number
  },
  webhookEvents: [{
    eventType: {
      type: String,
      required: true
    },
    eventData: {
      type: Schema.Types.Mixed,
      required: true
    },
    receivedAt: {
      type: Date,
      default: Date.now
    }
  }],
  completedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
PaymentSchema.index({ userId: 1, status: 1 });
PaymentSchema.index({ rideId: 1 });
PaymentSchema.index({ status: 1, createdAt: -1 });
PaymentSchema.index({ paymentMethod: 1, status: 1 });
PaymentSchema.index({ createdAt: -1 });

// Virtual for formatted amount
PaymentSchema.virtual('formattedAmount').get(function() {
  return `₹${this.amount.toFixed(2)}`;
});

// Virtual for net amount (after fees)
PaymentSchema.virtual('netAmount').get(function() {
  return this.amount - this.fees.total;
});

// Methods
PaymentSchema.methods.isSuccessful = function(): boolean {
  return this.status === 'completed';
};

PaymentSchema.methods.canRefund = function(): boolean {
  return this.status === 'completed' && !this.refundId;
};

PaymentSchema.methods.addWebhookEvent = function(eventType: string, eventData: any) {
  this.webhookEvents.push({
    eventType,
    eventData,
    receivedAt: new Date()
  });
};

export const Payment = mongoose.models.Payment || mongoose.model<IPayment>('Payment', PaymentSchema);
