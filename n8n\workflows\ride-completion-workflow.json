{"name": "Ride Completion & Rewards", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ride-completed", "responseMode": "responseNode"}, "id": "webhook-ride-completed", "name": "Ride Completed Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"url": "http://mcp-server:8080/rewards/award", "sendBody": true, "bodyParameters": {"parameters": [{"name": "userId", "value": "={{ $json.rideData.userId }}"}, {"name": "points", "value": 10}, {"name": "reason", "value": "Ride completion"}, {"name": "type", "value": "ride_completion"}, {"name": "metadata", "value": "={{ {\n  \"rideId\": $json.rideData.rideId,\n  \"distance\": $json.rideData.distance,\n  \"fare\": $json.rideData.fare\n} }}"}]}}, "id": "award-user-points", "name": "Award User Points", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 200]}, {"parameters": {"url": "http://mcp-server:8080/rewards/award", "sendBody": true, "bodyParameters": {"parameters": [{"name": "userId", "value": "={{ $json.rideData.driverId }}"}, {"name": "points", "value": "={{ Math.round($json.rideData.driverEarnings * 0.1) }}"}, {"name": "reason", "value": "Driver ride completion"}, {"name": "type", "value": "driver_completion"}, {"name": "metadata", "value": "={{ {\n  \"rideId\": $json.rideData.rideId,\n  \"earnings\": $json.rideData.driverEarnings,\n  \"rating\": $json.rideData.rating\n} }}"}]}}, "id": "award-driver-points", "name": "Award Driver Points", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [460, 400]}, {"parameters": {"url": "http://mcp-server:8080/notifications/send", "sendBody": true, "bodyParameters": {"parameters": [{"name": "userId", "value": "={{ $json.rideData.userId }}"}, {"name": "type", "value": "ride_completed"}, {"name": "title", "value": "Ride Completed Successfully!"}, {"name": "message", "value": "Your ride has been completed. You earned 10 points!"}, {"name": "data", "value": "={{ {\n  \"rideId\": $json.rideData.rideId,\n  \"pointsEarned\": 10,\n  \"fare\": $json.rideData.fare\n} }}"}]}}, "id": "notify-user-completion", "name": "Notify User Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 200]}, {"parameters": {"url": "http://mcp-server:8080/notifications/send", "sendBody": true, "bodyParameters": {"parameters": [{"name": "userId", "value": "={{ $json.rideData.driverId }}"}, {"name": "type", "value": "driver_ride_completed"}, {"name": "title", "value": "Ride Completed - Payment Received"}, {"name": "message", "value": "Ride completed successfully. Earnings: ₹{{ $('Ride Completed Webhook').item.json.rideData.driverEarnings }}"}, {"name": "data", "value": "={{ {\n  \"rideId\": $json.rideData.rideId,\n  \"earnings\": $json.rideData.driverEarnings,\n  \"pointsEarned\": Math.round($json.rideData.driverEarnings * 0.1)\n} }}"}]}}, "id": "notify-driver-completion", "name": "Notify Driver Completion", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 400]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-rating", "leftValue": "={{ $json.rideData.rating }}", "rightValue": 4.5, "operator": {"type": "number", "operation": "gte"}}], "combinator": "and"}, "options": {}}, "id": "check-high-rating", "name": "Check High Rating", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"url": "http://mcp-server:8080/rewards/award", "sendBody": true, "bodyParameters": {"parameters": [{"name": "userId", "value": "={{ $('Ride Completed Webhook').item.json.rideData.driverId }}"}, {"name": "points", "value": 5}, {"name": "reason", "value": "High rating bonus ({{ $('Ride Completed Webhook').item.json.rideData.rating }}★)"}, {"name": "type", "value": "rating_bonus"}, {"name": "metadata", "value": "={{ {\n  \"rideId\": $('Ride Completed Webhook').item.json.rideData.rideId,\n  \"rating\": $('Ride Completed Webhook').item.json.rideData.rating\n} }}"}]}}, "id": "award-rating-bonus", "name": "Award Rating Bonus", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 200]}, {"parameters": {"url": "http://app:3000/api/rides/{{ $('Ride Completed Webhook').item.json.rideData.rideId }}", "sendBody": true, "requestMethod": "PATCH", "bodyParameters": {"parameters": [{"name": "status", "value": "completed"}, {"name": "completedAt", "value": "={{ $now }}"}, {"name": "finalFare", "value": "={{ $('Ride Completed Webhook').item.json.rideData.fare }}"}, {"name": "rating", "value": "={{ $('Ride Completed Webhook').item.json.rideData.rating }}"}, {"name": "feedback", "value": "={{ $('Ride Completed Webhook').item.json.rideData.feedback }}"}]}}, "id": "update-ride-status", "name": "Update Ride Status", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1120, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Ride completion processed successfully\",\n  \"userPointsAwarded\": 10,\n  \"driverPointsAwarded\": Math.round($('Ride Completed Webhook').item.json.rideData.driverEarnings * 0.1),\n  \"ratingBonus\": $('Ride Completed Webhook').item.json.rideData.rating >= 4.5 ? 5 : 0,\n  \"rideId\": $('Ride Completed Webhook').item.json.rideData.rideId\n} }}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}], "connections": {"Ride Completed Webhook": {"main": [[{"node": "Award User Points", "type": "main", "index": 0}, {"node": "Award Driver Points", "type": "main", "index": 0}]]}, "Award User Points": {"main": [[{"node": "Notify User Completion", "type": "main", "index": 0}]]}, "Award Driver Points": {"main": [[{"node": "Notify Driver Completion", "type": "main", "index": 0}]]}, "Notify User Completion": {"main": [[{"node": "Check High Rating", "type": "main", "index": 0}]]}, "Notify Driver Completion": {"main": [[{"node": "Check High Rating", "type": "main", "index": 0}]]}, "Check High Rating": {"main": [[{"node": "Award Rating Bonus", "type": "main", "index": 0}], [{"node": "Update Ride Status", "type": "main", "index": 0}]]}, "Award Rating Bonus": {"main": [[{"node": "Update Ride Status", "type": "main", "index": 0}]]}, "Update Ride Status": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "ride-completion-workflow", "tags": []}