'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { LoadingSpinner, RideLoading } from '@/components/ui/loading';
import { 
  MapPin, 
  Clock, 
  CreditCard, 
  ArrowRight, 
  Zap, 
  Star,
  Navigation,
  Phone,
  MessageCircle
} from 'lucide-react';

interface MobileRideBookingProps {
  onBookRide?: (data: any) => void;
}

export default function MobileRideBooking({ onBookRide }: MobileRideBookingProps) {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    pickup: '',
    destination: '',
    rideType: 'standard',
    paymentMethod: 'cash'
  });

  const steps = [
    { id: 1, title: 'Pickup Location', icon: MapPin },
    { id: 2, title: 'Destination', icon: Navigation },
    { id: 3, title: 'Ride Options', icon: Zap },
    { id: 4, title: 'Confirmation', icon: CreditCard }
  ];

  const handleNext = () => {
    if (step < 4) {
      setStep(step + 1);
    } else {
      handleBookRide();
    }
  };

  const handleBookRide = async () => {
    setLoading(true);
    // Simulate booking process
    setTimeout(() => {
      setLoading(false);
      onBookRide?.(formData);
    }, 2000);
  };

  if (loading) {
    return <RideLoading message="Booking your ride..." className="min-h-screen" />;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Progress Header */}
      <div className="bg-white shadow-sm border-b p-4">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold">Book a Ride</h1>
          <Badge variant="outline">{step}/4</Badge>
        </div>
        <div className="flex space-x-2">
          {steps.map((s) => (
            <div
              key={s.id}
              className={`flex-1 h-2 rounded-full transition-all duration-300 ${
                s.id <= step ? 'bg-gradient-primary' : 'bg-gray-200'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="flex-1 p-4 space-y-4">
        {step === 1 && (
          <Card className="shadow-soft border-0">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto bg-gradient-primary w-16 h-16 flex items-center justify-center rounded-2xl mb-4">
                <MapPin className="h-8 w-8 text-white" />
              </div>
              <CardTitle>Where are you?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Enter pickup location"
                  value={formData.pickup}
                  onChange={(e) => setFormData({...formData, pickup: e.target.value})}
                  className="pl-10 h-12 text-lg"
                />
              </div>
              <Button 
                onClick={() => {/* Get current location */}} 
                variant="outline" 
                className="w-full h-12"
              >
                <Navigation className="h-5 w-5 mr-2" />
                Use Current Location
              </Button>
            </CardContent>
          </Card>
        )}

        {step === 2 && (
          <Card className="shadow-soft border-0">
            <CardHeader className="text-center pb-4">
              <div className="mx-auto bg-gradient-success w-16 h-16 flex items-center justify-center rounded-2xl mb-4">
                <Navigation className="h-8 w-8 text-white" />
              </div>
              <CardTitle>Where to?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <Input
                  placeholder="Enter destination"
                  value={formData.destination}
                  onChange={(e) => setFormData({...formData, destination: e.target.value})}
                  className="pl-10 h-12 text-lg"
                />
              </div>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-2">Recent destinations</p>
                <div className="space-y-2">
                  {['Home', 'Office', 'Mall'].map((place) => (
                    <Button
                      key={place}
                      variant="ghost"
                      className="w-full justify-start h-10"
                      onClick={() => setFormData({...formData, destination: place})}
                    >
                      <Clock className="h-4 w-4 mr-2" />
                      {place}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {step === 3 && (
          <div className="space-y-4">
            <Card className="shadow-soft border-0">
              <CardHeader>
                <CardTitle>Choose Ride Type</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { id: 'standard', name: 'Standard', price: '₹45', time: '5 min', icon: Zap },
                  { id: 'premium', name: 'Premium', price: '₹65', time: '3 min', icon: Star }
                ].map((ride) => (
                  <div
                    key={ride.id}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      formData.rideType === ride.id 
                        ? 'border-primary bg-primary/5' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setFormData({...formData, rideType: ride.id})}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <ride.icon className="h-6 w-6 text-primary" />
                        <div>
                          <p className="font-medium">{ride.name}</p>
                          <p className="text-sm text-gray-500">{ride.time} away</p>
                        </div>
                      </div>
                      <p className="font-bold text-lg">{ride.price}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card className="shadow-soft border-0">
              <CardHeader>
                <CardTitle>Payment Method</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { id: 'cash', name: 'Cash', icon: CreditCard },
                  { id: 'wallet', name: 'Wallet', icon: CreditCard }
                ].map((payment) => (
                  <div
                    key={payment.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-all ${
                      formData.paymentMethod === payment.id 
                        ? 'border-primary bg-primary/5' 
                        : 'border-gray-200'
                    }`}
                    onClick={() => setFormData({...formData, paymentMethod: payment.id})}
                  >
                    <div className="flex items-center space-x-3">
                      <payment.icon className="h-5 w-5 text-primary" />
                      <span className="font-medium">{payment.name}</span>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        )}

        {step === 4 && (
          <Card className="shadow-soft border-0">
            <CardHeader className="text-center">
              <CardTitle>Confirm Your Ride</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">Pickup</p>
                    <p className="text-sm text-gray-600">{formData.pickup}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-red-500" />
                  <div>
                    <p className="font-medium">Destination</p>
                    <p className="text-sm text-gray-600">{formData.destination}</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-between items-center p-4 bg-primary/5 rounded-lg">
                <span className="font-medium">Total Fare</span>
                <span className="text-2xl font-bold text-primary">₹45</span>
              </div>

              <div className="text-center text-sm text-gray-600">
                <p>Your driver will arrive in 5 minutes</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Bottom Action */}
      <div className="p-4 bg-white border-t">
        <div className="flex space-x-3">
          {step > 1 && (
            <Button 
              variant="outline" 
              onClick={() => setStep(step - 1)}
              className="flex-1 h-12"
            >
              Back
            </Button>
          )}
          <Button 
            onClick={handleNext}
            disabled={
              (step === 1 && !formData.pickup) ||
              (step === 2 && !formData.destination)
            }
            className="flex-1 h-12 bg-gradient-primary"
          >
            {step === 4 ? 'Book Ride' : 'Next'}
            <ArrowRight className="h-5 w-5 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}
