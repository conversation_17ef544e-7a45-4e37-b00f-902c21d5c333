import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';
import { Ride } from '@/lib/models/Ride';
import { authenticateRequest } from '@/lib/auth';

interface DriverMatch {
  driverId: string;
  driver: {
    _id: string;
    firstName: string;
    lastName: string;
    phone: string;
    profileImage?: string;
    driverProfile: {
      rating: number;
      totalRides: number;
      vehicleModel?: string;
      vehicleColor?: string;
    };
  };
  distance: number;
  estimatedArrival: number;
}

// Calculate distance between two coordinates (Haversine formula)
function calculateDistance(
  lat1: number, 
  lon1: number, 
  lat2: number, 
  lon2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// Mock driver locations (in production, this would come from real-time location tracking)
const mockDriverLocations: { [key: string]: { latitude: number; longitude: number; isAvailable: boolean } } = {};

// Initialize some mock driver locations
function initializeMockDrivers() {
  // This would be replaced with real-time driver location data
  const baseLocations = [
    { latitude: 19.1136, longitude: 72.8697 }, // Andheri
    { latitude: 19.0596, longitude: 72.8295 }, // Bandra
    { latitude: 19.0760, longitude: 72.8777 }, // Mumbai Central
    { latitude: 19.0330, longitude: 72.8570 }, // Worli
    { latitude: 19.1075, longitude: 72.8263 }, // Juhu
  ];

  return baseLocations.map((loc, index) => ({
    ...loc,
    latitude: loc.latitude + (Math.random() - 0.5) * 0.02,
    longitude: loc.longitude + (Math.random() - 0.5) * 0.02,
    isAvailable: Math.random() > 0.3, // 70% chance of being available
  }));
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Authenticate request
    const user = await authenticateRequest(request);
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (user.role !== 'rider') {
      return NextResponse.json(
        { error: 'Only riders can search for drivers' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { rideId, pickupCoordinates, maxDistance = 10 } = body;

    if (!rideId || !pickupCoordinates) {
      return NextResponse.json(
        { error: 'Ride ID and pickup coordinates are required' },
        { status: 400 }
      );
    }

    // Verify the ride exists and belongs to the user
    const ride = await Ride.findOne({ 
      _id: rideId, 
      riderId: user.userId,
      status: 'pending'
    });

    if (!ride) {
      return NextResponse.json(
        { error: 'Ride not found or not in pending status' },
        { status: 404 }
      );
    }

    // Find available drivers within the specified radius
    const availableDrivers = await User.find({
      role: 'driver',
      isActive: true,
      'driverProfile.isApproved': true,
    }).select('firstName lastName phone profileImage driverProfile');

    // Initialize mock locations for demo
    const mockLocations = initializeMockDrivers();
    
    // Calculate distances and filter drivers
    const nearbyDrivers: DriverMatch[] = [];

    availableDrivers.forEach((driver, index) => {
      // In production, get real driver location from location tracking service
      const driverLocation = mockLocations[index % mockLocations.length];
      
      if (!driverLocation?.isAvailable) return;

      const distance = calculateDistance(
        pickupCoordinates.latitude,
        pickupCoordinates.longitude,
        driverLocation.latitude,
        driverLocation.longitude
      );

      if (distance <= maxDistance) {
        // Estimate arrival time (assuming average speed of 25 km/h in city)
        const estimatedArrival = Math.round((distance / 25) * 60); // minutes

        nearbyDrivers.push({
          driverId: driver._id.toString(),
          driver: {
            _id: driver._id.toString(),
            firstName: driver.firstName,
            lastName: driver.lastName,
            phone: driver.phone,
            profileImage: driver.profileImage,
            driverProfile: {
              rating: driver.driverProfile?.rating || 5.0,
              totalRides: driver.driverProfile?.totalRides || 0,
              vehicleModel: driver.driverProfile?.vehicleModel,
              vehicleColor: driver.driverProfile?.vehicleColor,
            },
          },
          distance: Math.round(distance * 10) / 10,
          estimatedArrival,
        });
      }
    });

    // Sort by distance and rating
    nearbyDrivers.sort((a, b) => {
      // Prioritize by distance first, then by rating
      const distanceDiff = a.distance - b.distance;
      if (Math.abs(distanceDiff) < 1) {
        return b.driver.driverProfile.rating - a.driver.driverProfile.rating;
      }
      return distanceDiff;
    });

    // Limit to top 10 drivers
    const topDrivers = nearbyDrivers.slice(0, 10);

    return NextResponse.json({
      message: 'Drivers found successfully',
      drivers: topDrivers,
      totalFound: topDrivers.length,
      searchRadius: maxDistance,
    });

  } catch (error) {
    console.error('Find drivers error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
