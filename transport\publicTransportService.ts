// Public Transport Integration Service
import { apmService } from '../monitoring/apm';
import { Location } from '../ai/routeOptimization';

export interface TransportMode {
  id: string;
  type: 'bus' | 'train' | 'metro' | 'tram' | 'ferry' | 'rideshare' | 'walk' | 'bike';
  name: string;
  operator: string;
  color?: string;
  icon?: string;
  accessibility: {
    wheelchairAccessible: boolean;
    audioAnnouncements: boolean;
    visualAnnouncements: boolean;
  };
}

export interface TransportStop {
  id: string;
  name: string;
  location: Location;
  modes: string[]; // Transport mode IDs
  facilities: {
    parking: boolean;
    bikeRacks: boolean;
    shelter: boolean;
    ticketing: boolean;
    wifi: boolean;
    restrooms: boolean;
  };
  accessibility: {
    wheelchairAccessible: boolean;
    elevators: boolean;
    tactilePaving: boolean;
  };
}

export interface TransportRoute {
  id: string;
  modeId: string;
  routeNumber: string;
  routeName: string;
  direction: string;
  stops: string[]; // Stop IDs in order
  operatingHours: {
    start: string; // HH:MM format
    end: string;
    days: number[]; // 0-6, Sunday=0
  };
  frequency: {
    peakMinutes: number;
    offPeakMinutes: number;
    weekendMinutes: number;
  };
}

export interface RealTimeUpdate {
  routeId: string;
  stopId: string;
  scheduledTime: Date;
  estimatedTime: Date;
  delay: number; // in minutes
  status: 'on_time' | 'delayed' | 'cancelled' | 'early';
  crowding: 'low' | 'medium' | 'high' | 'full';
  accessibility: {
    wheelchairSpace: boolean;
    audioWorking: boolean;
  };
}

export interface JourneySegment {
  mode: TransportMode;
  route?: TransportRoute;
  fromStop?: TransportStop;
  toStop?: TransportStop;
  fromLocation: Location;
  toLocation: Location;
  startTime: Date;
  endTime: Date;
  duration: number; // in minutes
  distance: number; // in km
  cost: number; // in local currency
  instructions: string[];
  realTimeUpdates?: RealTimeUpdate[];
  carbonFootprint: number; // in kg CO2
}

export interface MultiModalJourney {
  id: string;
  fromLocation: Location;
  toLocation: Location;
  segments: JourneySegment[];
  totalDuration: number;
  totalDistance: number;
  totalCost: number;
  totalCarbonFootprint: number;
  walkingDistance: number;
  accessibility: {
    wheelchairAccessible: boolean;
    difficulty: 'easy' | 'moderate' | 'difficult';
    elevatorRequired: boolean;
  };
  alternatives: MultiModalJourney[];
  confidence: number; // 0-1 reliability score
  lastUpdated: Date;
}

export interface JourneyPlanRequest {
  from: Location;
  to: Location;
  departureTime?: Date;
  arrivalTime?: Date;
  preferences: {
    modes: string[]; // Preferred transport mode IDs
    maxWalkingDistance: number; // in km
    maxTransfers: number;
    accessibility: boolean;
    avoidModes?: string[];
    prioritize: 'time' | 'cost' | 'comfort' | 'environment';
  };
  userId?: string;
}

class PublicTransportService {
  private transportModes: Map<string, TransportMode> = new Map();
  private transportStops: Map<string, TransportStop> = new Map();
  private transportRoutes: Map<string, TransportRoute> = new Map();
  private realTimeCache: Map<string, RealTimeUpdate[]> = new Map();
  private journeyCache: Map<string, MultiModalJourney> = new Map();
  private cacheExpiry: number = 2 * 60 * 1000; // 2 minutes

  constructor() {
    this.initializeTransportData();
    this.startRealTimeUpdates();
  }

  /**
   * Plan multi-modal journey between two locations
   */
  async planJourney(request: JourneyPlanRequest): Promise<MultiModalJourney> {
    const timer = apmService.startTimer('journey_planning');
    
    try {
      // Check cache first
      const cacheKey = this.generateJourneyCacheKey(request);
      const cachedJourney = this.getCachedJourney(cacheKey);
      if (cachedJourney) {
        timer.end(true);
        return cachedJourney;
      }

      // Find nearby stops for origin and destination
      const originStops = await this.findNearbyStops(request.from, request.preferences.maxWalkingDistance);
      const destinationStops = await this.findNearbyStops(request.to, request.preferences.maxWalkingDistance);

      // Generate journey options
      const journeyOptions = await this.generateJourneyOptions(
        request,
        originStops,
        destinationStops
      );

      // Score and rank journeys
      const rankedJourneys = this.rankJourneys(journeyOptions, request.preferences);

      // Get real-time updates for best journey
      const bestJourney = rankedJourneys[0];
      await this.addRealTimeUpdates(bestJourney);

      // Add alternatives
      bestJourney.alternatives = rankedJourneys.slice(1, 4); // Top 3 alternatives

      // Cache the result
      this.cacheJourney(cacheKey, bestJourney);

      // Record metrics
      apmService.recordBusinessMetric({
        event: 'journey_planned',
        value: 1,
        timestamp: Date.now(),
        properties: {
          segments: bestJourney.segments.length,
          duration: bestJourney.totalDuration,
          modes: bestJourney.segments.map(s => s.mode.type).join(','),
        },
      });

      timer.end(true);
      return bestJourney;

    } catch (error) {
      timer.end(false);
      console.error('Journey planning failed:', error);
      throw error;
    }
  }

  /**
   * Get real-time updates for transport routes
   */
  async getRealTimeUpdates(routeIds: string[]): Promise<Map<string, RealTimeUpdate[]>> {
    const updates = new Map<string, RealTimeUpdate[]>();

    for (const routeId of routeIds) {
      try {
        // Check cache first
        const cachedUpdates = this.realTimeCache.get(routeId);
        if (cachedUpdates && this.isCacheValid(routeId)) {
          updates.set(routeId, cachedUpdates);
          continue;
        }

        // Fetch real-time data from transport API
        const realTimeData = await this.fetchRealTimeData(routeId);
        updates.set(routeId, realTimeData);
        
        // Cache the updates
        this.realTimeCache.set(routeId, realTimeData);

      } catch (error) {
        console.error(`Failed to get real-time updates for route ${routeId}:`, error);
        // Use cached data if available, even if expired
        const cachedUpdates = this.realTimeCache.get(routeId);
        if (cachedUpdates) {
          updates.set(routeId, cachedUpdates);
        }
      }
    }

    return updates;
  }

  /**
   * Find nearby transport stops
   */
  async findNearbyStops(location: Location, maxDistance: number): Promise<TransportStop[]> {
    const nearbyStops: TransportStop[] = [];

    for (const stop of this.transportStops.values()) {
      const distance = this.calculateDistance(location, stop.location);
      if (distance <= maxDistance) {
        nearbyStops.push(stop);
      }
    }

    // Sort by distance
    nearbyStops.sort((a, b) => {
      const distanceA = this.calculateDistance(location, a.location);
      const distanceB = this.calculateDistance(location, b.location);
      return distanceA - distanceB;
    });

    return nearbyStops.slice(0, 10); // Return top 10 closest stops
  }

  /**
   * Get transport mode information
   */
  getTransportMode(modeId: string): TransportMode | undefined {
    return this.transportModes.get(modeId);
  }

  /**
   * Get all available transport modes
   */
  getAllTransportModes(): TransportMode[] {
    return Array.from(this.transportModes.values());
  }

  /**
   * Get transport route information
   */
  getTransportRoute(routeId: string): TransportRoute | undefined {
    return this.transportRoutes.get(routeId);
  }

  /**
   * Initialize transport data (would typically load from database/API)
   */
  private initializeTransportData() {
    // Sample transport modes
    const modes: TransportMode[] = [
      {
        id: 'bus',
        type: 'bus',
        name: 'City Bus',
        operator: 'City Transport Authority',
        color: '#FF6B35',
        accessibility: {
          wheelchairAccessible: true,
          audioAnnouncements: true,
          visualAnnouncements: true,
        },
      },
      {
        id: 'metro',
        type: 'metro',
        name: 'Metro Rail',
        operator: 'Metro Corporation',
        color: '#1E88E5',
        accessibility: {
          wheelchairAccessible: true,
          audioAnnouncements: true,
          visualAnnouncements: true,
        },
      },
      {
        id: 'rideshare',
        type: 'rideshare',
        name: 'RideShare',
        operator: 'Our Platform',
        color: '#4CAF50',
        accessibility: {
          wheelchairAccessible: false,
          audioAnnouncements: false,
          visualAnnouncements: false,
        },
      },
    ];

    modes.forEach(mode => this.transportModes.set(mode.id, mode));

    // Sample transport stops (would load from database)
    const stops: TransportStop[] = [
      {
        id: 'stop_001',
        name: 'Central Station',
        location: { latitude: 12.9716, longitude: 77.5946 },
        modes: ['bus', 'metro'],
        facilities: {
          parking: true,
          bikeRacks: true,
          shelter: true,
          ticketing: true,
          wifi: true,
          restrooms: true,
        },
        accessibility: {
          wheelchairAccessible: true,
          elevators: true,
          tactilePaving: true,
        },
      },
      {
        id: 'stop_002',
        name: 'Tech Park',
        location: { latitude: 12.9784, longitude: 77.6408 },
        modes: ['bus'],
        facilities: {
          parking: false,
          bikeRacks: true,
          shelter: true,
          ticketing: false,
          wifi: false,
          restrooms: false,
        },
        accessibility: {
          wheelchairAccessible: true,
          elevators: false,
          tactilePaving: true,
        },
      },
    ];

    stops.forEach(stop => this.transportStops.set(stop.id, stop));

    // Sample routes (would load from database)
    const routes: TransportRoute[] = [
      {
        id: 'route_001',
        modeId: 'bus',
        routeNumber: '42',
        routeName: 'Central - Tech Park',
        direction: 'Eastbound',
        stops: ['stop_001', 'stop_002'],
        operatingHours: {
          start: '05:00',
          end: '23:00',
          days: [1, 2, 3, 4, 5, 6], // Monday to Saturday
        },
        frequency: {
          peakMinutes: 10,
          offPeakMinutes: 15,
          weekendMinutes: 20,
        },
      },
    ];

    routes.forEach(route => this.transportRoutes.set(route.id, route));
  }

  /**
   * Generate journey options
   */
  private async generateJourneyOptions(
    request: JourneyPlanRequest,
    originStops: TransportStop[],
    destinationStops: TransportStop[]
  ): Promise<MultiModalJourney[]> {
    const journeyOptions: MultiModalJourney[] = [];

    // Direct rideshare option
    const rideshareJourney = await this.createRideshareJourney(request);
    journeyOptions.push(rideshareJourney);

    // Public transport + rideshare combinations
    for (const originStop of originStops.slice(0, 3)) { // Limit to top 3 origin stops
      for (const destStop of destinationStops.slice(0, 3)) { // Limit to top 3 destination stops
        try {
          const publicTransportJourney = await this.createPublicTransportJourney(
            request,
            originStop,
            destStop
          );
          journeyOptions.push(publicTransportJourney);
        } catch (error) {
          console.error('Failed to create public transport journey:', error);
        }
      }
    }

    return journeyOptions;
  }

  /**
   * Create rideshare-only journey
   */
  private async createRideshareJourney(request: JourneyPlanRequest): Promise<MultiModalJourney> {
    const rideshareMode = this.transportModes.get('rideshare')!;
    const distance = this.calculateDistance(request.from, request.to);
    const duration = (distance / 25) * 60; // Assume 25 km/h average speed
    const cost = 30 + (distance * 10); // Base fare + per km rate

    const segment: JourneySegment = {
      mode: rideshareMode,
      fromLocation: request.from,
      toLocation: request.to,
      startTime: request.departureTime || new Date(),
      endTime: new Date((request.departureTime || new Date()).getTime() + duration * 60 * 1000),
      duration,
      distance,
      cost,
      instructions: ['Book a ride to your destination'],
      carbonFootprint: distance * 0.12, // kg CO2 per km
    };

    return {
      id: `journey_rideshare_${Date.now()}`,
      fromLocation: request.from,
      toLocation: request.to,
      segments: [segment],
      totalDuration: duration,
      totalDistance: distance,
      totalCost: cost,
      totalCarbonFootprint: segment.carbonFootprint,
      walkingDistance: 0,
      accessibility: {
        wheelchairAccessible: false,
        difficulty: 'easy',
        elevatorRequired: false,
      },
      alternatives: [],
      confidence: 0.9,
      lastUpdated: new Date(),
    };
  }

  /**
   * Create public transport journey
   */
  private async createPublicTransportJourney(
    request: JourneyPlanRequest,
    originStop: TransportStop,
    destStop: TransportStop
  ): Promise<MultiModalJourney> {
    const segments: JourneySegment[] = [];
    let totalCost = 0;
    let totalDuration = 0;
    let totalDistance = 0;
    let totalCarbonFootprint = 0;

    // Walking segment to origin stop
    const walkToStop = this.createWalkingSegment(
      request.from,
      originStop.location,
      request.departureTime || new Date()
    );
    segments.push(walkToStop);
    totalDuration += walkToStop.duration;
    totalDistance += walkToStop.distance;

    // Public transport segment
    const transitSegment = this.createTransitSegment(
      originStop,
      destStop,
      new Date(walkToStop.endTime.getTime())
    );
    segments.push(transitSegment);
    totalCost += transitSegment.cost;
    totalDuration += transitSegment.duration;
    totalDistance += transitSegment.distance;
    totalCarbonFootprint += transitSegment.carbonFootprint;

    // Walking segment from destination stop
    const walkFromStop = this.createWalkingSegment(
      destStop.location,
      request.to,
      transitSegment.endTime
    );
    segments.push(walkFromStop);
    totalDuration += walkFromStop.duration;
    totalDistance += walkFromStop.distance;

    const walkingDistance = walkToStop.distance + walkFromStop.distance;

    return {
      id: `journey_transit_${Date.now()}`,
      fromLocation: request.from,
      toLocation: request.to,
      segments,
      totalDuration,
      totalDistance,
      totalCost,
      totalCarbonFootprint,
      walkingDistance,
      accessibility: {
        wheelchairAccessible: originStop.accessibility.wheelchairAccessible && 
                             destStop.accessibility.wheelchairAccessible,
        difficulty: walkingDistance > 1 ? 'moderate' : 'easy',
        elevatorRequired: originStop.accessibility.elevators || destStop.accessibility.elevators,
      },
      alternatives: [],
      confidence: 0.8,
      lastUpdated: new Date(),
    };
  }

  /**
   * Create walking segment
   */
  private createWalkingSegment(from: Location, to: Location, startTime: Date): JourneySegment {
    const distance = this.calculateDistance(from, to);
    const duration = (distance / 5) * 60; // 5 km/h walking speed
    
    return {
      mode: {
        id: 'walk',
        type: 'walk',
        name: 'Walking',
        operator: 'Self',
        accessibility: {
          wheelchairAccessible: true,
          audioAnnouncements: false,
          visualAnnouncements: false,
        },
      },
      fromLocation: from,
      toLocation: to,
      startTime,
      endTime: new Date(startTime.getTime() + duration * 60 * 1000),
      duration,
      distance,
      cost: 0,
      instructions: [`Walk ${(distance * 1000).toFixed(0)}m to destination`],
      carbonFootprint: 0,
    };
  }

  /**
   * Create transit segment
   */
  private createTransitSegment(
    fromStop: TransportStop,
    toStop: TransportStop,
    startTime: Date
  ): JourneySegment {
    // Find a route that connects these stops
    const route = Array.from(this.transportRoutes.values()).find(r => 
      r.stops.includes(fromStop.id) && r.stops.includes(toStop.id)
    );

    if (!route) {
      throw new Error('No route found between stops');
    }

    const mode = this.transportModes.get(route.modeId)!;
    const distance = this.calculateDistance(fromStop.location, toStop.location);
    const duration = 15; // Simplified - would calculate based on route schedule
    const cost = 25; // Fixed transit fare

    return {
      mode,
      route,
      fromStop,
      toStop,
      fromLocation: fromStop.location,
      toLocation: toStop.location,
      startTime,
      endTime: new Date(startTime.getTime() + duration * 60 * 1000),
      duration,
      distance,
      cost,
      instructions: [
        `Take ${route.routeNumber} ${mode.name} from ${fromStop.name}`,
        `Ride for ${duration} minutes`,
        `Get off at ${toStop.name}`,
      ],
      carbonFootprint: distance * 0.05, // Lower emissions for public transport
    };
  }

  /**
   * Rank journeys based on preferences
   */
  private rankJourneys(journeys: MultiModalJourney[], preferences: any): MultiModalJourney[] {
    return journeys.sort((a, b) => {
      let scoreA = 0;
      let scoreB = 0;

      switch (preferences.prioritize) {
        case 'time':
          scoreA = 1 / a.totalDuration;
          scoreB = 1 / b.totalDuration;
          break;
        case 'cost':
          scoreA = 1 / (a.totalCost + 1);
          scoreB = 1 / (b.totalCost + 1);
          break;
        case 'environment':
          scoreA = 1 / (a.totalCarbonFootprint + 0.1);
          scoreB = 1 / (b.totalCarbonFootprint + 0.1);
          break;
        default: // balanced
          scoreA = (1 / a.totalDuration) * 0.4 + (1 / (a.totalCost + 1)) * 0.3 + (1 / (a.totalCarbonFootprint + 0.1)) * 0.3;
          scoreB = (1 / b.totalDuration) * 0.4 + (1 / (b.totalCost + 1)) * 0.3 + (1 / (b.totalCarbonFootprint + 0.1)) * 0.3;
      }

      return scoreB - scoreA;
    });
  }

  /**
   * Add real-time updates to journey
   */
  private async addRealTimeUpdates(journey: MultiModalJourney) {
    for (const segment of journey.segments) {
      if (segment.route) {
        try {
          const updates = await this.getRealTimeUpdates([segment.route.id]);
          segment.realTimeUpdates = updates.get(segment.route.id) || [];
        } catch (error) {
          console.error('Failed to get real-time updates:', error);
        }
      }
    }
  }

  /**
   * Utility methods
   */
  private calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Earth's radius in km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private generateJourneyCacheKey(request: JourneyPlanRequest): string {
    const key = `${request.from.latitude},${request.from.longitude}-${request.to.latitude},${request.to.longitude}-${request.preferences.prioritize}-${Math.floor(Date.now() / 120000)}`;
    return Buffer.from(key).toString('base64');
  }

  private getCachedJourney(key: string): MultiModalJourney | null {
    const cached = this.journeyCache.get(key);
    if (cached && Date.now() - cached.lastUpdated.getTime() < this.cacheExpiry) {
      return cached;
    }
    return null;
  }

  private cacheJourney(key: string, journey: MultiModalJourney): void {
    this.journeyCache.set(key, journey);
    
    // Clean up old cache entries
    if (this.journeyCache.size > 100) {
      const oldestKey = this.journeyCache.keys().next().value;
      this.journeyCache.delete(oldestKey);
    }
  }

  private isCacheValid(routeId: string): boolean {
    // Real-time data expires after 1 minute
    return Date.now() - (this.realTimeCache.get(routeId)?.[0]?.scheduledTime.getTime() || 0) < 60000;
  }

  private async fetchRealTimeData(routeId: string): Promise<RealTimeUpdate[]> {
    // This would integrate with real transport APIs
    // For now, return mock data
    return [
      {
        routeId,
        stopId: 'stop_001',
        scheduledTime: new Date(Date.now() + 5 * 60 * 1000),
        estimatedTime: new Date(Date.now() + 7 * 60 * 1000),
        delay: 2,
        status: 'delayed',
        crowding: 'medium',
        accessibility: {
          wheelchairSpace: true,
          audioWorking: true,
        },
      },
    ];
  }

  private startRealTimeUpdates() {
    // Start periodic real-time data updates
    setInterval(() => {
      // Update real-time data for active routes
      this.updateRealTimeData();
    }, 30000); // Every 30 seconds
  }

  private async updateRealTimeData() {
    // Update real-time data for cached routes
    for (const routeId of this.realTimeCache.keys()) {
      try {
        const updates = await this.fetchRealTimeData(routeId);
        this.realTimeCache.set(routeId, updates);
      } catch (error) {
        console.error(`Failed to update real-time data for route ${routeId}:`, error);
      }
    }
  }
}

// Export singleton instance
export const publicTransportService = new PublicTransportService();
export default publicTransportService;
