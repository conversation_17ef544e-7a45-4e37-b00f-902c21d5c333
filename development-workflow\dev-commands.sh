#!/bin/bash

# Development Workflow Commands for AI-Powered Two-Wheeler Sharing Platform
# This script provides convenient commands for common development tasks

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FEATURE_FLAGS_CONFIG="$SCRIPT_DIR/feature-flags-config.json"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_deps=()
    
    # Check for required commands
    for cmd in python3 git curl jq docker; do
        if ! command -v $cmd &> /dev/null; then
            missing_deps+=($cmd)
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install the missing dependencies and try again"
        exit 1
    fi
    
    log_success "All dependencies are installed"
}

# Setup development environment
setup_dev_env() {
    log_info "Setting up development environment..."
    
    # Install Python dependencies
    if [ -f "$SCRIPT_DIR/requirements.txt" ]; then
        log_info "Installing Python dependencies..."
        pip install -r "$SCRIPT_DIR/requirements.txt"
    fi
    
    # Install Node.js dependencies
    if [ -f "$PROJECT_ROOT/package.json" ]; then
        log_info "Installing Node.js dependencies..."
        cd "$PROJECT_ROOT" && npm install
    fi
    
    # Setup pre-commit hooks
    if command -v pre-commit &> /dev/null; then
        log_info "Setting up pre-commit hooks..."
        pre-commit install
    fi
    
    log_success "Development environment setup complete"
}

# Feature flag management
update_feature_flag() {
    local flag_name="$1"
    local enabled="$2"
    local environment="${3:-all}"
    
    if [ -z "$flag_name" ] || [ -z "$enabled" ]; then
        log_error "Usage: update_feature_flag <flag_name> <true|false> [environment]"
        return 1
    fi
    
    log_info "Updating feature flag: $flag_name to $enabled for $environment"
    
    python3 "$SCRIPT_DIR/dev-workflow.py" update-flag \
        --flag "$flag_name" \
        --enabled "$enabled" \
        --environment "$environment"
    
    if [ $? -eq 0 ]; then
        log_success "Feature flag updated successfully"
    else
        log_error "Failed to update feature flag"
        return 1
    fi
}

# List all feature flags
list_feature_flags() {
    log_info "Current feature flags configuration:"
    
    if [ -f "$FEATURE_FLAGS_CONFIG" ]; then
        jq -r '.feature_flags | to_entries[] | "\(.key): \(.value.enabled) (\(.value.environments | to_entries[] | "\(.key):\(.value)") | join(", "))"' "$FEATURE_FLAGS_CONFIG"
    else
        log_error "Feature flags configuration file not found"
        return 1
    fi
}

# Create a new feature branch
create_feature_branch() {
    local issue_number="$1"
    local description="$2"
    
    if [ -z "$issue_number" ]; then
        log_error "Usage: create_feature_branch <issue_number> [description]"
        return 1
    fi
    
    local branch_name="feature/issue-$issue_number"
    if [ -n "$description" ]; then
        # Convert description to branch-friendly format
        local desc_slug=$(echo "$description" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')
        branch_name="feature/issue-$issue_number-$desc_slug"
    fi
    
    log_info "Creating feature branch: $branch_name"
    
    # Ensure we're on main branch and up to date
    git checkout main
    git pull origin main
    
    # Create and checkout new branch
    git checkout -b "$branch_name"
    
    log_success "Feature branch created: $branch_name"
}

# Run comprehensive tests
run_tests() {
    local test_type="${1:-all}"
    
    log_info "Running tests: $test_type"
    
    case $test_type in
        "unit")
            log_info "Running unit tests..."
            python3 -m pytest tests/ -v --cov=.
            ;;
        "integration")
            log_info "Running integration tests..."
            python3 -m pytest tests/integration/ -v
            ;;
        "e2e")
            log_info "Running end-to-end tests..."
            python3 -m pytest tests/e2e/ -v
            ;;
        "performance")
            log_info "Running performance tests..."
            locust -f tests/performance/locustfile.py --headless -u 10 -r 2 -t 30s --host http://localhost:3000
            ;;
        "all")
            log_info "Running all tests..."
            python3 -m pytest tests/ -v --cov=. --cov-report=html
            ;;
        *)
            log_error "Unknown test type: $test_type"
            log_info "Available types: unit, integration, e2e, performance, all"
            return 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        log_success "Tests completed successfully"
    else
        log_error "Tests failed"
        return 1
    fi
}

# Code quality checks
run_quality_checks() {
    log_info "Running code quality checks..."
    
    # Python code formatting
    log_info "Checking Python code formatting..."
    black --check .
    
    # Python linting
    log_info "Running Python linting..."
    flake8 .
    
    # Type checking
    log_info "Running type checking..."
    mypy . --ignore-missing-imports
    
    # Security scanning
    log_info "Running security scan..."
    bandit -r . -f json -o security-report.json
    
    # JavaScript/TypeScript linting (if applicable)
    if [ -f "$PROJECT_ROOT/package.json" ]; then
        log_info "Running JavaScript/TypeScript linting..."
        cd "$PROJECT_ROOT"
        npm run lint
        npm run type-check
    fi
    
    log_success "Code quality checks completed"
}

# Generate documentation
generate_docs() {
    log_info "Generating documentation..."
    
    # Generate API documentation
    if command -v sphinx-build &> /dev/null; then
        log_info "Generating Sphinx documentation..."
        sphinx-apidoc -o docs/api .
        cd docs && make html
    fi
    
    # Generate README for new features
    python3 -c "
import os
import subprocess

# Get recent commits
result = subprocess.run(['git', 'log', '--oneline', '-5'], 
                      capture_output=True, text=True)
commits = result.stdout.strip().split('\n')

# Generate feature documentation
readme_content = '''# Recent Development Updates

## Latest Changes
'''
for commit in commits:
    readme_content += f'- {commit}\n'

readme_content += '''

## Development Workflow

### Quick Commands
\`\`\`bash
# Update feature flags
./development-workflow/dev-commands.sh update_flag ai_enhanced_ride_matching true

# Run tests
./development-workflow/dev-commands.sh run_tests unit

# Quality checks
./development-workflow/dev-commands.sh quality_checks
\`\`\`
'''

with open('DEVELOPMENT.md', 'w') as f:
    f.write(readme_content)
"
    
    log_success "Documentation generated"
}

# Database operations
query_database() {
    local table_name="$1"
    local limit="${2:-10}"
    
    if [ -z "$table_name" ]; then
        log_error "Usage: query_database <table_name> [limit]"
        return 1
    fi
    
    log_info "Querying table: $table_name (limit: $limit)"
    
    python3 "$SCRIPT_DIR/dev-workflow.py" query-db \
        --table "$table_name" \
        --limit "$limit"
}

# Start development services
start_services() {
    log_info "Starting development services..."
    
    # Start Docker services
    if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        cd "$PROJECT_ROOT"
        docker-compose up -d
        
        # Wait for services to be ready
        log_info "Waiting for services to start..."
        sleep 30
        
        # Health check
        log_info "Performing health checks..."
        
        # Check ML service
        if curl -f http://localhost:8081/health &> /dev/null; then
            log_success "ML Service is healthy"
        else
            log_warning "ML Service health check failed"
        fi
        
        # Check MCP server
        if curl -f http://localhost:8080/health &> /dev/null; then
            log_success "MCP Server is healthy"
        else
            log_warning "MCP Server health check failed"
        fi
        
        # Check n8n
        if curl -f http://localhost:5678/healthz &> /dev/null; then
            log_success "n8n is healthy"
        else
            log_warning "n8n health check failed"
        fi
    else
        log_error "docker-compose.yml not found"
        return 1
    fi
}

# Stop development services
stop_services() {
    log_info "Stopping development services..."
    
    if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        cd "$PROJECT_ROOT"
        docker-compose down
        log_success "Services stopped"
    else
        log_error "docker-compose.yml not found"
        return 1
    fi
}

# Create a pull request
create_pull_request() {
    local title="$1"
    local description="$2"
    
    if [ -z "$title" ]; then
        log_error "Usage: create_pull_request <title> [description]"
        return 1
    fi
    
    log_info "Creating pull request: $title"
    
    # Get current branch
    local current_branch=$(git branch --show-current)
    
    if [ "$current_branch" = "main" ]; then
        log_error "Cannot create PR from main branch"
        return 1
    fi
    
    # Push current branch
    git push origin "$current_branch"
    
    # Create PR using GitHub CLI if available
    if command -v gh &> /dev/null; then
        if [ -n "$description" ]; then
            gh pr create --title "$title" --body "$description"
        else
            gh pr create --title "$title"
        fi
        log_success "Pull request created"
    else
        log_info "GitHub CLI not available. Please create PR manually at:"
        log_info "https://github.com/$(git remote get-url origin | sed 's/.*github.com[:/]\([^/]*\/[^/]*\).*/\1/' | sed 's/\.git$//')/compare/$current_branch"
    fi
}

# Main command dispatcher
main() {
    local command="$1"
    shift
    
    case $command in
        "setup")
            check_dependencies
            setup_dev_env
            ;;
        "update_flag")
            update_feature_flag "$@"
            ;;
        "list_flags")
            list_feature_flags
            ;;
        "create_branch")
            create_feature_branch "$@"
            ;;
        "test")
            run_tests "$@"
            ;;
        "quality_checks")
            run_quality_checks
            ;;
        "docs")
            generate_docs
            ;;
        "query_db")
            query_database "$@"
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "pr")
            create_pull_request "$@"
            ;;
        "help"|"--help"|"-h"|"")
            echo "Development Workflow Commands"
            echo ""
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  setup                           Setup development environment"
            echo "  update_flag <name> <bool> [env] Update feature flag"
            echo "  list_flags                      List all feature flags"
            echo "  create_branch <issue> [desc]    Create feature branch"
            echo "  test [type]                     Run tests (unit|integration|e2e|performance|all)"
            echo "  quality_checks                  Run code quality checks"
            echo "  docs                           Generate documentation"
            echo "  query_db <table> [limit]       Query database table"
            echo "  start                          Start development services"
            echo "  stop                           Stop development services"
            echo "  pr <title> [description]       Create pull request"
            echo "  help                           Show this help message"
            ;;
        *)
            log_error "Unknown command: $command"
            log_info "Use '$0 help' to see available commands"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
