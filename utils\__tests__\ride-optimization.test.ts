/**
 * Test suite for ride optimization utilities
 * Generated automatically by development workflow
 */

import {
  calculateDistance,
  calculateTravelTime,
  calculateCarbonFootprint,
  findOptimalDriver,
  calculateSurgePricing,
  optimizeMultiWaypointRoute,
  calculateETAWithTraffic,
  validateRideRequest,
  formatDuration,
  formatDistance,
  calculateRideCost,
  Location,
  RideRequest,
  Driver
} from '../ride-optimization';

describe('Ride Optimization Utilities', () => {
  // Sample test data
  const mumbaiLocation: Location = { latitude: 19.0760, longitude: 72.8777 };
  const andheriLocation: Location = { latitude: 19.1136, longitude: 72.8697 };
  const bkcLocation: Location = { latitude: 19.0596, longitude: 72.8656 };

  const sampleRideRequest: RideRequest = {
    id: 'ride_001',
    userId: 'user_001',
    pickup: mumbaiLocation,
    destination: andheriLocation,
    requestTime: new Date(),
    passengerCount: 1
  };

  const sampleDrivers: Driver[] = [
    {
      id: 'driver_001',
      name: '<PERSON>',
      location: { latitude: 19.0800, longitude: 72.8800 },
      isAvailable: true,
      rating: 4.5,
      vehicleType: 'scooter'
    },
    {
      id: 'driver_002',
      name: '<PERSON>',
      location: { latitude: 19.1000, longitude: 72.8600 },
      isAvailable: true,
      rating: 4.8,
      vehicleType: 'electric_scooter'
    },
    {
      id: 'driver_003',
      name: 'Bob Wilson',
      location: { latitude: 19.0500, longitude: 72.9000 },
      isAvailable: false,
      rating: 4.2,
      vehicleType: 'motorcycle'
    }
  ];

  describe('calculateDistance', () => {
    it('should calculate distance between two points correctly', () => {
      const distance = calculateDistance(mumbaiLocation, andheriLocation);
      expect(distance).toBeGreaterThan(0);
      expect(distance).toBeLessThan(50); // Should be reasonable for Mumbai distances
    });

    it('should return 0 for same location', () => {
      const distance = calculateDistance(mumbaiLocation, mumbaiLocation);
      expect(distance).toBeCloseTo(0, 2);
    });

    it('should handle extreme coordinates', () => {
      const point1: Location = { latitude: -90, longitude: -180 };
      const point2: Location = { latitude: 90, longitude: 180 };
      const distance = calculateDistance(point1, point2);
      expect(distance).toBeGreaterThan(0);
      expect(distance).toBeLessThan(25000); // Should be less than Earth's circumference
    });
  });

  describe('calculateTravelTime', () => {
    it('should calculate travel time correctly', () => {
      const time = calculateTravelTime(10, 1.0, 'scooter');
      expect(time).toBeGreaterThan(0);
      expect(time).toBe(24); // 10km at 25km/h = 24 minutes
    });

    it('should adjust for traffic factor', () => {
      const normalTime = calculateTravelTime(10, 1.0, 'scooter');
      const heavyTrafficTime = calculateTravelTime(10, 2.0, 'scooter');
      expect(heavyTrafficTime).toBe(normalTime * 2);
    });

    it('should handle different vehicle types', () => {
      const scooterTime = calculateTravelTime(10, 1.0, 'scooter');
      const bikeTime = calculateTravelTime(10, 1.0, 'bike');
      expect(bikeTime).toBeGreaterThan(scooterTime);
    });
  });

  describe('calculateCarbonFootprint', () => {
    it('should calculate carbon footprint for electric vehicles', () => {
      const footprint = calculateCarbonFootprint(100, 'electric_scooter');
      expect(footprint).toBe(2.0); // 100km * 0.02 kg/km
    });

    it('should calculate higher footprint for petrol vehicles', () => {
      const electricFootprint = calculateCarbonFootprint(100, 'electric_scooter');
      const petrolFootprint = calculateCarbonFootprint(100, 'scooter');
      expect(petrolFootprint).toBeGreaterThan(electricFootprint);
    });

    it('should return 0 for bikes', () => {
      const footprint = calculateCarbonFootprint(100, 'bike');
      expect(footprint).toBe(0);
    });
  });

  describe('findOptimalDriver', () => {
    it('should find the best available driver', () => {
      const result = findOptimalDriver(sampleRideRequest, sampleDrivers);
      expect(result).not.toBeNull();
      expect(result!.assignedDriver.isAvailable).toBe(true);
      expect(result!.optimizationScore).toBeGreaterThan(0);
    });

    it('should return null when no drivers available', () => {
      const unavailableDrivers = sampleDrivers.map(d => ({ ...d, isAvailable: false }));
      const result = findOptimalDriver(sampleRideRequest, unavailableDrivers);
      expect(result).toBeNull();
    });

    it('should return null for empty driver list', () => {
      const result = findOptimalDriver(sampleRideRequest, []);
      expect(result).toBeNull();
    });

    it('should prefer closer drivers', () => {
      const closeDriver: Driver = {
        id: 'close_driver',
        name: 'Close Driver',
        location: { latitude: 19.0761, longitude: 72.8778 }, // Very close to pickup
        isAvailable: true,
        rating: 4.0,
        vehicleType: 'scooter'
      };

      const result = findOptimalDriver(sampleRideRequest, [...sampleDrivers, closeDriver]);
      expect(result!.assignedDriver.id).toBe('close_driver');
    });
  });

  describe('calculateSurgePricing', () => {
    it('should return base price when demand equals supply', () => {
      const result = calculateSurgePricing(100, 0.5, 0.5);
      expect(result.surgeMultiplier).toBe(1.0);
      expect(result.finalPrice).toBe(100);
    });

    it('should increase price when demand exceeds supply', () => {
      const result = calculateSurgePricing(100, 0.8, 0.4);
      expect(result.surgeMultiplier).toBeGreaterThan(1.0);
      expect(result.finalPrice).toBeGreaterThan(100);
    });

    it('should cap surge multiplier at 3.0', () => {
      const result = calculateSurgePricing(100, 1.0, 0.1, 5.0);
      expect(result.surgeMultiplier).toBe(3.0);
    });

    it('should not go below 1.0 multiplier', () => {
      const result = calculateSurgePricing(100, 0.1, 1.0);
      expect(result.surgeMultiplier).toBe(1.0);
    });
  });

  describe('optimizeMultiWaypointRoute', () => {
    it('should return same route for 2 or fewer waypoints', () => {
      const waypoints = [mumbaiLocation, andheriLocation];
      const optimized = optimizeMultiWaypointRoute(waypoints);
      expect(optimized).toEqual(waypoints);
    });

    it('should optimize route for multiple waypoints', () => {
      const waypoints = [mumbaiLocation, andheriLocation, bkcLocation];
      const optimized = optimizeMultiWaypointRoute(waypoints);
      expect(optimized).toHaveLength(3);
      expect(optimized[0]).toEqual(mumbaiLocation); // Should start with first waypoint
    });

    it('should handle empty waypoint list', () => {
      const optimized = optimizeMultiWaypointRoute([]);
      expect(optimized).toEqual([]);
    });
  });

  describe('calculateETAWithTraffic', () => {
    it('should calculate ETA with traffic factor', () => {
      const result = calculateETAWithTraffic(mumbaiLocation, andheriLocation, 'scooter', 1.5);
      expect(result.eta).toBeGreaterThan(0);
      expect(result.distance).toBeGreaterThan(0);
      expect(result.route).toHaveLength(2);
    });

    it('should increase ETA with higher traffic', () => {
      const normalResult = calculateETAWithTraffic(mumbaiLocation, andheriLocation, 'scooter', 1.0);
      const trafficResult = calculateETAWithTraffic(mumbaiLocation, andheriLocation, 'scooter', 2.0);
      expect(trafficResult.eta).toBeGreaterThan(normalResult.eta);
    });
  });

  describe('validateRideRequest', () => {
    it('should return no errors for valid request', () => {
      const errors = validateRideRequest(sampleRideRequest);
      expect(errors).toHaveLength(0);
    });

    it('should return error for missing user ID', () => {
      const invalidRequest = { ...sampleRideRequest, userId: '' };
      const errors = validateRideRequest(invalidRequest);
      expect(errors).toContain('User ID is required');
    });

    it('should return error for missing pickup location', () => {
      const invalidRequest = { ...sampleRideRequest, pickup: undefined };
      const errors = validateRideRequest(invalidRequest);
      expect(errors).toContain('Pickup location is required');
    });

    it('should return error for invalid coordinates', () => {
      const invalidRequest = {
        ...sampleRideRequest,
        pickup: { latitude: 'invalid' as any, longitude: 72.8777 }
      };
      const errors = validateRideRequest(invalidRequest);
      expect(errors).toContain('Invalid pickup coordinates');
    });

    it('should return error for invalid passenger count', () => {
      const invalidRequest = { ...sampleRideRequest, passengerCount: 5 };
      const errors = validateRideRequest(invalidRequest);
      expect(errors).toContain('Passenger count must be between 1 and 4');
    });
  });

  describe('formatDuration', () => {
    it('should format minutes correctly', () => {
      expect(formatDuration(30)).toBe('30 min');
      expect(formatDuration(45.7)).toBe('46 min');
    });

    it('should format hours correctly', () => {
      expect(formatDuration(60)).toBe('1 hr');
      expect(formatDuration(90)).toBe('1 hr 30 min');
      expect(formatDuration(120)).toBe('2 hr');
    });

    it('should handle edge cases', () => {
      expect(formatDuration(0)).toBe('0 min');
      expect(formatDuration(1)).toBe('1 min');
    });
  });

  describe('formatDistance', () => {
    it('should format meters for short distances', () => {
      expect(formatDistance(0.5)).toBe('500 m');
      expect(formatDistance(0.123)).toBe('123 m');
    });

    it('should format kilometers for longer distances', () => {
      expect(formatDistance(1.5)).toBe('1.5 km');
      expect(formatDistance(10)).toBe('10.0 km');
    });

    it('should handle edge cases', () => {
      expect(formatDistance(0)).toBe('0 m');
      expect(formatDistance(1)).toBe('1.0 km');
    });
  });

  describe('calculateRideCost', () => {
    it('should calculate basic ride cost', () => {
      const result = calculateRideCost(10, 30, 1.0, 'scooter');
      expect(result.baseCost).toBeGreaterThan(0);
      expect(result.surgeCost).toBe(result.baseCost);
      expect(result.totalCost).toBe(result.surgeCost);
    });

    it('should apply surge pricing correctly', () => {
      const normalResult = calculateRideCost(10, 30, 1.0, 'scooter');
      const surgeResult = calculateRideCost(10, 30, 2.0, 'scooter');
      expect(surgeResult.surgeCost).toBe(normalResult.baseCost * 2);
    });

    it('should handle different vehicle types', () => {
      const scooterCost = calculateRideCost(10, 30, 1.0, 'scooter');
      const bikeCost = calculateRideCost(10, 30, 1.0, 'bike');
      expect(scooterCost.baseCost).toBeGreaterThan(bikeCost.baseCost);
    });

    it('should return rounded values', () => {
      const result = calculateRideCost(10.7, 30.3, 1.5, 'scooter');
      expect(Number.isInteger(result.baseCost)).toBe(true);
      expect(Number.isInteger(result.surgeCost)).toBe(true);
      expect(Number.isInteger(result.totalCost)).toBe(true);
    });
  });

  describe('Performance Tests', () => {
    it('should handle large driver lists efficiently', () => {
      const largeDriverList: Driver[] = Array.from({ length: 1000 }, (_, i) => ({
        id: `driver_${i}`,
        name: `Driver ${i}`,
        location: { latitude: 19 + Math.random(), longitude: 72 + Math.random() },
        isAvailable: Math.random() > 0.3,
        rating: 3 + Math.random() * 2,
        vehicleType: 'scooter'
      }));

      const startTime = performance.now();
      const result = findOptimalDriver(sampleRideRequest, largeDriverList);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
      expect(result).not.toBeNull();
    });

    it('should handle complex route optimization efficiently', () => {
      const manyWaypoints: Location[] = Array.from({ length: 20 }, (_, i) => ({
        latitude: 19 + (i * 0.01),
        longitude: 72 + (i * 0.01)
      }));

      const startTime = performance.now();
      const optimized = optimizeMultiWaypointRoute(manyWaypoints);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50); // Should complete within 50ms
      expect(optimized).toHaveLength(20);
    });
  });
});
