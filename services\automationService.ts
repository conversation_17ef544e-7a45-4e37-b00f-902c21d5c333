import { workflowManager } from '../n8n/workflowManager';
import { WorkflowTrigger, NotificationWorkflowData, RideWorkflowData, PaymentWorkflowData } from '../n8n/config';

/**
 * Automation Service
 * High-level service for triggering automated workflows
 */
export class AutomationService {
  
  /**
   * Automate ride assignment process
   */
  static async automateRideAssignment(rideData: {
    rideId: string;
    riderId: string;
    pickupLocation: any;
    dropoffLocation: any;
    estimatedDistance: number;
    estimatedDuration: number;
    finalAmount: number;
  }) {
    const workflowData: RideWorkflowData = {
      trigger: WorkflowTrigger.RIDE_REQUESTED,
      timestamp: Date.now(),
      data: {},
      ride: {
        id: rideData.rideId,
        riderId: rideData.riderId,
        status: 'pending',
        pickupLocation: rideData.pickupLocation,
        dropoffLocation: rideData.dropoffLocation,
        estimatedDistance: rideData.estimatedDistance,
        estimatedDuration: rideData.estimatedDuration,
        finalAmount: rideData.finalAmount,
      },
      metadata: {
        rideId: rideData.rideId,
        userId: rideData.riderId,
        priority: 'high',
      },
    };

    return await workflowManager.triggerRideBookingFlow(workflowData);
  }

  /**
   * Send automated driver notification
   */
  static async notifyDriver(driverData: {
    driverId: string;
    rideId: string;
    message: {
      title: string;
      body: string;
      type: 'info' | 'success' | 'warning' | 'error';
    };
    channels: ('push' | 'sms' | 'email' | 'websocket')[];
  }) {
    const notificationData: NotificationWorkflowData = {
      trigger: WorkflowTrigger.DRIVER_FOUND,
      timestamp: Date.now(),
      data: {},
      recipient: {
        userId: driverData.driverId,
        role: 'driver',
      },
      message: driverData.message,
      channels: driverData.channels,
      metadata: {
        rideId: driverData.rideId,
        driverId: driverData.driverId,
        priority: 'high',
      },
    };

    return await workflowManager.triggerDriverNotification(notificationData);
  }

  /**
   * Send automated rider notification
   */
  static async notifyRider(riderData: {
    riderId: string;
    rideId: string;
    message: {
      title: string;
      body: string;
      type: 'info' | 'success' | 'warning' | 'error';
    };
    channels: ('push' | 'sms' | 'email' | 'websocket')[];
  }) {
    const notificationData: NotificationWorkflowData = {
      trigger: WorkflowTrigger.RIDE_ACCEPTED,
      timestamp: Date.now(),
      data: {},
      recipient: {
        userId: riderData.riderId,
        role: 'rider',
      },
      message: riderData.message,
      channels: riderData.channels,
      metadata: {
        rideId: riderData.rideId,
        userId: riderData.riderId,
        priority: 'high',
      },
    };

    return await workflowManager.sendNotification(notificationData);
  }

  /**
   * Automate payment processing
   */
  static async processPayment(paymentData: {
    rideId: string;
    riderId: string;
    driverId: string;
    amount: number;
    method: string;
    riderEmail: string;
  }) {
    const workflowData: PaymentWorkflowData = {
      trigger: WorkflowTrigger.PAYMENT_PROCESSED,
      timestamp: Date.now(),
      data: {},
      payment: {
        rideId: paymentData.rideId,
        amount: paymentData.amount,
        currency: 'INR',
        method: paymentData.method,
        status: 'completed',
      },
      rider: {
        id: paymentData.riderId,
        email: paymentData.riderEmail,
      },
      driver: {
        id: paymentData.driverId,
        earnings: paymentData.amount * 0.8, // 80% to driver
      },
      metadata: {
        rideId: paymentData.rideId,
        userId: paymentData.riderId,
        driverId: paymentData.driverId,
        priority: 'high',
      },
    };

    return await workflowManager.triggerPaymentProcessing(workflowData);
  }

  /**
   * Trigger emergency alert workflow
   */
  static async triggerEmergencyAlert(emergencyData: {
    userId: string;
    userRole: 'rider' | 'driver';
    rideId: string;
    location: {
      latitude: number;
      longitude: number;
    };
    message?: string;
  }) {
    const workflowData = {
      trigger: WorkflowTrigger.EMERGENCY_ALERT,
      timestamp: Date.now(),
      data: {
        userId: emergencyData.userId,
        userRole: emergencyData.userRole,
        rideId: emergencyData.rideId,
        location: emergencyData.location,
        message: emergencyData.message || 'Emergency assistance requested',
      },
      metadata: {
        rideId: emergencyData.rideId,
        userId: emergencyData.userId,
        priority: 'critical',
      },
    };

    return await workflowManager.triggerEmergencyAlert(workflowData);
  }

  /**
   * Automate ride status updates
   */
  static async updateRideStatus(statusData: {
    rideId: string;
    riderId: string;
    driverId?: string;
    oldStatus: string;
    newStatus: string;
    location?: any;
  }) {
    const workflowData: RideWorkflowData = {
      trigger: this.getStatusTrigger(statusData.newStatus),
      timestamp: Date.now(),
      data: {
        oldStatus: statusData.oldStatus,
        newStatus: statusData.newStatus,
        location: statusData.location,
      },
      ride: {
        id: statusData.rideId,
        riderId: statusData.riderId,
        driverId: statusData.driverId,
        status: statusData.newStatus,
        pickupLocation: { address: '', coordinates: { latitude: 0, longitude: 0 } },
        dropoffLocation: { address: '', coordinates: { latitude: 0, longitude: 0 } },
        estimatedDistance: 0,
        estimatedDuration: 0,
        finalAmount: 0,
      },
      metadata: {
        rideId: statusData.rideId,
        userId: statusData.riderId,
        driverId: statusData.driverId,
        priority: 'medium',
      },
    };

    return await workflowManager.triggerStatusUpdate(workflowData);
  }

  /**
   * Schedule daily reports
   */
  static async scheduleDailyReports() {
    return await workflowManager.triggerDailyReports();
  }

  /**
   * Monitor driver performance
   */
  static async monitorDriverPerformance(driverId: string, metrics: {
    rating: number;
    totalRides: number;
    cancellationRate: number;
    acceptanceRate: number;
    responseTime: number;
  }) {
    return await workflowManager.monitorDriverPerformance(driverId, metrics);
  }

  /**
   * Automated driver onboarding workflow
   */
  static async triggerDriverOnboarding(driverData: {
    driverId: string;
    email: string;
    phone: string;
    documentsUploaded: boolean;
  }) {
    const workflowData = {
      trigger: 'driver_registered' as WorkflowTrigger,
      timestamp: Date.now(),
      data: {
        driverId: driverData.driverId,
        email: driverData.email,
        phone: driverData.phone,
        documentsUploaded: driverData.documentsUploaded,
        onboardingSteps: [
          'document_verification',
          'background_check',
          'vehicle_inspection',
          'training_completion',
          'account_activation',
        ],
      },
      metadata: {
        userId: driverData.driverId,
        priority: 'medium',
      },
    };

    return await workflowManager.triggerWorkflow('/webhook/driver-onboarding', workflowData);
  }

  /**
   * Automated rider welcome workflow
   */
  static async triggerRiderWelcome(riderData: {
    riderId: string;
    email: string;
    firstName: string;
  }) {
    const workflowData = {
      trigger: 'rider_registered' as WorkflowTrigger,
      timestamp: Date.now(),
      data: {
        riderId: riderData.riderId,
        email: riderData.email,
        firstName: riderData.firstName,
        welcomeSteps: [
          'send_welcome_email',
          'setup_profile_reminder',
          'first_ride_discount',
          'app_tutorial',
        ],
      },
      metadata: {
        userId: riderData.riderId,
        priority: 'low',
      },
    };

    return await workflowManager.triggerWorkflow('/webhook/rider-welcome', workflowData);
  }

  /**
   * Get appropriate workflow trigger based on ride status
   */
  private static getStatusTrigger(status: string): WorkflowTrigger {
    switch (status) {
      case 'accepted':
        return WorkflowTrigger.RIDE_ACCEPTED;
      case 'in_progress':
        return WorkflowTrigger.RIDE_STARTED;
      case 'completed':
        return WorkflowTrigger.RIDE_COMPLETED;
      case 'cancelled':
        return WorkflowTrigger.RIDE_CANCELLED;
      default:
        return WorkflowTrigger.RIDE_REQUESTED;
    }
  }

  /**
   * Health check for automation service
   */
  static async healthCheck() {
    return await workflowManager.healthCheck();
  }
}

export default AutomationService;
