/**
 * GitHub AI Reviewer Integration
 * Automatically reviews pull requests and posts intelligent comments
 */

const { Octokit } = require('@octokit/rest');
const CodeReviewer = require('./code-reviewer');
const QualityMonitor = require('./quality-monitor');

class GitHubReviewer {
  constructor(githubToken, repoOwner, repoName) {
    this.octokit = new Octokit({ auth: githubToken });
    this.repoOwner = repoOwner;
    this.repoName = repoName;
    this.codeReviewer = new CodeReviewer();
    this.qualityMonitor = new QualityMonitor();
    this.reviewedPRs = new Set();
  }

  /**
   * Set up webhook handler for PR events
   */
  async setupWebhookHandler(app) {
    app.post('/webhook/github', async (req, res) => {
      try {
        const event = req.headers['x-github-event'];
        const payload = req.body;

        console.log(`📥 GitHub webhook received: ${event}`);

        switch (event) {
          case 'pull_request':
            await this.handlePullRequestEvent(payload);
            break;
          case 'pull_request_review':
            await this.handleReviewEvent(payload);
            break;
          case 'push':
            await this.handlePushEvent(payload);
            break;
          default:
            console.log(`ℹ️  Unhandled event: ${event}`);
        }

        res.status(200).json({ success: true });
      } catch (error) {
        console.error('❌ Webhook handler error:', error.message);
        res.status(500).json({ error: error.message });
      }
    });
  }

  /**
   * Handle pull request events (opened, synchronize, etc.)
   */
  async handlePullRequestEvent(payload) {
    const { action, pull_request } = payload;
    
    if (!['opened', 'synchronize', 'reopened'].includes(action)) {
      return;
    }

    const prNumber = pull_request.number;
    const prId = `${this.repoOwner}/${this.repoName}#${prNumber}`;

    console.log(`🔍 Processing PR ${prId} (${action})`);

    try {
      // Skip if already reviewed recently
      if (this.reviewedPRs.has(prId) && action === 'synchronize') {
        console.log(`⏭️  Skipping recent review for ${prId}`);
        return;
      }

      // Get PR details and files
      const prData = await this.getPullRequestData(prNumber);
      
      // Perform AI review
      const reviewResults = await this.codeReviewer.reviewPullRequest(prData);
      
      // Check quality gates
      const qualityGates = this.codeReviewer.checkQualityGates(reviewResults);
      
      // Post review comments
      await this.postReviewComments(prNumber, reviewResults);
      
      // Post review summary
      await this.postReviewSummary(prNumber, reviewResults, qualityGates);
      
      // Update PR status checks
      await this.updateStatusChecks(pull_request.head.sha, qualityGates);
      
      // Mark as reviewed
      this.reviewedPRs.add(prId);
      
      console.log(`✅ AI review completed for ${prId}`);

    } catch (error) {
      console.error(`❌ Failed to review ${prId}:`, error.message);
      await this.postErrorComment(prNumber, error);
    }
  }

  /**
   * Get comprehensive PR data for review
   */
  async getPullRequestData(prNumber) {
    try {
      // Get PR details
      const { data: pr } = await this.octokit.pulls.get({
        owner: this.repoOwner,
        repo: this.repoName,
        pull_number: prNumber
      });

      // Get PR files
      const { data: files } = await this.octokit.pulls.listFiles({
        owner: this.repoOwner,
        repo: this.repoName,
        pull_number: prNumber
      });

      // Get diff for each file
      const filesWithContent = await Promise.all(
        files.map(async (file) => {
          let content = '';
          
          if (file.status !== 'removed' && file.filename.match(/\.(js|jsx|ts|tsx|py|java|go|rs)$/)) {
            try {
              const { data: fileData } = await this.octokit.repos.getContent({
                owner: this.repoOwner,
                repo: this.repoName,
                path: file.filename,
                ref: pr.head.sha
              });
              
              content = Buffer.from(fileData.content, 'base64').toString('utf8');
            } catch (error) {
              console.log(`⚠️  Could not fetch content for ${file.filename}`);
            }
          }

          return {
            path: file.filename,
            content,
            changes: {
              added: file.additions,
              removed: file.deletions,
              modified: file.changes
            },
            status: file.status,
            patch: file.patch || ''
          };
        })
      );

      return {
        files: filesWithContent.filter(f => f.content), // Only files we could read
        diff: files.map(f => f.patch).join('\n'),
        metadata: {
          title: pr.title,
          description: pr.body,
          author: pr.user.login,
          branch: pr.head.ref,
          base: pr.base.ref,
          number: prNumber,
          url: pr.html_url
        }
      };

    } catch (error) {
      console.error('❌ Failed to get PR data:', error.message);
      throw error;
    }
  }

  /**
   * Post AI-generated review comments
   */
  async postReviewComments(prNumber, reviewResults) {
    const comments = [];

    for (const fileReview of reviewResults.files) {
      const fileComments = await this.codeReviewer.generateReviewComments(fileReview);
      comments.push(...fileComments);
    }

    // Group comments by severity
    const criticalComments = comments.filter(c => c.severity === 'critical');
    const highComments = comments.filter(c => c.severity === 'high');
    const otherComments = comments.filter(c => !['critical', 'high'].includes(c.severity));

    // Post critical and high severity comments first
    for (const comment of [...criticalComments, ...highComments]) {
      try {
        await this.octokit.pulls.createReviewComment({
          owner: this.repoOwner,
          repo: this.repoName,
          pull_number: prNumber,
          body: comment.body,
          path: comment.path,
          line: comment.line,
          side: 'RIGHT'
        });
      } catch (error) {
        console.error(`❌ Failed to post comment on ${comment.path}:${comment.line}`, error.message);
      }
    }

    // Post other comments (limited to avoid spam)
    const limitedOtherComments = otherComments.slice(0, 5);
    for (const comment of limitedOtherComments) {
      try {
        await this.octokit.pulls.createReviewComment({
          owner: this.repoOwner,
          repo: this.repoName,
          pull_number: prNumber,
          body: comment.body,
          path: comment.path,
          line: comment.line,
          side: 'RIGHT'
        });
      } catch (error) {
        console.error(`❌ Failed to post comment on ${comment.path}:${comment.line}`, error.message);
      }
    }

    console.log(`💬 Posted ${criticalComments.length + highComments.length + limitedOtherComments.length} review comments`);
  }

  /**
   * Post comprehensive review summary
   */
  async postReviewSummary(prNumber, reviewResults, qualityGates) {
    const summary = this.generateReviewSummaryMarkdown(reviewResults, qualityGates);

    try {
      await this.octokit.pulls.createReview({
        owner: this.repoOwner,
        repo: this.repoName,
        pull_number: prNumber,
        body: summary,
        event: qualityGates.passed ? 'APPROVE' : 'REQUEST_CHANGES'
      });

      console.log(`📝 Posted review summary (${qualityGates.passed ? 'APPROVED' : 'CHANGES_REQUESTED'})`);
    } catch (error) {
      console.error('❌ Failed to post review summary:', error.message);
    }
  }

  /**
   * Generate markdown review summary
   */
  generateReviewSummaryMarkdown(reviewResults, qualityGates) {
    const emoji = qualityGates.passed ? '✅' : '⚠️';
    const status = qualityGates.passed ? 'APPROVED' : 'CHANGES REQUESTED';

    let summary = `## ${emoji} AI Code Review - ${status}\n\n`;
    
    // Overall metrics
    summary += `### 📊 Quality Metrics\n`;
    summary += `- **Overall Score**: ${reviewResults.overall_score}/10\n`;
    summary += `- **Files Reviewed**: ${reviewResults.files.length}\n`;
    summary += `- **Issues Found**: ${reviewResults.blocking_issues.length} critical, ${reviewResults.suggestions.length} suggestions\n\n`;

    // Quality gates
    summary += `### 🚪 Quality Gates\n`;
    summary += `- **Overall Quality**: ${qualityGates.overall_quality ? '✅' : '❌'}\n`;
    summary += `- **No Critical Issues**: ${qualityGates.no_critical_issues ? '✅' : '❌'}\n`;
    summary += `- **Security Compliant**: ${qualityGates.security_compliant ? '✅' : '❌'}\n`;
    summary += `- **Performance Acceptable**: ${qualityGates.performance_acceptable ? '✅' : '❌'}\n\n`;

    // File breakdown
    if (reviewResults.files.length > 0) {
      summary += `### 📁 File Analysis\n`;
      for (const fileReview of reviewResults.files) {
        const score = fileReview.review.quality_score;
        const emoji = score >= 8 ? '🟢' : score >= 6 ? '🟡' : '🔴';
        summary += `- ${emoji} \`${fileReview.file}\`: ${score}/10\n`;
      }
      summary += '\n';
    }

    // Recommendations
    if (reviewResults.recommendations.length > 0) {
      summary += `### 💡 Recommendations\n`;
      for (const rec of reviewResults.recommendations) {
        const emoji = rec.priority === 'critical' ? '🚨' : rec.priority === 'high' ? '⚠️' : 'ℹ️';
        summary += `- ${emoji} **${rec.type.toUpperCase()}**: ${rec.description}\n`;
      }
      summary += '\n';
    }

    // AI summary
    summary += `### 🤖 AI Summary\n`;
    summary += `${reviewResults.summary}\n\n`;

    summary += `---\n`;
    summary += `*This review was generated by AI Code Review Assistant. Please verify critical issues manually.*`;

    return summary;
  }

  /**
   * Update GitHub status checks
   */
  async updateStatusChecks(sha, qualityGates) {
    const checks = [
      {
        name: 'AI Code Review / Quality Gates',
        status: qualityGates.passed ? 'success' : 'failure',
        description: qualityGates.passed ? 'All quality gates passed' : 'Quality gates failed'
      },
      {
        name: 'AI Code Review / Overall Quality',
        status: qualityGates.overall_quality ? 'success' : 'failure',
        description: qualityGates.overall_quality ? 'Code quality acceptable' : 'Code quality below threshold'
      },
      {
        name: 'AI Code Review / Security',
        status: qualityGates.security_compliant ? 'success' : 'failure',
        description: qualityGates.security_compliant ? 'No security issues' : 'Security issues found'
      }
    ];

    for (const check of checks) {
      try {
        await this.octokit.repos.createCommitStatus({
          owner: this.repoOwner,
          repo: this.repoName,
          sha: sha,
          state: check.status,
          context: check.name,
          description: check.description
        });
      } catch (error) {
        console.error(`❌ Failed to update status check ${check.name}:`, error.message);
      }
    }
  }

  /**
   * Handle review events (for learning)
   */
  async handleReviewEvent(payload) {
    const { action, review, pull_request } = payload;
    
    if (action === 'submitted' && review.user.type !== 'Bot') {
      // Learn from human reviewer feedback
      console.log(`📚 Learning from human review on PR #${pull_request.number}`);
      // This could be used to improve AI review accuracy
    }
  }

  /**
   * Handle push events (for quality monitoring)
   */
  async handlePushEvent(payload) {
    const { ref, commits } = payload;
    
    if (ref === 'refs/heads/main' || ref === 'refs/heads/master') {
      console.log(`📊 Triggering quality monitoring for main branch push`);
      // Trigger quality monitoring
      this.qualityMonitor.performQualityCheck();
    }
  }

  /**
   * Post error comment when review fails
   */
  async postErrorComment(prNumber, error) {
    const errorComment = `## ❌ AI Review Error

Sorry, the AI code review encountered an error:

\`\`\`
${error.message}
\`\`\`

Please try again or contact the development team if the issue persists.

*This is an automated message from the AI Code Review Assistant.*`;

    try {
      await this.octokit.issues.createComment({
        owner: this.repoOwner,
        repo: this.repoName,
        issue_number: prNumber,
        body: errorComment
      });
    } catch (commentError) {
      console.error('❌ Failed to post error comment:', commentError.message);
    }
  }

  /**
   * Get review statistics
   */
  async getReviewStats(days = 30) {
    const since = new Date();
    since.setDate(since.getDate() - days);

    try {
      const { data: pulls } = await this.octokit.pulls.list({
        owner: this.repoOwner,
        repo: this.repoName,
        state: 'all',
        since: since.toISOString()
      });

      const stats = {
        total_prs: pulls.length,
        reviewed_by_ai: this.reviewedPRs.size,
        average_review_time: 0, // Would calculate from actual data
        quality_improvements: 0, // Would track from metrics
        issues_prevented: 0 // Would track from review data
      };

      return stats;
    } catch (error) {
      console.error('❌ Failed to get review stats:', error.message);
      return null;
    }
  }
}

module.exports = GitHubReviewer;
