import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MapPin, Clock, Info } from "lucide-react"

export default function OfferRidePage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Offer Your Vehicle</h1>

        <div className="bg-muted p-4 rounded-lg mb-8 flex items-start gap-3">
          <Info className="h-5 w-5 text-primary mt-0.5" />
          <div>
            <p className="font-medium">Earn ₹3 per kilometer</p>
            <p className="text-sm text-muted-foreground">
              For a daily 20km ride, you can earn ₹60 per day or ₹1,800 per month!
            </p>
          </div>
        </div>

        <Tabs defaultValue="vehicle-details">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="vehicle-details">Vehicle Details</TabsTrigger>
            <TabsTrigger value="route-details">Route Details</TabsTrigger>
            <TabsTrigger value="availability">Availability</TabsTrigger>
          </TabsList>

          <TabsContent value="vehicle-details">
            <Card>
              <CardHeader>
                <CardTitle>Vehicle Information</CardTitle>
                <CardDescription>Provide details about your two-wheeler</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="vehicle-type">Vehicle Type</Label>
                    <Select>
                      <SelectTrigger id="vehicle-type">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="motorcycle">Motorcycle</SelectItem>
                        <SelectItem value="scooter">Scooter</SelectItem>
                        <SelectItem value="electric">Electric Scooter</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="vehicle-model">Model</Label>
                    <Input id="vehicle-model" placeholder="e.g., Honda Activa, Hero Splendor" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="registration">Registration Number</Label>
                    <Input id="registration" placeholder="e.g., MH01AB1234" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="year">Year of Manufacture</Label>
                    <Input id="year" type="number" placeholder="e.g., 2020" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Vehicle Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your vehicle, its condition, features, etc."
                    rows={4}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Save & Continue</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="route-details">
            <Card>
              <CardHeader>
                <CardTitle>Route Information</CardTitle>
                <CardDescription>Specify your regular travel route</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="start-location">Start Location</Label>
                  <div className="flex">
                    <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <Input id="start-location" placeholder="Enter your starting point" className="rounded-l-none" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end-location">End Location</Label>
                  <div className="flex">
                    <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <Input id="end-location" placeholder="Enter your destination" className="rounded-l-none" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="route-description">Route Description</Label>
                  <Textarea
                    id="route-description"
                    placeholder="Describe your route, including major landmarks or areas you pass through"
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="distance">Approximate Distance (km)</Label>
                  <Input id="distance" type="number" placeholder="e.g., 20" />
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Save & Continue</Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="availability">
            <Card>
              <CardHeader>
                <CardTitle>Availability Schedule</CardTitle>
                <CardDescription>Set your regular availability for rides</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Days Available</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].map((day) => (
                        <div key={day} className="flex items-center space-x-2">
                          <input type="checkbox" id={day.toLowerCase()} className="rounded border-gray-300" />
                          <Label htmlFor={day.toLowerCase()}>{day}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="departure-time">Departure Time</Label>
                      <div className="flex">
                        <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <Input id="departure-time" type="time" className="rounded-l-none" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="return-time">Return Time</Label>
                      <div className="flex">
                        <div className="flex items-center justify-center px-3 border border-r-0 rounded-l-md bg-muted">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <Input id="return-time" type="time" className="rounded-l-none" />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="additional-notes">Additional Notes</Label>
                  <Textarea
                    id="additional-notes"
                    placeholder="Any additional information about your availability"
                    rows={4}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Complete Registration</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

