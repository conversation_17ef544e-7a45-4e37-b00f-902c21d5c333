import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { taskId, driverId } = await request.json()

    if (!taskId || !driverId) {
      return NextResponse.json(
        { success: false, error: 'Missing taskId or driverId' },
        { status: 400 }
      )
    }

    // Update task status in database (mock implementation)
    // In real implementation, update MongoDB document

    // Notify MCP server about task acceptance
    const mcpResponse = await fetch('http://localhost:8080/tools/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        tool: 'notify_driver',
        parameters: {
          driverId,
          message: `Task ${taskId} accepted. Please proceed to pickup location.`,
          taskId,
          priority: 'medium'
        }
      })
    })

    if (!mcpResponse.ok) {
      throw new Error('Failed to notify via MCP server')
    }

    // Also notify the user
    await fetch('http://localhost:8080/notifications/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: 'user123', // Get from task data
        type: 'driver_accepted',
        title: 'Driver Accepted Your Ride!',
        message: `Your driver is on the way to pickup location.`,
        priority: 'high',
        data: {
          taskId,
          driverId,
          status: 'accepted'
        }
      })
    })

    return NextResponse.json({
      success: true,
      message: 'Task accepted successfully',
      taskId,
      status: 'accepted'
    })
  } catch (error) {
    console.error('Task acceptance error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to accept task' },
      { status: 500 }
    )
  }
}
