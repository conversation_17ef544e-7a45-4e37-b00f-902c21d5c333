#!/usr/bin/env node

/**
 * AI-Powered Development Assistant Demo
 * Demonstrates code analysis and smart test generation capabilities
 */

const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const { execSync } = require('child_process');

class AIDevDemo {
  constructor() {
    this.mcpServerUrl = process.env.MCP_SERVER_URL || 'http://localhost:8080';
    this.componentsDir = path.join(__dirname, '..', 'components');
    this.results = [];
  }

  /**
   * Run the complete AI development assistant demonstration
   */
  async runDemo() {
    console.log('🚀 AI-Powered Development Assistant Demo');
    console.log('==========================================\n');

    try {
      // Check if MCP server is running
      await this.checkMCPServer();

      // Analyze existing components
      await this.analyzeComponents();

      // Generate tests for components
      await this.generateTests();

      // Demonstrate refactoring suggestions
      await this.demonstrateRefactoring();

      // Show PR description generation
      await this.demonstratePRGeneration();

      // Display summary
      this.displaySummary();

    } catch (error) {
      console.error('❌ Demo failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Check if MCP server is running and AI services are available
   */
  async checkMCPServer() {
    console.log('🔍 Checking MCP Server and AI services...');
    
    try {
      const response = await axios.get(`${this.mcpServerUrl}/ai-dev-assistant/health`);
      
      if (response.data.success) {
        console.log('✅ MCP Server is running');
        console.log('✅ AI services are healthy');
        console.log(`📊 Ollama status: ${response.data.health.status}\n`);
      } else {
        throw new Error('AI services not healthy');
      }
    } catch (error) {
      console.log('⚠️  MCP Server not running. Starting demo in offline mode...\n');
      this.offlineMode = true;
    }
  }

  /**
   * Analyze existing React components
   */
  async analyzeComponents() {
    console.log('🔍 Analyzing React Components');
    console.log('==============================');

    const componentFiles = [
      'use-mobile.tsx',
      'use-toast.ts'
    ];

    for (const file of componentFiles) {
      try {
        const filePath = path.join(this.componentsDir, file);
        const codeContent = await fs.readFile(filePath, 'utf8');

        console.log(`\n📁 Analyzing: ${file}`);
        
        if (this.offlineMode) {
          // Offline analysis
          const analysis = this.performOfflineAnalysis(file, codeContent);
          console.log(`   Quality Score: ${analysis.quality_score}/10`);
          console.log(`   Issues Found: ${analysis.issues.length}`);
          console.log(`   Suggestions: ${analysis.optimizations.length}`);
        } else {
          // Online AI analysis
          const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/analyze`, {
            filePath: file,
            codeContent
          });

          if (response.data.success) {
            const analysis = response.data.analysis.analysis;
            console.log(`   Quality Score: ${analysis.quality_score}/10`);
            console.log(`   Issues Found: ${analysis.issues.length}`);
            console.log(`   Optimizations: ${analysis.optimizations.length}`);
            console.log(`   Security Concerns: ${analysis.security.length}`);
          }
        }

        this.results.push({
          file,
          type: 'analysis',
          success: true
        });

      } catch (error) {
        console.log(`   ❌ Failed to analyze ${file}: ${error.message}`);
        this.results.push({
          file,
          type: 'analysis',
          success: false,
          error: error.message
        });
      }
    }
  }

  /**
   * Generate comprehensive tests for components
   */
  async generateTests() {
    console.log('\n\n🧪 Generating Smart Tests');
    console.log('==========================');

    const componentFiles = [
      'use-mobile.tsx',
      'use-toast.ts'
    ];

    for (const file of componentFiles) {
      try {
        const filePath = path.join(this.componentsDir, file);
        const codeContent = await fs.readFile(filePath, 'utf8');

        console.log(`\n📝 Generating tests for: ${file}`);

        if (this.offlineMode) {
          // Offline test generation
          const testCode = this.generateOfflineTests(file, codeContent);
          const testFilePath = path.join(this.componentsDir, file.replace(/\.(tsx?|jsx?)$/, '.test.$1'));
          
          await fs.writeFile(testFilePath, testCode, 'utf8');
          console.log(`   ✅ Test file created: ${path.basename(testFilePath)}`);
          console.log(`   📊 Estimated coverage: 85%`);
        } else {
          // Online AI test generation
          const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-tests`, {
            filePath: file,
            codeContent
          });

          if (response.data.success) {
            const testResult = response.data.testResult;
            console.log(`   ✅ Test file generated: ${path.basename(testResult.testFilePath)}`);
            console.log(`   📊 Estimated coverage: ${testResult.coverage.estimated_coverage}%`);
            console.log(`   📏 Test lines: ${testResult.coverage.test_lines}`);
          }
        }

        this.results.push({
          file,
          type: 'test-generation',
          success: true
        });

      } catch (error) {
        console.log(`   ❌ Failed to generate tests for ${file}: ${error.message}`);
        this.results.push({
          file,
          type: 'test-generation',
          success: false,
          error: error.message
        });
      }
    }
  }

  /**
   * Demonstrate refactoring suggestions
   */
  async demonstrateRefactoring() {
    console.log('\n\n🔧 Refactoring Suggestions');
    console.log('===========================');

    const file = 'use-toast.ts';
    try {
      const filePath = path.join(this.componentsDir, file);
      const codeContent = await fs.readFile(filePath, 'utf8');

      console.log(`\n🔍 Analyzing ${file} for refactoring opportunities...`);

      if (this.offlineMode) {
        const suggestions = this.generateOfflineRefactoring(file, codeContent);
        console.log(`   💡 Found ${suggestions.length} refactoring opportunities:`);
        suggestions.forEach((suggestion, index) => {
          console.log(`   ${index + 1}. ${suggestion.description}`);
        });
      } else {
        const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/suggest-refactoring`, {
          filePath: file,
          codeContent
        });

        if (response.data.success) {
          const suggestions = response.data.suggestions.suggestions;
          console.log(`   💡 Found ${suggestions.length} refactoring opportunities`);
          suggestions.forEach((suggestion, index) => {
            console.log(`   ${index + 1}. ${suggestion.description}`);
          });
        }
      }

    } catch (error) {
      console.log(`   ❌ Failed to generate refactoring suggestions: ${error.message}`);
    }
  }

  /**
   * Demonstrate PR description generation
   */
  async demonstratePRGeneration() {
    console.log('\n\n📝 PR Description Generation');
    console.log('=============================');

    try {
      // Simulate git diff
      const gitDiff = `
diff --git a/components/use-mobile.tsx b/components/use-mobile.tsx
index 1234567..abcdefg 100644
--- a/components/use-mobile.tsx
+++ b/components/use-mobile.tsx
@@ -1,5 +1,6 @@
 import * as React from "react"
 
+// Mobile breakpoint constant
 const MOBILE_BREAKPOINT = 768
 
 export function useIsMobile() {`;

      const branchName = 'feature/ai-enhanced-mobile-detection';

      console.log(`\n🔍 Generating PR description for branch: ${branchName}`);

      if (this.offlineMode) {
        const prDescription = this.generateOfflinePRDescription(gitDiff, branchName);
        console.log('   ✅ PR description generated:');
        console.log('   📋 Title: Add comment to mobile breakpoint constant');
        console.log('   📝 Description: Enhanced code documentation for better maintainability');
      } else {
        const response = await axios.post(`${this.mcpServerUrl}/ai-dev-assistant/generate-pr`, {
          gitDiff,
          branchName
        });

        if (response.data.success) {
          const prDescription = response.data.prDescription;
          console.log('   ✅ PR description generated:');
          console.log(`   📋 Title: ${prDescription.title}`);
          console.log('   📝 Description: AI-generated comprehensive PR description');
        }
      }

    } catch (error) {
      console.log(`   ❌ Failed to generate PR description: ${error.message}`);
    }
  }

  /**
   * Display demo summary
   */
  displaySummary() {
    console.log('\n\n📊 Demo Summary');
    console.log('================');

    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;

    console.log(`✅ Successful operations: ${successful}`);
    console.log(`❌ Failed operations: ${failed}`);
    console.log(`📈 Success rate: ${Math.round((successful / this.results.length) * 100)}%`);

    console.log('\n🎯 Key Features Demonstrated:');
    console.log('   • AI-powered code analysis');
    console.log('   • Smart test generation');
    console.log('   • Refactoring suggestions');
    console.log('   • PR description generation');
    console.log('   • Batch processing capabilities');

    console.log('\n🚀 Next Steps:');
    console.log('   • Integrate with your IDE');
    console.log('   • Set up automated workflows');
    console.log('   • Configure CI/CD integration');
    console.log('   • Customize AI prompts for your team');

    console.log('\n✨ AI-Powered Development Assistant Demo Complete!');
  }

  // Offline mode methods for demonstration when AI services are not available
  performOfflineAnalysis(file, code) {
    return {
      quality_score: 8,
      issues: [
        { type: 'style', description: 'Consider adding JSDoc comments', line: 5, severity: 'low' }
      ],
      optimizations: [
        { description: 'Add memoization for performance', impact: 'medium' }
      ],
      security: [],
      refactoring: [
        { suggestion: 'Extract constants to separate file', benefit: 'Better maintainability' }
      ],
      best_practices: []
    };
  }

  generateOfflineTests(file, code) {
    return `// AI-Generated tests for ${file}
import { renderHook } from '@testing-library/react';
import { useIsMobile } from './${file.replace(/\.(tsx?|jsx?)$/, '')}';

describe('${file}', () => {
  it('should detect mobile correctly', () => {
    const { result } = renderHook(() => useIsMobile());
    expect(typeof result.current).toBe('boolean');
  });
});`;
  }

  generateOfflineRefactoring(file, code) {
    return [
      { description: 'Extract MOBILE_BREAKPOINT to a constants file' },
      { description: 'Add TypeScript strict mode compliance' },
      { description: 'Consider using a custom hook for window resize events' }
    ];
  }

  generateOfflinePRDescription(gitDiff, branchName) {
    return {
      title: 'Add documentation comment to mobile breakpoint',
      description: 'Enhanced code readability by adding explanatory comment'
    };
  }
}

// Run demo if called directly
if (require.main === module) {
  const demo = new AIDevDemo();
  demo.runDemo().catch(console.error);
}

module.exports = AIDevDemo;
