version: '3.8'

services:
  # Next.js Application
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/two-wheeler-sharing
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook
      - MCP_SERVER_URL=http://mcp-server:8080
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongodb
      - n8n
      - mcp-server

  # n8n Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    ports:
      - "5678:5678"
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8n123
      - N8N_WEBHOOK_URL=http://localhost:5678/
      - WEBHOOK_URL=http://localhost:5678/
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./n8n/workflows:/home/<USER>/.n8n/workflows
    depends_on:
      - postgres

  # PostgreSQL for n8n
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8n123
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # MongoDB for application data
  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
      - MONGO_INITDB_DATABASE=two-wheeler-sharing
    volumes:
      - mongodb_data:/data/db

  # MCP Server for AI tool calling
  mcp-server:
    build: ./mcp-server
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/two-wheeler-sharing
      - OLLAMA_URL=http://ollama:11434
    depends_on:
      - mongodb
      - ollama

  # Ollama for AI/LLM capabilities
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0

  # NGINX Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
      - n8n
      - mcp-server

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # ML Prediction Service
  ml-service:
    build: ./ml-service
    ports:
      - "8081:8081"
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/two-wheeler-sharing
      - REDIS_URL=redis://redis:6379
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY}
      - MCP_SERVER_URL=http://mcp-server:8080
    volumes:
      - ./ml-service/models:/app/models
      - ./ml-service/data:/app/data
    depends_on:
      - mongodb
      - redis

volumes:
  n8n_data:
  postgres_data:
  mongodb_data:
  ollama_data:
  redis_data:
