"use client"

import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { 
  Activity, 
  Users, 
  Car, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  MapPin,
  DollarSign,
  BarChart3
} from "lucide-react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts'

interface SystemMetrics {
  activeUsers: number
  activeDrivers: number
  ongoingRides: number
  completedRidesToday: number
  totalRevenue: number
  averageRating: number
  systemHealth: 'healthy' | 'warning' | 'critical'
}

interface WorkflowStatus {
  id: string
  name: string
  status: 'active' | 'paused' | 'error'
  lastRun: string
  successRate: number
  totalExecutions: number
}

export function AdminMonitoring() {
  const [metrics, setMetrics] = useState<SystemMetrics>({
    activeUsers: 1247,
    activeDrivers: 89,
    ongoingRides: 23,
    completedRidesToday: 156,
    totalRevenue: 12450,
    averageRating: 4.7,
    systemHealth: 'healthy'
  })

  const [workflows, setWorkflows] = useState<WorkflowStatus[]>([
    {
      id: 'ride-matching',
      name: 'Ride Matching Automation',
      status: 'active',
      lastRun: '2 minutes ago',
      successRate: 98.5,
      totalExecutions: 1247
    },
    {
      id: 'ride-completion',
      name: 'Ride Completion & Rewards',
      status: 'active',
      lastRun: '5 minutes ago',
      successRate: 99.2,
      totalExecutions: 892
    },
    {
      id: 'demand-prediction',
      name: 'AI Demand Prediction',
      status: 'active',
      lastRun: '1 minute ago',
      successRate: 95.8,
      totalExecutions: 2341
    },
    {
      id: 'driver-notifications',
      name: 'Driver Notification System',
      status: 'warning',
      lastRun: '15 minutes ago',
      successRate: 87.3,
      totalExecutions: 3456
    }
  ])

  const [realtimeData, setRealtimeData] = useState([
    { time: '00:00', rides: 12, revenue: 450 },
    { time: '04:00', rides: 8, revenue: 320 },
    { time: '08:00', rides: 45, revenue: 1800 },
    { time: '12:00', rides: 38, revenue: 1520 },
    { time: '16:00', rides: 42, revenue: 1680 },
    { time: '20:00', rides: 35, revenue: 1400 },
  ])

  const [aiInsights, setAiInsights] = useState({
    demandPrediction: 'High demand expected in Andheri East area in next 2 hours',
    pricingRecommendation: 'Consider 15% surge pricing for peak evening hours',
    driverAllocation: '12 more drivers needed in Bandra-Kurla Complex',
    systemOptimization: 'Ride matching efficiency improved by 8% this week'
  })

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setMetrics(prev => ({
        ...prev,
        ongoingRides: prev.ongoingRides + Math.floor(Math.random() * 3) - 1,
        completedRidesToday: prev.completedRidesToday + Math.floor(Math.random() * 2)
      }))
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  const triggerWorkflow = async (workflowId: string) => {
    try {
      const response = await fetch(`/api/admin/workflows/${workflowId}/trigger`, {
        method: 'POST'
      })
      if (response.ok) {
        console.log(`Workflow ${workflowId} triggered successfully`)
      }
    } catch (error) {
      console.error('Error triggering workflow:', error)
    }
  }

  const pauseWorkflow = async (workflowId: string) => {
    try {
      const response = await fetch(`/api/admin/workflows/${workflowId}/pause`, {
        method: 'POST'
      })
      if (response.ok) {
        setWorkflows(prev => prev.map(w => 
          w.id === workflowId ? { ...w, status: 'paused' } : w
        ))
      }
    } catch (error) {
      console.error('Error pausing workflow:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500'
      case 'warning': return 'bg-yellow-500'
      case 'error': return 'bg-red-500'
      case 'paused': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Admin Monitoring Dashboard</h1>
        <div className="flex items-center gap-2">
          <div className={`flex items-center gap-2 ${getHealthColor(metrics.systemHealth)}`}>
            <Activity className="h-5 w-5" />
            <span className="font-medium capitalize">{metrics.systemHealth}</span>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeUsers}</div>
            <p className="text-xs text-muted-foreground">
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Drivers</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeDrivers}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.ongoingRides} currently on rides
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{metrics.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.completedRidesToday} rides completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averageRating}</div>
            <p className="text-xs text-muted-foreground">
              Excellent service quality
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="workflows" className="space-y-4">
        <TabsList>
          <TabsTrigger value="workflows">Workflow Status</TabsTrigger>
          <TabsTrigger value="analytics">Real-time Analytics</TabsTrigger>
          <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
          <TabsTrigger value="system-health">System Health</TabsTrigger>
        </TabsList>

        <TabsContent value="workflows" className="space-y-4">
          <div className="grid gap-4">
            {workflows.map((workflow) => (
              <Card key={workflow.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{workflow.name}</CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge className={getStatusColor(workflow.status)}>
                          {workflow.status}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          Last run: {workflow.lastRun}
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => triggerWorkflow(workflow.id)}
                      >
                        Trigger
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => pauseWorkflow(workflow.id)}
                      >
                        {workflow.status === 'paused' ? 'Resume' : 'Pause'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Success Rate</span>
                      <span className="text-sm font-medium">{workflow.successRate}%</span>
                    </div>
                    <Progress value={workflow.successRate} />
                    <div className="flex justify-between items-center text-sm text-muted-foreground">
                      <span>Total Executions: {workflow.totalExecutions}</span>
                      <span>Errors: {Math.round((100 - workflow.successRate) * workflow.totalExecutions / 100)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Rides Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={realtimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="rides" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={realtimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="revenue" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Live Activity Feed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <p className="font-medium">Ride completed successfully</p>
                    <p className="text-sm text-muted-foreground">Driver: Rahul S. • User: Priya M. • ₹65</p>
                  </div>
                  <span className="text-xs text-muted-foreground ml-auto">2 min ago</span>
                </div>
                
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">New ride request assigned</p>
                    <p className="text-sm text-muted-foreground">Andheri East → BKC • Driver: Amit K.</p>
                  </div>
                  <span className="text-xs text-muted-foreground ml-auto">3 min ago</span>
                </div>

                <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  <div>
                    <p className="font-medium">High demand detected</p>
                    <p className="text-sm text-muted-foreground">Bandra area • Surge pricing activated</p>
                  </div>
                  <span className="text-xs text-muted-foreground ml-auto">5 min ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-insights" className="space-y-4">
          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Real-Time AI Insights
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-semibold text-blue-900">Demand Prediction</h4>
                    <p className="text-blue-800 text-sm">{aiInsights.demandPrediction}</p>
                    <div className="mt-2 flex items-center gap-2">
                      <div className="text-2xl font-bold text-blue-900">85%</div>
                      <div className="text-xs text-blue-700">Confidence</div>
                    </div>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-semibold text-green-900">Revenue Optimization</h4>
                    <p className="text-green-800 text-sm">{aiInsights.pricingRecommendation}</p>
                    <div className="mt-2 flex items-center gap-2">
                      <div className="text-2xl font-bold text-green-900">+12%</div>
                      <div className="text-xs text-green-700">Revenue Increase</div>
                    </div>
                  </div>

                  <div className="p-4 bg-orange-50 rounded-lg">
                    <h4 className="font-semibold text-orange-900">Fleet Positioning</h4>
                    <p className="text-orange-800 text-sm">{aiInsights.driverAllocation}</p>
                    <div className="mt-2 flex items-center gap-2">
                      <div className="text-2xl font-bold text-orange-900">78%</div>
                      <div className="text-xs text-orange-700">Efficiency Score</div>
                    </div>
                  </div>

                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-semibold text-purple-900">Predictive Maintenance</h4>
                    <p className="text-purple-800 text-sm">3 vehicles need attention in next 7 days</p>
                    <div className="mt-2 flex items-center gap-2">
                      <div className="text-2xl font-bold text-purple-900">₹15K</div>
                      <div className="text-xs text-purple-700">Potential Savings</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>ML Model Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Demand Forecasting</span>
                    <div className="flex items-center gap-2">
                      <Progress value={92} className="w-20" />
                      <span className="text-sm font-medium">92%</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span>Driver Assignment</span>
                    <div className="flex items-center gap-2">
                      <Progress value={88} className="w-20" />
                      <span className="text-sm font-medium">88%</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span>Pricing Optimization</span>
                    <div className="flex items-center gap-2">
                      <Progress value={85} className="w-20" />
                      <span className="text-sm font-medium">85%</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span>Maintenance Prediction</span>
                    <div className="flex items-center gap-2">
                      <Progress value={90} className="w-20" />
                      <span className="text-sm font-medium">90%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Automated AI Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Dynamic surge pricing activated</p>
                      <p className="text-sm text-muted-foreground">BKC area • 1.8x multiplier • High demand detected</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">AI Auto</Badge>
                      <span className="text-xs text-muted-foreground">2 min ago</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Fleet repositioning recommendations sent</p>
                      <p className="text-sm text-muted-foreground">8 drivers notified to move to high-demand areas</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">AI Auto</Badge>
                      <span className="text-xs text-muted-foreground">5 min ago</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Maintenance alert triggered</p>
                      <p className="text-sm text-muted-foreground">Vehicle VH-001 scheduled for service</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">AI Auto</Badge>
                      <span className="text-xs text-muted-foreground">15 min ago</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Traffic-aware routing updated</p>
                      <p className="text-sm text-muted-foreground">Alternative routes suggested due to congestion</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">AI Auto</Badge>
                      <span className="text-xs text-muted-foreground">20 min ago</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>A/B Testing Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">Pricing Strategy Test</h4>
                      <Badge variant="outline">Active</Badge>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">Conservative</div>
                        <div className="font-medium">72% conversion</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Standard</div>
                        <div className="font-medium">68% conversion</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Aggressive</div>
                        <div className="font-medium">65% conversion</div>
                      </div>
                    </div>
                  </div>

                  <div className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">Driver Assignment Algorithm</h4>
                      <Badge variant="outline">Completed</Badge>
                    </div>
                    <div className="text-sm">
                      <div className="text-muted-foreground">Winner: ML-Enhanced Assignment</div>
                      <div className="font-medium">15% faster pickup times, 92% driver satisfaction</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="system-health" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Service Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span>Main Application</span>
                  <Badge className="bg-green-500">Healthy</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>n8n Workflows</span>
                  <Badge className="bg-green-500">Healthy</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>MCP Server</span>
                  <Badge className="bg-green-500">Healthy</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Ollama AI</span>
                  <Badge className="bg-yellow-500">Warning</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>MongoDB</span>
                  <Badge className="bg-green-500">Healthy</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Redis Cache</span>
                  <Badge className="bg-green-500">Healthy</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>API Response Time</span>
                    <span>245ms</span>
                  </div>
                  <Progress value={75} />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>Database Performance</span>
                    <span>Good</span>
                  </div>
                  <Progress value={85} />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>Memory Usage</span>
                    <span>68%</span>
                  </div>
                  <Progress value={68} />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span>CPU Usage</span>
                    <span>42%</span>
                  </div>
                  <Progress value={42} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
