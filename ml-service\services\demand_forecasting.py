import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import requests
import holidays
import pytz
from geopy.distance import geodesic

logger = logging.getLogger(__name__)

class DemandForecastingService:
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = []
        self.model_path = "models/demand_forecasting_model.joblib"
        self.scaler_path = "models/demand_scaler.joblib"
        self.encoders_path = "models/demand_encoders.joblib"
        
        # Mumbai timezone
        self.timezone = pytz.timezone('Asia/Kolkata')
        
        # Load or train model
        self._load_or_train_model()
    
    def _load_or_train_model(self):
        """Load existing model or train new one"""
        try:
            self.model = joblib.load(self.model_path)
            self.scaler = joblib.load(self.scaler_path)
            self.label_encoders = joblib.load(self.encoders_path)
            logger.info("Loaded existing demand forecasting model")
        except FileNotFoundError:
            logger.info("No existing model found, training new model...")
            self._train_initial_model()
    
    def _train_initial_model(self):
        """Train initial model with synthetic data"""
        # Generate synthetic training data
        synthetic_data = self._generate_synthetic_data()
        
        # Prepare features
        X, y = self._prepare_features(synthetic_data)
        
        # Train model
        self._train_model(X, y)
        
        logger.info("Initial demand forecasting model trained successfully")
    
    def _generate_synthetic_data(self, days=90):
        """Generate synthetic ride data for training"""
        data = []
        start_date = datetime.now() - timedelta(days=days)
        
        # Mumbai locations with coordinates
        locations = [
            {"name": "Andheri East", "lat": 19.1136, "lon": 72.8697},
            {"name": "Bandra Kurla Complex", "lat": 19.0596, "lon": 72.8656},
            {"name": "Powai", "lat": 19.1197, "lon": 72.9056},
            {"name": "Malad West", "lat": 19.1875, "lon": 72.8304},
            {"name": "Goregaon East", "lat": 19.1653, "lon": 72.8526},
            {"name": "Hiranandani Gardens", "lat": 19.1197, "lon": 72.9056},
            {"name": "Lower Parel", "lat": 19.0176, "lon": 72.8286},
            {"name": "Worli", "lat": 19.0176, "lon": 72.8156}
        ]
        
        for day in range(days):
            current_date = start_date + timedelta(days=day)
            
            for location in locations:
                for hour in range(24):
                    timestamp = current_date.replace(hour=hour, minute=0, second=0)
                    
                    # Base demand patterns
                    base_demand = self._calculate_base_demand(hour, timestamp.weekday(), location["name"])
                    
                    # Weather impact (synthetic)
                    weather_impact = np.random.normal(1.0, 0.2)
                    
                    # Event impact (random events)
                    event_impact = 1.0
                    if np.random.random() < 0.05:  # 5% chance of event
                        event_impact = np.random.uniform(1.5, 3.0)
                    
                    # Final demand
                    demand = max(0, base_demand * weather_impact * event_impact + np.random.normal(0, 2))
                    
                    data.append({
                        'timestamp': timestamp,
                        'location': location["name"],
                        'latitude': location["lat"],
                        'longitude': location["lon"],
                        'demand': demand,
                        'hour': hour,
                        'day_of_week': timestamp.weekday(),
                        'day_of_month': timestamp.day,
                        'month': timestamp.month,
                        'is_weekend': timestamp.weekday() >= 5,
                        'is_holiday': timestamp.date() in holidays.India(years=timestamp.year),
                        'weather_temp': np.random.normal(28, 5),  # Mumbai average temp
                        'weather_humidity': np.random.normal(70, 15),
                        'weather_condition': np.random.choice(['clear', 'cloudy', 'rain', 'drizzle'], p=[0.4, 0.3, 0.2, 0.1])
                    })
        
        return pd.DataFrame(data)
    
    def _calculate_base_demand(self, hour, day_of_week, location):
        """Calculate base demand based on time and location patterns"""
        # Peak hours: 8-10 AM, 6-8 PM
        if hour in [8, 9, 18, 19]:
            base = 15
        elif hour in [7, 10, 17, 20]:
            base = 10
        elif hour in [11, 12, 13, 14, 15, 16]:
            base = 8
        elif hour in [21, 22]:
            base = 6
        else:
            base = 3
        
        # Weekend adjustment
        if day_of_week >= 5:
            if hour in [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]:
                base *= 1.2
            else:
                base *= 0.7
        
        # Location-specific multipliers
        location_multipliers = {
            "Bandra Kurla Complex": 1.5,  # Business district
            "Andheri East": 1.3,
            "Lower Parel": 1.4,
            "Powai": 1.1,
            "Worli": 1.2,
            "Malad West": 0.9,
            "Goregaon East": 0.8,
            "Hiranandani Gardens": 1.0
        }
        
        return base * location_multipliers.get(location, 1.0)
    
    def _prepare_features(self, data):
        """Prepare features for ML model"""
        # Create feature columns
        feature_data = data.copy()
        
        # Encode categorical variables
        categorical_columns = ['location', 'weather_condition']
        for col in categorical_columns:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                feature_data[col + '_encoded'] = self.label_encoders[col].fit_transform(feature_data[col])
            else:
                feature_data[col + '_encoded'] = self.label_encoders[col].transform(feature_data[col])
        
        # Create time-based features
        feature_data['hour_sin'] = np.sin(2 * np.pi * feature_data['hour'] / 24)
        feature_data['hour_cos'] = np.cos(2 * np.pi * feature_data['hour'] / 24)
        feature_data['day_sin'] = np.sin(2 * np.pi * feature_data['day_of_week'] / 7)
        feature_data['day_cos'] = np.cos(2 * np.pi * feature_data['day_of_week'] / 7)
        feature_data['month_sin'] = np.sin(2 * np.pi * feature_data['month'] / 12)
        feature_data['month_cos'] = np.cos(2 * np.pi * feature_data['month'] / 12)
        
        # Select feature columns
        self.feature_columns = [
            'latitude', 'longitude', 'hour', 'day_of_week', 'day_of_month', 'month',
            'is_weekend', 'is_holiday', 'weather_temp', 'weather_humidity',
            'location_encoded', 'weather_condition_encoded',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos'
        ]
        
        X = feature_data[self.feature_columns]
        y = feature_data['demand']
        
        return X, y
    
    def _train_model(self, X, y):
        """Train the demand forecasting model"""
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train ensemble model
        rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        
        rf_model.fit(X_train_scaled, y_train)
        gb_model.fit(X_train_scaled, y_train)
        
        # Create ensemble
        self.model = {
            'rf': rf_model,
            'gb': gb_model,
            'weights': [0.6, 0.4]  # RF gets more weight
        }
        
        # Evaluate model
        rf_pred = rf_model.predict(X_test_scaled)
        gb_pred = gb_model.predict(X_test_scaled)
        ensemble_pred = 0.6 * rf_pred + 0.4 * gb_pred
        
        mae = mean_absolute_error(y_test, ensemble_pred)
        rmse = np.sqrt(mean_squared_error(y_test, ensemble_pred))
        r2 = r2_score(y_test, ensemble_pred)
        
        logger.info(f"Model performance - MAE: {mae:.2f}, RMSE: {rmse:.2f}, R2: {r2:.3f}")
        
        # Save model
        self._save_model()
    
    def _save_model(self):
        """Save trained model and preprocessors"""
        joblib.dump(self.model, self.model_path)
        joblib.dump(self.scaler, self.scaler_path)
        joblib.dump(self.label_encoders, self.encoders_path)
        logger.info("Model saved successfully")
    
    async def predict_demand(self, location: str, latitude: float, longitude: float, 
                           hours_ahead: int = 2, include_weather: bool = True):
        """Predict demand for specific location and time"""
        try:
            # Get current time in Mumbai timezone
            current_time = datetime.now(self.timezone)
            prediction_time = current_time + timedelta(hours=hours_ahead)
            
            # Prepare features
            features = await self._prepare_prediction_features(
                location, latitude, longitude, prediction_time, include_weather
            )
            
            # Make prediction
            prediction = self._make_prediction(features)
            
            # Calculate confidence based on historical variance
            confidence = self._calculate_confidence(location, prediction_time)
            
            # Identify contributing factors
            factors = self._analyze_factors(features, prediction)
            
            result = {
                "location": location,
                "predicted_demand": float(prediction),
                "confidence": float(confidence),
                "factors": factors,
                "timestamp": current_time,
                "valid_until": prediction_time + timedelta(minutes=30)
            }
            
            # Cache result
            cache_key = f"demand_prediction:{location}:{prediction_time.hour}"
            await self.cache_manager.set(cache_key, result, expire=1800)  # 30 minutes
            
            return result
            
        except Exception as e:
            logger.error(f"Demand prediction error: {e}")
            raise
    
    async def _prepare_prediction_features(self, location, latitude, longitude, 
                                         prediction_time, include_weather):
        """Prepare features for prediction"""
        features = {
            'latitude': latitude,
            'longitude': longitude,
            'hour': prediction_time.hour,
            'day_of_week': prediction_time.weekday(),
            'day_of_month': prediction_time.day,
            'month': prediction_time.month,
            'is_weekend': prediction_time.weekday() >= 5,
            'is_holiday': prediction_time.date() in holidays.India(years=prediction_time.year)
        }
        
        # Add weather data
        if include_weather:
            weather_data = await self._get_weather_data(latitude, longitude)
            features.update(weather_data)
        else:
            # Use default weather values
            features.update({
                'weather_temp': 28.0,
                'weather_humidity': 70.0,
                'weather_condition': 'clear'
            })
        
        # Encode categorical variables
        if 'location' in self.label_encoders:
            try:
                features['location_encoded'] = self.label_encoders['location'].transform([location])[0]
            except ValueError:
                # Unknown location, use most common encoding
                features['location_encoded'] = 0
        else:
            features['location_encoded'] = 0
            
        if 'weather_condition' in self.label_encoders:
            try:
                features['weather_condition_encoded'] = self.label_encoders['weather_condition'].transform([features['weather_condition']])[0]
            except ValueError:
                features['weather_condition_encoded'] = 0
        else:
            features['weather_condition_encoded'] = 0
        
        # Add time-based features
        features['hour_sin'] = np.sin(2 * np.pi * features['hour'] / 24)
        features['hour_cos'] = np.cos(2 * np.pi * features['hour'] / 24)
        features['day_sin'] = np.sin(2 * np.pi * features['day_of_week'] / 7)
        features['day_cos'] = np.cos(2 * np.pi * features['day_of_week'] / 7)
        features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
        features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
        
        return features
    
    async def _get_weather_data(self, latitude, longitude):
        """Get weather data from OpenWeatherMap API"""
        try:
            api_key = os.getenv('OPENWEATHERMAP_API_KEY', 'sample_key_12345')
            if api_key == 'sample_key_12345':
                # Return mock weather data
                return {
                    'weather_temp': 28.0,
                    'weather_humidity': 70.0,
                    'weather_condition': 'clear'
                }
            
            url = f"http://api.openweathermap.org/data/2.5/weather"
            params = {
                'lat': latitude,
                'lon': longitude,
                'appid': api_key,
                'units': 'metric'
            }
            
            response = requests.get(url, params=params, timeout=5)
            if response.status_code == 200:
                data = response.json()
                return {
                    'weather_temp': data['main']['temp'],
                    'weather_humidity': data['main']['humidity'],
                    'weather_condition': data['weather'][0]['main'].lower()
                }
            else:
                logger.warning(f"Weather API error: {response.status_code}")
                return {
                    'weather_temp': 28.0,
                    'weather_humidity': 70.0,
                    'weather_condition': 'clear'
                }
                
        except Exception as e:
            logger.warning(f"Weather data fetch error: {e}")
            return {
                'weather_temp': 28.0,
                'weather_humidity': 70.0,
                'weather_condition': 'clear'
            }
    
    def _make_prediction(self, features):
        """Make demand prediction using ensemble model"""
        # Prepare feature vector
        feature_vector = np.array([[features[col] for col in self.feature_columns]])
        feature_vector_scaled = self.scaler.transform(feature_vector)
        
        # Get predictions from ensemble
        rf_pred = self.model['rf'].predict(feature_vector_scaled)[0]
        gb_pred = self.model['gb'].predict(feature_vector_scaled)[0]
        
        # Weighted ensemble prediction
        prediction = (self.model['weights'][0] * rf_pred + 
                     self.model['weights'][1] * gb_pred)
        
        return max(0, prediction)  # Ensure non-negative demand
    
    def _calculate_confidence(self, location, prediction_time):
        """Calculate prediction confidence based on historical accuracy"""
        # Simple confidence calculation based on time of day and location
        hour = prediction_time.hour
        
        # Higher confidence during peak hours
        if hour in [8, 9, 18, 19]:
            base_confidence = 0.85
        elif hour in [7, 10, 17, 20]:
            base_confidence = 0.80
        else:
            base_confidence = 0.75
        
        # Adjust for weekend
        if prediction_time.weekday() >= 5:
            base_confidence *= 0.95
        
        return min(0.95, max(0.60, base_confidence))
    
    def _analyze_factors(self, features, prediction):
        """Analyze factors contributing to demand prediction"""
        factors = {}
        
        # Time factors
        if features['hour'] in [8, 9, 18, 19]:
            factors['peak_hour'] = 0.3
        elif features['hour'] in [7, 10, 17, 20]:
            factors['high_demand_hour'] = 0.2
        
        # Weather factors
        if features['weather_condition'] == 'rain':
            factors['weather_boost'] = 0.25
        elif features['weather_condition'] == 'clear':
            factors['good_weather'] = 0.1
        
        # Weekend factor
        if features['is_weekend']:
            factors['weekend_pattern'] = 0.15
        
        # Holiday factor
        if features['is_holiday']:
            factors['holiday_impact'] = 0.2
        
        return factors
    
    async def predict_demand_batch(self):
        """Predict demand for all major locations"""
        locations = [
            {"name": "Andheri East", "lat": 19.1136, "lon": 72.8697},
            {"name": "Bandra Kurla Complex", "lat": 19.0596, "lon": 72.8656},
            {"name": "Powai", "lat": 19.1197, "lon": 72.9056},
            {"name": "Malad West", "lat": 19.1875, "lon": 72.8304},
            {"name": "Lower Parel", "lat": 19.0176, "lon": 72.8286}
        ]
        
        predictions = []
        for location in locations:
            for hours_ahead in [1, 2, 4]:
                prediction = await self.predict_demand(
                    location["name"], 
                    location["lat"], 
                    location["lon"], 
                    hours_ahead
                )
                predictions.append({
                    **prediction,
                    "hours_ahead": hours_ahead
                })
        
        return predictions
    
    async def retrain_model(self):
        """Retrain model with latest data"""
        try:
            # Get latest ride data from database
            rides_data = await self._get_historical_rides()
            
            if len(rides_data) > 100:  # Minimum data requirement
                # Prepare features
                X, y = self._prepare_features(rides_data)
                
                # Retrain model
                self._train_model(X, y)
                
                logger.info("Demand forecasting model retrained successfully")
            else:
                logger.warning("Insufficient data for model retraining")
                
        except Exception as e:
            logger.error(f"Model retraining error: {e}")
    
    async def _get_historical_rides(self):
        """Get historical ride data from database"""
        try:
            # Get rides from last 30 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            rides = await self.db_manager.get_rides_by_date_range(start_date, end_date)
            
            # Convert to DataFrame and aggregate by hour and location
            if rides:
                df = pd.DataFrame(rides)
                # Aggregate rides by hour and location to get demand
                # This would be implemented based on your actual data structure
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error fetching historical rides: {e}")
            return pd.DataFrame()
    
    async def get_model_status(self):
        """Get current model status and performance metrics"""
        return {
            "model_type": "Ensemble (RandomForest + GradientBoosting)",
            "last_trained": datetime.now().isoformat(),
            "feature_count": len(self.feature_columns),
            "status": "active"
        }
