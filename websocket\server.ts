import { Server as HTTPServer } from 'http';
import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import connectDB from '../mongodb';
import { User } from '../models/User';
import { Ride } from '../models/Ride';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: 'rider' | 'driver';
  userData?: {
    _id: string;
    firstName: string;
    lastName: string;
    role: 'rider' | 'driver';
  };
}

interface LocationUpdate {
  latitude: number;
  longitude: number;
  heading?: number;
  speed?: number;
  accuracy?: number;
  timestamp: number;
}

interface ChatMessage {
  rideId: string;
  senderId: string;
  senderRole: 'rider' | 'driver';
  message: string;
  timestamp: number;
  messageType: 'text' | 'location' | 'system';
}

class WebSocketManager {
  private io: SocketIOServer;
  private connectedUsers: Map<string, AuthenticatedSocket> = new Map();
  private driverLocations: Map<string, LocationUpdate> = new Map();
  private activeRides: Map<string, { riderId: string; driverId: string; status: string }> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, JWT_SECRET) as any;
        
        await connectDB();
        const user = await User.findById(decoded.userId).select('-password');
        
        if (!user || !user.isActive) {
          return next(new Error('Invalid or inactive user'));
        }

        socket.userId = user._id.toString();
        socket.userRole = user.role;
        socket.userData = {
          _id: user._id.toString(),
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
        };

        next();
      } catch (error) {
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      console.log(`User connected: ${socket.userData?.firstName} (${socket.userRole})`);
      
      // Store connected user
      if (socket.userId) {
        this.connectedUsers.set(socket.userId, socket);
      }

      // Join user to their personal room
      socket.join(`user:${socket.userId}`);
      
      // Join driver to drivers room for broadcast notifications
      if (socket.userRole === 'driver') {
        socket.join('drivers');
      }

      this.setupSocketEvents(socket);

      socket.on('disconnect', () => {
        console.log(`User disconnected: ${socket.userData?.firstName}`);
        if (socket.userId) {
          this.connectedUsers.delete(socket.userId);
          
          // Remove driver location when they disconnect
          if (socket.userRole === 'driver') {
            this.driverLocations.delete(socket.userId);
            this.broadcastDriverLocationUpdate(socket.userId, null);
          }
        }
      });
    });
  }

  private setupSocketEvents(socket: AuthenticatedSocket) {
    // Driver location updates
    socket.on('driver:location-update', (locationData: LocationUpdate) => {
      if (socket.userRole === 'driver' && socket.userId) {
        this.driverLocations.set(socket.userId, {
          ...locationData,
          timestamp: Date.now()
        });
        
        this.broadcastDriverLocationUpdate(socket.userId, locationData);
      }
    });

    // Driver availability toggle
    socket.on('driver:availability-toggle', async (isAvailable: boolean) => {
      if (socket.userRole === 'driver' && socket.userId) {
        try {
          await User.findByIdAndUpdate(socket.userId, {
            'driverProfile.isAvailable': isAvailable
          });
          
          socket.broadcast.emit('driver:availability-changed', {
            driverId: socket.userId,
            isAvailable
          });
        } catch (error) {
          socket.emit('error', { message: 'Failed to update availability' });
        }
      }
    });

    // Join ride room for real-time updates
    socket.on('ride:join', (rideId: string) => {
      socket.join(`ride:${rideId}`);
    });

    // Leave ride room
    socket.on('ride:leave', (rideId: string) => {
      socket.leave(`ride:${rideId}`);
    });

    // Chat messages
    socket.on('chat:send-message', async (messageData: Omit<ChatMessage, 'timestamp'>) => {
      const message: ChatMessage = {
        ...messageData,
        timestamp: Date.now(),
        senderId: socket.userId!,
        senderRole: socket.userRole!
      };

      // Broadcast to ride room
      this.io.to(`ride:${messageData.rideId}`).emit('chat:new-message', message);
      
      // Store message in database (implement message model if needed)
      console.log('Chat message:', message);
    });

    // Ride status updates
    socket.on('ride:status-update', async (data: { rideId: string; status: string; location?: LocationUpdate }) => {
      try {
        const ride = await Ride.findById(data.rideId);
        if (!ride) return;

        // Verify user has permission to update this ride
        const canUpdate = (socket.userRole === 'rider' && ride.riderId.toString() === socket.userId) ||
                         (socket.userRole === 'driver' && ride.driverId?.toString() === socket.userId);
        
        if (!canUpdate) return;

        // Broadcast status update to ride room
        this.io.to(`ride:${data.rideId}`).emit('ride:status-changed', {
          rideId: data.rideId,
          status: data.status,
          updatedBy: socket.userRole,
          location: data.location,
          timestamp: Date.now()
        });

        // Update active rides tracking
        if (data.status === 'accepted' || data.status === 'in_progress') {
          this.activeRides.set(data.rideId, {
            riderId: ride.riderId.toString(),
            driverId: ride.driverId?.toString() || '',
            status: data.status
          });
        } else if (data.status === 'completed' || data.status === 'cancelled') {
          this.activeRides.delete(data.rideId);
        }

      } catch (error) {
        socket.emit('error', { message: 'Failed to update ride status' });
      }
    });

    // Emergency alert
    socket.on('emergency:alert', (data: { rideId: string; location: LocationUpdate; message?: string }) => {
      // Broadcast emergency alert to all connected users and emergency services
      this.io.emit('emergency:alert-received', {
        rideId: data.rideId,
        userId: socket.userId,
        userRole: socket.userRole,
        location: data.location,
        message: data.message,
        timestamp: Date.now()
      });
      
      console.log('EMERGENCY ALERT:', data);
    });
  }

  // Public methods for external use
  public sendRideRequest(driverId: string, rideData: any) {
    const driverSocket = this.connectedUsers.get(driverId);
    if (driverSocket) {
      driverSocket.emit('ride:new-request', rideData);
      return true;
    }
    return false;
  }

  public sendRideUpdate(userId: string, updateData: any) {
    const userSocket = this.connectedUsers.get(userId);
    if (userSocket) {
      userSocket.emit('ride:update', updateData);
      return true;
    }
    return false;
  }

  public broadcastToRide(rideId: string, event: string, data: any) {
    this.io.to(`ride:${rideId}`).emit(event, data);
  }

  public sendNotification(userId: string, notification: any) {
    const userSocket = this.connectedUsers.get(userId);
    if (userSocket) {
      userSocket.emit('notification', notification);
      return true;
    }
    return false;
  }

  private broadcastDriverLocationUpdate(driverId: string, location: LocationUpdate | null) {
    // Broadcast to riders looking for drivers
    this.io.to('riders').emit('driver:location-update', {
      driverId,
      location,
      timestamp: Date.now()
    });
  }

  public getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys());
  }

  public getDriverLocations(): Map<string, LocationUpdate> {
    return this.driverLocations;
  }

  public getActiveRides(): Map<string, any> {
    return this.activeRides;
  }
}

let wsManager: WebSocketManager | null = null;

export function initializeWebSocket(server: HTTPServer): WebSocketManager {
  if (!wsManager) {
    wsManager = new WebSocketManager(server);
  }
  return wsManager;
}

export function getWebSocketManager(): WebSocketManager | null {
  return wsManager;
}

export default WebSocketManager;
