import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { apmService } from '@/lib/monitoring/apm';
import { errorTrackingService } from '@/lib/monitoring/errorTracking';
import { healthMonitoringService } from '@/lib/monitoring/healthCheck';
import connectDB from '@/lib/mongodb';
import { User } from '@/lib/models/User';
import { Ride } from '@/lib/models/Ride';
import { Payment } from '@/lib/models/Payment';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Check if user is admin (you might want to add an admin role check)
    await connectDB();
    const user = await User.findById(decoded.userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '1h'; // 1h, 24h, 7d, 30d
    const metricType = searchParams.get('type') || 'all'; // all, performance, business, errors

    // Calculate time range
    const now = Date.now();
    let startTime: number;
    switch (timeRange) {
      case '1h':
        startTime = now - (60 * 60 * 1000);
        break;
      case '24h':
        startTime = now - (24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = now - (7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = now - (30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = now - (60 * 60 * 1000);
    }

    // Get metrics based on type
    const metrics: any = {};

    if (metricType === 'all' || metricType === 'performance') {
      metrics.performance = apmService.getMetricsSummary();
    }

    if (metricType === 'all' || metricType === 'errors') {
      metrics.errors = errorTrackingService.getErrorStats();
    }

    if (metricType === 'all' || metricType === 'health') {
      metrics.health = await healthMonitoringService.getCurrentHealth();
    }

    if (metricType === 'all' || metricType === 'business') {
      metrics.business = await getBusinessMetrics(startTime, now);
    }

    return NextResponse.json({
      success: true,
      data: {
        metrics,
        timeRange,
        startTime,
        endTime: now,
        timestamp: Date.now(),
      },
    });

  } catch (error) {
    console.error('Metrics API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

async function getBusinessMetrics(startTime: number, endTime: number) {
  try {
    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    // Get ride metrics
    const rideMetrics = await Ride.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$finalAmount' },
          avgAmount: { $avg: '$finalAmount' }
        }
      }
    ]);

    // Get payment metrics
    const paymentMetrics = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalAmount: { $sum: '$amount' },
          avgAmount: { $avg: '$amount' }
        }
      }
    ]);

    // Get user metrics
    const userMetrics = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    // Calculate key business metrics
    const totalRides = rideMetrics.reduce((sum, metric) => sum + metric.count, 0);
    const completedRides = rideMetrics.find(m => m._id === 'completed')?.count || 0;
    const totalRevenue = rideMetrics.reduce((sum, metric) => sum + (metric.totalAmount || 0), 0);
    const successfulPayments = paymentMetrics.find(m => m._id === 'completed')?.count || 0;
    const totalPaymentAmount = paymentMetrics.reduce((sum, metric) => sum + (metric.totalAmount || 0), 0);

    return {
      rides: {
        total: totalRides,
        completed: completedRides,
        completionRate: totalRides > 0 ? (completedRides / totalRides) * 100 : 0,
        revenue: totalRevenue,
        avgRideValue: completedRides > 0 ? totalRevenue / completedRides : 0,
        byStatus: rideMetrics,
      },
      payments: {
        total: paymentMetrics.reduce((sum, metric) => sum + metric.count, 0),
        successful: successfulPayments,
        totalAmount: totalPaymentAmount,
        avgPaymentValue: successfulPayments > 0 ? totalPaymentAmount / successfulPayments : 0,
        byStatus: paymentMetrics,
      },
      users: {
        total: userMetrics.reduce((sum, metric) => sum + metric.count, 0),
        byRole: userMetrics,
      },
      kpis: {
        rideCompletionRate: totalRides > 0 ? (completedRides / totalRides) * 100 : 0,
        paymentSuccessRate: paymentMetrics.reduce((sum, metric) => sum + metric.count, 0) > 0 
          ? (successfulPayments / paymentMetrics.reduce((sum, metric) => sum + metric.count, 0)) * 100 
          : 0,
        averageRideValue: completedRides > 0 ? totalRevenue / completedRides : 0,
        totalRevenue,
      },
    };
  } catch (error) {
    console.error('Error getting business metrics:', error);
    return {
      rides: { total: 0, completed: 0, completionRate: 0, revenue: 0 },
      payments: { total: 0, successful: 0, totalAmount: 0 },
      users: { total: 0, byRole: [] },
      kpis: { rideCompletionRate: 0, paymentSuccessRate: 0, averageRideValue: 0, totalRevenue: 0 },
    };
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
