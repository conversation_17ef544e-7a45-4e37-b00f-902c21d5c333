import { NextRequest, NextResponse } from 'next/server';
import { healthMonitoringService } from '@/lib/monitoring/healthCheck';
import { apmService } from '@/lib/monitoring/apm';
import { errorTrackingService } from '@/lib/monitoring/errorTracking';

export async function GET(request: NextRequest) {
  try {
    // Get current system health
    const systemHealth = await healthMonitoringService.getCurrentHealth();
    
    // Get additional metrics
    const apmSummary = apmService.getMetricsSummary();
    const errorStats = errorTrackingService.getErrorStats();
    
    // Calculate response time
    const startTime = Date.now();
    const responseTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      data: {
        health: systemHealth,
        metrics: {
          apm: apmSummary,
          errors: errorStats,
          responseTime,
        },
        timestamp: Date.now(),
      },
    });

  } catch (error) {
    console.error('Health check API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Simple health check endpoint for load balancers
export async function HEAD(request: NextRequest) {
  try {
    const health = await healthMonitoringService.getCurrentHealth();
    
    if (health.overall === 'healthy') {
      return new NextResponse(null, { status: 200 });
    } else if (health.overall === 'degraded') {
      return new NextResponse(null, { status: 200 }); // Still serve traffic but with warning
    } else {
      return new NextResponse(null, { status: 503 }); // Service unavailable
    }
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
