const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');

// Notification schema
const notificationSchema = new mongoose.Schema({
  userId: { type: String, required: true },
  type: { type: String, required: true },
  title: { type: String, required: true },
  message: { type: String, required: true },
  data: { type: Object, default: {} },
  read: { type: Boolean, default: false },
  priority: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' },
  createdAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) } // 7 days
});

const Notification = mongoose.model('Notification', notificationSchema);

// Send notification
router.post('/send', async (req, res) => {
  const { userId, type, title, message, data, priority } = req.body;
  const io = req.app.get('io');
  const logger = req.app.get('logger');

  try {
    // Create notification in database
    const notification = new Notification({
      userId,
      type,
      title,
      message,
      data,
      priority
    });

    await notification.save();

    // Send real-time notification
    const notificationData = {
      id: notification._id,
      type,
      title,
      message,
      data,
      priority,
      timestamp: notification.createdAt
    };

    // Send to specific user
    io.to(`user_${userId}`).emit('notification', notificationData);
    
    // Also send to driver if it's a driver notification
    if (type.includes('driver')) {
      io.to(`driver_${userId}`).emit('notification', notificationData);
    }

    logger.info(`Notification sent to user ${userId}: ${title}`);

    res.json({ success: true, notification: notificationData });
  } catch (error) {
    logger.error('Notification send error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get user notifications
router.get('/user/:userId', async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20, unreadOnly = false } = req.query;

  try {
    const query = { userId };
    if (unreadOnly === 'true') {
      query.read = false;
    }

    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .exec();

    const total = await Notification.countDocuments(query);
    const unreadCount = await Notification.countDocuments({ userId, read: false });

    res.json({
      notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Mark notification as read
router.patch('/:notificationId/read', async (req, res) => {
  const { notificationId } = req.params;

  try {
    const notification = await Notification.findByIdAndUpdate(
      notificationId,
      { read: true },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json({ success: true, notification });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Mark all notifications as read for a user
router.patch('/user/:userId/read-all', async (req, res) => {
  const { userId } = req.params;

  try {
    await Notification.updateMany(
      { userId, read: false },
      { read: true }
    );

    res.json({ success: true, message: 'All notifications marked as read' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete notification
router.delete('/:notificationId', async (req, res) => {
  const { notificationId } = req.params;

  try {
    const notification = await Notification.findByIdAndDelete(notificationId);

    if (!notification) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    res.json({ success: true, message: 'Notification deleted' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Bulk notification sending (for admin/system use)
router.post('/bulk', async (req, res) => {
  const { userIds, type, title, message, data, priority } = req.body;
  const io = req.app.get('io');
  const logger = req.app.get('logger');

  try {
    const notifications = userIds.map(userId => ({
      userId,
      type,
      title,
      message,
      data,
      priority
    }));

    const savedNotifications = await Notification.insertMany(notifications);

    // Send real-time notifications
    savedNotifications.forEach(notification => {
      const notificationData = {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        timestamp: notification.createdAt
      };

      io.to(`user_${notification.userId}`).emit('notification', notificationData);
      
      if (type.includes('driver')) {
        io.to(`driver_${notification.userId}`).emit('notification', notificationData);
      }
    });

    logger.info(`Bulk notification sent to ${userIds.length} users: ${title}`);

    res.json({ 
      success: true, 
      message: `Notifications sent to ${userIds.length} users`,
      count: savedNotifications.length
    });
  } catch (error) {
    logger.error('Bulk notification error:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
