"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { CreditCard, Trash2, Plus, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export function PaymentMethods() {
  const [showAddCard, setShowAddCard] = useState(false)

  // Sample payment methods
  const paymentMethods = [
    {
      id: 1,
      type: "credit",
      name: "HDFC Bank Credit Card",
      number: "••••••••••••4582",
      expiry: "05/26",
      isDefault: true,
    },
    {
      id: 2,
      type: "debit",
      name: "SBI Debit Card",
      number: "••••••••••••7891",
      expiry: "09/25",
      isDefault: false,
    },
    {
      id: 3,
      type: "upi",
      name: "UPI",
      number: "user@okbank",
      isDefault: false,
    },
  ]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Your Payment Methods</CardTitle>
          <CardDescription>Manage your saved payment methods</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {paymentMethods.map((method) => (
            <div key={method.id} className="flex items-center justify-between border rounded-lg p-4">
              <div className="flex items-center gap-4">
                {method.type === "upi" ? (
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <img src="/placeholder.svg?height=24&width=24" alt="UPI" className="h-6 w-6" />
                  </div>
                ) : (
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <CreditCard className="h-5 w-5 text-primary" />
                  </div>
                )}
                <div>
                  <p className="font-medium">{method.name}</p>
                  <p className="text-sm text-muted-foreground">{method.number}</p>
                  {method.expiry && <p className="text-xs text-muted-foreground">Expires: {method.expiry}</p>}
                </div>
              </div>
              <div className="flex items-center gap-4">
                {method.isDefault && (
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">Default</span>
                )}
                <Button variant="ghost" size="icon">
                  <Trash2 className="h-4 w-4 text-destructive" />
                </Button>
              </div>
            </div>
          ))}

          {!showAddCard && (
            <Button variant="outline" className="w-full" onClick={() => setShowAddCard(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add New Payment Method
            </Button>
          )}

          {showAddCard && (
            <Card>
              <CardHeader>
                <CardTitle>Add New Card</CardTitle>
                <CardDescription>Enter your card details to save for future payments</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="card-name">Name on Card</Label>
                  <Input id="card-name" placeholder="Enter name as on card" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="card-number">Card Number</Label>
                  <Input id="card-number" placeholder="1234 5678 9012 3456" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expiry">Expiry Date</Label>
                    <Select>
                      <SelectTrigger id="expiry">
                        <SelectValue placeholder="MM/YY" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="01/25">01/25</SelectItem>
                        <SelectItem value="02/25">02/25</SelectItem>
                        <SelectItem value="03/25">03/25</SelectItem>
                        {/* More options would be generated dynamically */}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cvv">CVV</Label>
                    <Input id="cvv" placeholder="123" type="password" maxLength={3} />
                  </div>
                </div>

                <div className="flex items-center space-x-2 pt-2">
                  <Switch id="make-default" />
                  <Label htmlFor="make-default">Make this my default payment method</Label>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Your card details are encrypted and stored securely. We comply with PCI DSS standards.
                  </AlertDescription>
                </Alert>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setShowAddCard(false)}>
                  Cancel
                </Button>
                <Button>Save Card</Button>
              </CardFooter>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

