# Development Workflow Automation

This directory contains comprehensive development workflow automation tools for the AI-powered two-wheeler sharing platform.

## 🚀 Quick Start

### Prerequisites
```bash
# Install required dependencies
pip install -r development-workflow/requirements.txt

# Set up environment variables
export GITHUB_TOKEN="your_github_token"
export LINEAR_TOKEN="your_linear_token"
export SUPABASE_URL="your_supabase_url"
export SUPABASE_ANON_KEY="your_supabase_key"
```

### Basic Usage
```bash
# Make the workflow script executable
chmod +x development-workflow/dev-workflow.py

# Update a feature flag
python development-workflow/dev-workflow.py update-flag --flag ai_enhanced_ride_matching --enabled true

# Create a feature PR from an issue
python development-workflow/dev-workflow.py create-feature-pr --issue 123 --description "Implement quantum optimization"

# Query Supabase table
python development-workflow/dev-workflow.py query-db --table users --limit 10

# Generate tests for a file
python development-workflow/dev-workflow.py generate-tests --file ml-service/demand_prediction.py
```

## 📋 Available Commands

### 1. Feature Flag Management

#### Update Feature Flags
```bash
# Enable a feature flag for all environments
python dev-workflow.py update-flag --flag autonomous_vehicle_control --enabled true

# Enable for specific environment
python dev-workflow.py update-flag --flag ar_navigation --enabled true --environment development

# Disable a feature flag
python dev-workflow.py update-flag --flag quantum_optimization --enabled false
```

#### Create Feature Flag PR
```bash
# Create a pull request for feature flag changes
python dev-workflow.py create-flag-pr
```

**Example Feature Flags:**
- `ai_enhanced_ride_matching` - AI-powered ride matching
- `autonomous_vehicle_control` - Autonomous vehicle features
- `carbon_neutrality_optimization` - Sustainability features
- `voice_ai_commands` - Voice AI integration
- `ar_navigation` - Augmented reality navigation
- `quantum_optimization` - Quantum computing features
- `neural_interface_integration` - Neural interface capabilities

### 2. Code Refactoring

#### Move Functions Between Files
```bash
# Move specific functions from one file to another
python dev-workflow.py refactor \
  --source ml-service/optimization.py \
  --target ml-service/quantum_optimization.py \
  --functions optimize_routes calculate_efficiency
```

**Best Practices:**
- Maintain coding conventions and style
- Ensure proper imports are updated
- Run tests after refactoring
- Update documentation

### 3. Feature Development

#### Create Feature PR from Issue
```bash
# Create a new feature branch and PR from GitHub issue
python dev-workflow.py create-feature-pr \
  --issue 456 \
  --description "Implement neural interface for thought-based navigation"
```

**Generated Structure:**
```
features/issue-456/
├── README.md
├── implementation.py
└── tests.py
```

### 4. Database Operations

#### Query Supabase Tables
```bash
# View table contents
python dev-workflow.py query-db --table rides --limit 20
python dev-workflow.py query-db --table drivers --limit 10
python dev-workflow.py query-db --table vehicles --limit 15
```

**Available Tables:**
- `users` - User profiles and data
- `drivers` - Driver information
- `vehicles` - Vehicle fleet data
- `rides` - Ride history and analytics
- `payments` - Payment transactions
- `analytics` - Performance metrics

### 5. Project Management

#### Create Linear Tickets
```bash
# Create a new Linear ticket
python dev-workflow.py create-ticket \
  --title "Implement quantum route optimization" \
  --description "Add quantum computing capabilities for route optimization" \
  --team-id "team_123"
```

### 6. Test Coverage

#### Generate Unit Tests
```bash
# Generate tests for a specific file
python dev-workflow.py generate-tests --file components/ride-matching.tsx
python dev-workflow.py generate-tests --file ml-service/prediction_engine.py
```

**Generated Test Features:**
- Unit test structure
- Mock setup and teardown
- Test placeholders for all public functions
- Proper imports and dependencies

## 🔧 Configuration

### Feature Flags Configuration
The `feature-flags-config.json` file controls all feature flags:

```json
{
  "feature_flags": {
    "ai_enhanced_ride_matching": {
      "enabled": true,
      "description": "Enable AI-enhanced ride matching with ML predictions",
      "environments": {
        "development": true,
        "staging": true,
        "production": true
      },
      "rollout_percentage": 100,
      "dependencies": ["ml_service", "demand_prediction"]
    }
  }
}
```

### Environment Variables
```bash
# GitHub integration
GITHUB_TOKEN=ghp_xxxxxxxxxxxx

# Linear integration
LINEAR_TOKEN=lin_api_xxxxxxxxxxxx

# Supabase integration
SUPABASE_URL=https://xxxxxxxxxxxx.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Optional: Slack notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
```

## 🤖 GitHub Actions Integration

The workflow automatically:

1. **Validates feature flags** on PR creation
2. **Runs code quality checks** (linting, formatting)
3. **Generates tests** for new files
4. **Updates documentation** on main branch pushes
5. **Performs security scans** on all PRs
6. **Checks deployment readiness** for production PRs

### Workflow Triggers
- **Pull Request**: Code quality, test generation, security scan
- **Push to Main**: Documentation updates, deployment checks
- **Manual Trigger**: Custom workflow actions

## 📝 Pull Request Template

The PR template automatically includes:

- **Change type classification** (bug fix, feature, breaking change)
- **Testing checklist** (unit, integration, e2e tests)
- **Security considerations** (compliance, privacy)
- **Deployment readiness** (feature flags, rollback plan)
- **Documentation requirements** (README, API docs)

## 🔍 Development Best Practices

### Feature Flag Guidelines
1. **Always use feature flags** for new features
2. **Start with development environment** only
3. **Gradual rollout** to staging then production
4. **Document dependencies** and rollback plans
5. **Clean up old flags** regularly

### Code Quality Standards
1. **100% test coverage** for new code
2. **Linting and formatting** must pass
3. **Security scans** must be clean
4. **Documentation** must be updated
5. **Performance impact** must be assessed

### Refactoring Guidelines
1. **Maintain backward compatibility** when possible
2. **Update all imports** and dependencies
3. **Run full test suite** after changes
4. **Update documentation** and comments
5. **Consider performance implications**

## 🚨 Troubleshooting

### Common Issues

#### Feature Flag Not Working
```bash
# Check feature flag configuration
cat development-workflow/feature-flags-config.json | jq '.feature_flags.your_flag_name'

# Verify environment settings
python -c "
import json
with open('development-workflow/feature-flags-config.json') as f:
    config = json.load(f)
    print(config['feature_flags']['your_flag_name']['environments'])
"
```

#### Refactoring Failed
```bash
# Check file permissions
ls -la source_file.py target_file.py

# Verify syntax after refactoring
python -m py_compile target_file.py

# Run tests to ensure functionality
python -m pytest tests/
```

#### Database Query Failed
```bash
# Test Supabase connection
curl -H "apikey: $SUPABASE_ANON_KEY" "$SUPABASE_URL/rest/v1/"

# Check table permissions
python -c "
import requests
response = requests.get(
    '$SUPABASE_URL/rest/v1/your_table?limit=1',
    headers={'apikey': '$SUPABASE_ANON_KEY'}
)
print(response.status_code, response.text)
"
```

## 📊 Monitoring and Analytics

### Workflow Metrics
- **PR creation time**: Average time from issue to PR
- **Test coverage**: Percentage of code covered by tests
- **Feature flag adoption**: Usage statistics per flag
- **Deployment frequency**: Number of deployments per week
- **Bug detection rate**: Issues caught by automation

### Performance Tracking
- **Build times**: CI/CD pipeline performance
- **Test execution time**: Test suite performance
- **Code quality scores**: Linting and complexity metrics
- **Security scan results**: Vulnerability detection

## 🔮 Future Enhancements

### Planned Features
1. **AI-powered code review** suggestions
2. **Automated dependency updates** with testing
3. **Performance regression detection** in CI
4. **Smart test selection** based on code changes
5. **Automated documentation generation** from code
6. **Integration with more project management tools**

### Advanced Workflows
1. **Canary deployments** with automatic rollback
2. **A/B testing** for feature flags
3. **Chaos engineering** integration
4. **Multi-environment** promotion pipelines
5. **Compliance automation** for enterprise requirements
