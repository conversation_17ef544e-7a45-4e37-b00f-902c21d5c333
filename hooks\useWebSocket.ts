'use client';

import { useEffect, useRef, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAuth } from '../contexts/AuthContext';

interface LocationUpdate {
  latitude: number;
  longitude: number;
  heading?: number;
  speed?: number;
  accuracy?: number;
  timestamp: number;
}

interface ChatMessage {
  rideId: string;
  senderId: string;
  senderRole: 'rider' | 'driver';
  message: string;
  timestamp: number;
  messageType: 'text' | 'location' | 'system';
}

interface RideUpdate {
  rideId: string;
  status: string;
  updatedBy: 'rider' | 'driver';
  location?: LocationUpdate;
  timestamp: number;
}

interface Notification {
  id: string;
  type: 'ride_request' | 'ride_update' | 'chat_message' | 'emergency' | 'system';
  title: string;
  message: string;
  data?: any;
  timestamp: number;
  read: boolean;
}

export function useWebSocket() {
  const { token, user, isAuthenticated } = useAuth();
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [chatMessages, setChatMessages] = useState<{ [rideId: string]: ChatMessage[] }>({});
  const [rideUpdates, setRideUpdates] = useState<RideUpdate[]>([]);
  const [driverLocations, setDriverLocations] = useState<{ [driverId: string]: LocationUpdate }>({});

  // Initialize WebSocket connection
  useEffect(() => {
    if (isAuthenticated && token && user) {
      const socket = io(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling']
      });

      socketRef.current = socket;

      // Connection events
      socket.on('connect', () => {
        console.log('WebSocket connected');
        setIsConnected(true);
      });

      socket.on('disconnect', () => {
        console.log('WebSocket disconnected');
        setIsConnected(false);
      });

      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        setIsConnected(false);
      });

      // Ride request events
      socket.on('ride:new-request', (rideData) => {
        const notification: Notification = {
          id: `ride_request_${Date.now()}`,
          type: 'ride_request',
          title: 'New Ride Request',
          message: `Ride request from ${rideData.rider?.firstName} ${rideData.rider?.lastName}`,
          data: rideData,
          timestamp: Date.now(),
          read: false
        };
        setNotifications(prev => [notification, ...prev]);
      });

      // Ride status updates
      socket.on('ride:status-changed', (updateData: RideUpdate) => {
        setRideUpdates(prev => [updateData, ...prev.slice(0, 49)]); // Keep last 50 updates
        
        const notification: Notification = {
          id: `ride_update_${updateData.rideId}_${Date.now()}`,
          type: 'ride_update',
          title: 'Ride Status Updated',
          message: `Ride status changed to ${updateData.status}`,
          data: updateData,
          timestamp: updateData.timestamp,
          read: false
        };
        setNotifications(prev => [notification, ...prev]);
      });

      // Chat messages
      socket.on('chat:new-message', (message: ChatMessage) => {
        setChatMessages(prev => ({
          ...prev,
          [message.rideId]: [...(prev[message.rideId] || []), message]
        }));

        // Add notification for chat messages from other users
        if (message.senderId !== user._id) {
          const notification: Notification = {
            id: `chat_${message.rideId}_${Date.now()}`,
            type: 'chat_message',
            title: 'New Message',
            message: `${message.senderRole === 'rider' ? 'Rider' : 'Driver'}: ${message.message}`,
            data: message,
            timestamp: message.timestamp,
            read: false
          };
          setNotifications(prev => [notification, ...prev]);
        }
      });

      // Driver location updates
      socket.on('driver:location-update', (data: { driverId: string; location: LocationUpdate | null }) => {
        setDriverLocations(prev => {
          const updated = { ...prev };
          if (data.location) {
            updated[data.driverId] = data.location;
          } else {
            delete updated[data.driverId];
          }
          return updated;
        });
      });

      // Emergency alerts
      socket.on('emergency:alert-received', (alertData) => {
        const notification: Notification = {
          id: `emergency_${Date.now()}`,
          type: 'emergency',
          title: 'Emergency Alert',
          message: `Emergency alert from ${alertData.userRole}`,
          data: alertData,
          timestamp: alertData.timestamp,
          read: false
        };
        setNotifications(prev => [notification, ...prev]);
      });

      // General notifications
      socket.on('notification', (notificationData) => {
        const notification: Notification = {
          id: `notification_${Date.now()}`,
          type: 'system',
          ...notificationData,
          timestamp: Date.now(),
          read: false
        };
        setNotifications(prev => [notification, ...prev]);
      });

      return () => {
        socket.disconnect();
        socketRef.current = null;
        setIsConnected(false);
      };
    }
  }, [isAuthenticated, token, user]);

  // WebSocket methods
  const joinRide = (rideId: string) => {
    socketRef.current?.emit('ride:join', rideId);
  };

  const leaveRide = (rideId: string) => {
    socketRef.current?.emit('ride:leave', rideId);
  };

  const sendChatMessage = (rideId: string, message: string, messageType: 'text' | 'location' = 'text') => {
    if (socketRef.current && user) {
      const messageData = {
        rideId,
        message,
        messageType
      };
      socketRef.current.emit('chat:send-message', messageData);
    }
  };

  const updateDriverLocation = (location: LocationUpdate) => {
    if (socketRef.current && user?.role === 'driver') {
      socketRef.current.emit('driver:location-update', location);
    }
  };

  const toggleDriverAvailability = (isAvailable: boolean) => {
    if (socketRef.current && user?.role === 'driver') {
      socketRef.current.emit('driver:availability-toggle', isAvailable);
    }
  };

  const updateRideStatus = (rideId: string, status: string, location?: LocationUpdate) => {
    if (socketRef.current) {
      socketRef.current.emit('ride:status-update', { rideId, status, location });
    }
  };

  const sendEmergencyAlert = (rideId: string, location: LocationUpdate, message?: string) => {
    if (socketRef.current) {
      socketRef.current.emit('emergency:alert', { rideId, location, message });
    }
  };

  const markNotificationAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const getChatMessages = (rideId: string): ChatMessage[] => {
    return chatMessages[rideId] || [];
  };

  const getUnreadNotifications = (): Notification[] => {
    return notifications.filter(n => !n.read);
  };

  return {
    // Connection state
    isConnected,
    
    // Data
    notifications,
    chatMessages,
    rideUpdates,
    driverLocations,
    
    // Methods
    joinRide,
    leaveRide,
    sendChatMessage,
    updateDriverLocation,
    toggleDriverAvailability,
    updateRideStatus,
    sendEmergencyAlert,
    markNotificationAsRead,
    clearNotifications,
    getChatMessages,
    getUnreadNotifications,
    
    // Computed values
    unreadCount: getUnreadNotifications().length,
    hasUnreadMessages: getUnreadNotifications().length > 0
  };
}
