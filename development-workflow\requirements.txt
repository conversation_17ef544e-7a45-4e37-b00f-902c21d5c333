# Development Workflow Dependencies
# Core Python packages for development automation

# HTTP requests and API integration
requests>=2.31.0
urllib3>=2.0.0

# JSON and YAML processing
pyyaml>=6.0.1
jsonschema>=4.19.0

# Git integration
GitPython>=3.1.37

# Code analysis and AST parsing
ast-tools>=0.1.0
astunparse>=1.6.3

# Testing frameworks
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.1
unittest-xml-reporting>=3.2.0

# Code quality tools
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
pylint>=2.17.0
isort>=5.12.0

# Documentation generation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
pydoc-markdown>=4.8.0
mkdocs>=1.5.0
mkdocs-material>=9.2.0

# Performance testing
locust>=2.16.0
memory-profiler>=0.61.0

# Security scanning
bandit>=1.7.5
safety>=2.3.0

# Database and API clients
psycopg2-binary>=2.9.7
supabase>=1.0.4
redis>=4.6.0

# Linear and project management
linear-sdk>=1.0.0

# GitHub API integration
PyGithub>=1.59.0
github3.py>=4.0.1

# Slack integration (optional)
slack-sdk>=3.21.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0

# Utilities
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.5.0
tabulate>=0.9.0

# Date and time handling
python-dateutil>=2.8.2
pytz>=2023.3

# Async support
aiohttp>=3.8.5
asyncio-mqtt>=0.13.0

# Configuration management
configparser>=5.3.0
toml>=0.10.2

# Logging and monitoring
structlog>=23.1.0
sentry-sdk>=1.29.0

# Development tools
pre-commit>=3.3.0
tox>=4.6.0
nox>=2023.4.22

# Type checking
types-requests>=2.31.0
types-PyYAML>=6.0.12
types-python-dateutil>=2.8.19

# Jupyter notebook support (for data analysis)
jupyter>=1.0.0
ipykernel>=6.25.0

# Machine learning utilities (for optimization)
scikit-learn>=1.3.0
scipy>=1.11.0

# Web scraping (for competitive analysis)
beautifulsoup4>=4.12.0
selenium>=4.11.0

# Image processing (for documentation)
Pillow>=10.0.0

# Encryption and security
cryptography>=41.0.0
bcrypt>=4.0.0

# Environment and deployment
docker>=6.1.0
kubernetes>=27.2.0

# Monitoring and observability
prometheus-client>=0.17.0
opentelemetry-api>=1.19.0

# Development server
uvicorn>=0.23.0
fastapi>=0.101.0

# File processing
openpyxl>=3.1.0
python-magic>=0.4.27

# Network utilities
ping3>=4.0.4
speedtest-cli>=2.1.3

# Caching
diskcache>=5.6.0
cachetools>=5.3.0
