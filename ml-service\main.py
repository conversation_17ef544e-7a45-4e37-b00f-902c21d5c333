from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Optional
import uvicorn
import logging
from datetime import datetime, timedelta
import os
from contextlib import asynccontextmanager

from services.demand_forecasting import DemandForecastingService
from services.fleet_positioning import FleetPositioningService
from services.revenue_optimization import RevenueOptimizationService
from services.maintenance_prediction import MaintenancePredictionService
from utils.database import DatabaseManager
from utils.cache import CacheManager
from utils.scheduler import MLScheduler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global services
db_manager = None
cache_manager = None
demand_service = None
fleet_service = None
revenue_service = None
maintenance_service = None
scheduler = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global db_manager, cache_manager, demand_service, fleet_service, revenue_service, maintenance_service, scheduler
    
    logger.info("Starting ML Service...")
    
    # Initialize managers
    db_manager = DatabaseManager()
    cache_manager = CacheManager()
    
    # Initialize ML services
    demand_service = DemandForecastingService(db_manager, cache_manager)
    fleet_service = FleetPositioningService(db_manager, cache_manager)
    revenue_service = RevenueOptimizationService(db_manager, cache_manager)
    maintenance_service = MaintenancePredictionService(db_manager, cache_manager)
    
    # Initialize scheduler
    scheduler = MLScheduler(demand_service, fleet_service, revenue_service, maintenance_service)
    scheduler.start()
    
    logger.info("ML Service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down ML Service...")
    if scheduler:
        scheduler.stop()
    logger.info("ML Service shutdown complete")

app = FastAPI(
    title="Two-Wheeler Sharing ML Service",
    description="Advanced predictive analytics and ML capabilities for ride-sharing platform",
    version="1.0.0",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class DemandPredictionRequest(BaseModel):
    location: str
    latitude: float
    longitude: float
    hours_ahead: int = 2
    include_weather: bool = True

class DemandPredictionResponse(BaseModel):
    location: str
    predicted_demand: float
    confidence: float
    factors: Dict[str, float]
    timestamp: datetime
    valid_until: datetime

class FleetPositioningRequest(BaseModel):
    current_drivers: List[Dict]
    time_horizon: int = 60  # minutes

class FleetPositioningResponse(BaseModel):
    recommendations: List[Dict]
    demand_hotspots: List[Dict]
    efficiency_score: float

class PricingOptimizationRequest(BaseModel):
    base_distance: float
    current_demand: str
    weather_condition: str
    time_of_day: str
    driver_availability: int
    historical_data: Optional[Dict] = None

class PricingOptimizationResponse(BaseModel):
    recommended_price: float
    surge_multiplier: float
    confidence: float
    factors: Dict[str, float]
    ab_test_variant: str

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "services": {
            "demand_forecasting": "active",
            "fleet_positioning": "active", 
            "revenue_optimization": "active",
            "maintenance_prediction": "active"
        }
    }

# Demand forecasting endpoints
@app.post("/predict/demand", response_model=DemandPredictionResponse)
async def predict_demand(request: DemandPredictionRequest):
    try:
        prediction = await demand_service.predict_demand(
            location=request.location,
            latitude=request.latitude,
            longitude=request.longitude,
            hours_ahead=request.hours_ahead,
            include_weather=request.include_weather
        )
        return prediction
    except Exception as e:
        logger.error(f"Demand prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/predict/demand/batch")
async def predict_demand_batch():
    """Predict demand for all major locations"""
    try:
        predictions = await demand_service.predict_demand_batch()
        return {"predictions": predictions, "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"Batch demand prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Fleet positioning endpoints
@app.post("/optimize/fleet-positioning", response_model=FleetPositioningResponse)
async def optimize_fleet_positioning(request: FleetPositioningRequest):
    try:
        optimization = await fleet_service.optimize_positioning(
            current_drivers=request.current_drivers,
            time_horizon=request.time_horizon
        )
        return optimization
    except Exception as e:
        logger.error(f"Fleet positioning error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analyze/demand-hotspots")
async def analyze_demand_hotspots():
    try:
        hotspots = await fleet_service.identify_demand_hotspots()
        return {"hotspots": hotspots, "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"Hotspot analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Revenue optimization endpoints
@app.post("/optimize/pricing", response_model=PricingOptimizationResponse)
async def optimize_pricing(request: PricingOptimizationRequest):
    try:
        optimization = await revenue_service.optimize_pricing(
            base_distance=request.base_distance,
            current_demand=request.current_demand,
            weather_condition=request.weather_condition,
            time_of_day=request.time_of_day,
            driver_availability=request.driver_availability,
            historical_data=request.historical_data
        )
        return optimization
    except Exception as e:
        logger.error(f"Pricing optimization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analyze/revenue-trends")
async def analyze_revenue_trends():
    try:
        trends = await revenue_service.analyze_revenue_trends()
        return {"trends": trends, "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"Revenue analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Maintenance prediction endpoints
@app.get("/predict/maintenance")
async def predict_maintenance():
    try:
        predictions = await maintenance_service.predict_maintenance_needs()
        return {"predictions": predictions, "timestamp": datetime.now()}
    except Exception as e:
        logger.error(f"Maintenance prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/analyze/vehicle-health/{vehicle_id}")
async def analyze_vehicle_health(vehicle_id: str):
    try:
        health = await maintenance_service.analyze_vehicle_health(vehicle_id)
        return health
    except Exception as e:
        logger.error(f"Vehicle health analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Model management endpoints
@app.post("/models/retrain")
async def retrain_models(background_tasks: BackgroundTasks):
    background_tasks.add_task(retrain_all_models)
    return {"message": "Model retraining started", "timestamp": datetime.now()}

@app.get("/models/status")
async def get_model_status():
    try:
        status = {
            "demand_model": await demand_service.get_model_status(),
            "fleet_model": await fleet_service.get_model_status(),
            "revenue_model": await revenue_service.get_model_status(),
            "maintenance_model": await maintenance_service.get_model_status()
        }
        return status
    except Exception as e:
        logger.error(f"Model status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def retrain_all_models():
    """Background task to retrain all ML models"""
    try:
        logger.info("Starting model retraining...")
        
        await demand_service.retrain_model()
        await fleet_service.retrain_model()
        await revenue_service.retrain_model()
        await maintenance_service.retrain_model()
        
        logger.info("Model retraining completed successfully")
    except Exception as e:
        logger.error(f"Model retraining failed: {e}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8081)
