'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  MessageCircle, 
  Wifi,
  WifiOff,
  MapPin
} from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="max-w-md w-full shadow-soft">
            <CardHeader className="text-center">
              <div className="mx-auto bg-red-100 w-16 h-16 flex items-center justify-center rounded-full mb-4">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <CardTitle className="text-xl">Oops! Something went wrong</CardTitle>
              <CardDescription>
                We encountered an unexpected error. Don't worry, our team has been notified.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {this.state.error?.message || 'An unexpected error occurred'}
                </AlertDescription>
              </Alert>
              
              <div className="flex space-x-2">
                <Button onClick={this.handleRetry} className="flex-1">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                <Button variant="outline" onClick={this.handleGoHome} className="flex-1">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>
              
              <Button variant="ghost" className="w-full" onClick={() => window.location.href = '/support'}>
                <MessageCircle className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Network Error Component
interface NetworkErrorProps {
  onRetry: () => void;
  message?: string;
}

export function NetworkError({ onRetry, message = 'Network connection failed' }: NetworkErrorProps) {
  return (
    <Card className="max-w-md mx-auto shadow-soft">
      <CardHeader className="text-center">
        <div className="mx-auto bg-orange-100 w-16 h-16 flex items-center justify-center rounded-full mb-4">
          <WifiOff className="h-8 w-8 text-orange-600" />
        </div>
        <CardTitle>Connection Problem</CardTitle>
        <CardDescription>{message}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <WifiOff className="h-4 w-4" />
          <AlertDescription>
            Please check your internet connection and try again.
          </AlertDescription>
        </Alert>
        <Button onClick={onRetry} className="w-full">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry Connection
        </Button>
      </CardContent>
    </Card>
  );
}

// Location Error Component
interface LocationErrorProps {
  onRetry: () => void;
  onSkip?: () => void;
  message?: string;
}

export function LocationError({ 
  onRetry, 
  onSkip, 
  message = 'Unable to access your location' 
}: LocationErrorProps) {
  return (
    <Card className="max-w-md mx-auto shadow-soft">
      <CardHeader className="text-center">
        <div className="mx-auto bg-blue-100 w-16 h-16 flex items-center justify-center rounded-full mb-4">
          <MapPin className="h-8 w-8 text-blue-600" />
        </div>
        <CardTitle>Location Access Needed</CardTitle>
        <CardDescription>{message}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <MapPin className="h-4 w-4" />
          <AlertDescription>
            We need location access to find nearby drivers and provide accurate pickup locations.
          </AlertDescription>
        </Alert>
        <div className="space-y-2">
          <Button onClick={onRetry} className="w-full">
            <MapPin className="h-4 w-4 mr-2" />
            Enable Location Access
          </Button>
          {onSkip && (
            <Button variant="outline" onClick={onSkip} className="w-full">
              Enter Location Manually
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Generic Error Component
interface GenericErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  onGoBack?: () => void;
  showSupport?: boolean;
}

export function GenericError({ 
  title = 'Something went wrong',
  message = 'We encountered an unexpected error. Please try again.',
  onRetry,
  onGoBack,
  showSupport = true
}: GenericErrorProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-md w-full shadow-soft">
        <CardHeader className="text-center">
          <div className="mx-auto bg-red-100 w-16 h-16 flex items-center justify-center rounded-full mb-4">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            {onRetry && (
              <Button onClick={onRetry} className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
            {onGoBack && (
              <Button variant="outline" onClick={onGoBack} className="flex-1">
                Go Back
              </Button>
            )}
          </div>
          
          {showSupport && (
            <Button variant="ghost" className="w-full" onClick={() => window.location.href = '/support'}>
              <MessageCircle className="h-4 w-4 mr-2" />
              Contact Support
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Form Error Component
interface FormErrorProps {
  errors: { [key: string]: string };
  className?: string;
}

export function FormError({ errors, className = '' }: FormErrorProps) {
  const errorMessages = Object.values(errors).filter(Boolean);
  
  if (errorMessages.length === 0) return null;

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription>
        <ul className="list-disc list-inside space-y-1">
          {errorMessages.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
}

// Inline Error Component
interface InlineErrorProps {
  message: string;
  className?: string;
}

export function InlineError({ message, className = '' }: InlineErrorProps) {
  return (
    <div className={`flex items-center space-x-2 text-red-600 text-sm ${className}`}>
      <AlertTriangle className="h-4 w-4" />
      <span>{message}</span>
    </div>
  );
}

// Success Message Component
interface SuccessMessageProps {
  title?: string;
  message: string;
  onClose?: () => void;
  autoClose?: boolean;
  duration?: number;
}

export function SuccessMessage({ 
  title = 'Success!',
  message,
  onClose,
  autoClose = false,
  duration = 3000
}: SuccessMessageProps) {
  React.useEffect(() => {
    if (autoClose && onClose) {
      const timer = setTimeout(onClose, duration);
      return () => clearTimeout(timer);
    }
  }, [autoClose, onClose, duration]);

  return (
    <Alert className="border-green-200 bg-green-50 text-green-800">
      <div className="flex items-center space-x-2">
        <div className="bg-green-100 p-1 rounded-full">
          <RefreshCw className="h-4 w-4 text-green-600" />
        </div>
        <div className="flex-1">
          <h4 className="font-medium">{title}</h4>
          <p className="text-sm">{message}</p>
        </div>
        {onClose && (
          <Button variant="ghost" size="sm" onClick={onClose}>
            <AlertTriangle className="h-4 w-4" />
          </Button>
        )}
      </div>
    </Alert>
  );
}
