import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, mean_absolute_error
import joblib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class MaintenancePredictionService:
    def __init__(self, db_manager, cache_manager):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.maintenance_classifier = None
        self.cost_predictor = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
        # Maintenance categories and their typical costs
        self.maintenance_types = {
            'routine_service': {'cost_range': (1500, 3000), 'downtime_hours': 4},
            'brake_service': {'cost_range': (2000, 4000), 'downtime_hours': 3},
            'engine_repair': {'cost_range': (5000, 15000), 'downtime_hours': 24},
            'transmission_service': {'cost_range': (3000, 8000), 'downtime_hours': 8},
            'electrical_repair': {'cost_range': (2500, 6000), 'downtime_hours': 6},
            'tire_replacement': {'cost_range': (1000, 3000), 'downtime_hours': 2},
            'battery_replacement': {'cost_range': (800, 2000), 'downtime_hours': 1},
            'chain_sprocket': {'cost_range': (1200, 2500), 'downtime_hours': 3}
        }
        
        # Load or train models
        self._load_or_train_models()
    
    def _load_or_train_models(self):
        """Load existing models or train new ones"""
        try:
            self.maintenance_classifier = joblib.load("models/maintenance_classifier.joblib")
            self.cost_predictor = joblib.load("models/maintenance_cost_predictor.joblib")
            self.scaler = joblib.load("models/maintenance_scaler.joblib")
            self.label_encoders = joblib.load("models/maintenance_encoders.joblib")
            logger.info("Loaded existing maintenance prediction models")
        except FileNotFoundError:
            logger.info("No existing maintenance models found, training new models...")
            self._train_initial_models()
    
    def _train_initial_models(self):
        """Train initial maintenance prediction models"""
        # Generate synthetic maintenance data
        synthetic_data = self._generate_synthetic_maintenance_data()
        
        # Prepare features
        X, y_maintenance, y_cost = self._prepare_maintenance_features(synthetic_data)
        
        # Train models
        self._train_maintenance_models(X, y_maintenance, y_cost)
        
        logger.info("Initial maintenance prediction models trained successfully")
    
    def _generate_synthetic_maintenance_data(self, samples=3000):
        """Generate synthetic vehicle maintenance data"""
        data = []
        
        vehicle_models = ['Hero Splendor', 'Honda Activa', 'Ather 450X', 'TVS Jupiter', 'Bajaj Pulsar']
        
        for i in range(samples):
            # Vehicle characteristics
            vehicle_id = f"vehicle_{i:03d}"
            model = np.random.choice(vehicle_models)
            year = np.random.randint(2018, 2024)
            age_months = (2024 - year) * 12 + np.random.randint(0, 12)
            
            # Usage patterns
            daily_km = np.random.normal(120, 40)  # Average daily kilometers
            daily_km = max(50, min(250, daily_km))  # Clamp to reasonable range
            
            rides_per_day = np.random.normal(15, 5)
            rides_per_day = max(5, min(30, rides_per_day))
            
            total_mileage = age_months * 30 * (daily_km / 30) + np.random.normal(0, 5000)
            total_mileage = max(1000, total_mileage)
            
            # Maintenance history
            days_since_last_maintenance = np.random.randint(10, 120)
            maintenance_frequency = np.random.normal(45, 15)  # Days between maintenance
            maintenance_frequency = max(20, min(90, maintenance_frequency))
            
            # Performance metrics
            fuel_efficiency = np.random.normal(45, 10)  # km/l
            if model == 'Ather 450X':  # Electric
                fuel_efficiency = np.random.normal(80, 15)  # km/charge equivalent
            
            breakdown_count = np.random.poisson(age_months / 12)  # Breakdowns per year
            
            # Driver behavior score (1-10)
            driver_behavior_score = np.random.normal(7, 2)
            driver_behavior_score = max(1, min(10, driver_behavior_score))
            
            # Environmental factors
            city_type = np.random.choice(['metro', 'tier1', 'tier2'], p=[0.5, 0.3, 0.2])
            traffic_exposure = np.random.normal(0.7, 0.2)  # 0-1 scale
            traffic_exposure = max(0.1, min(1.0, traffic_exposure))
            
            # Calculate maintenance need probability
            maintenance_probability = self._calculate_maintenance_probability(
                age_months, total_mileage, days_since_last_maintenance,
                fuel_efficiency, breakdown_count, driver_behavior_score,
                daily_km, rides_per_day, model
            )
            
            # Determine if maintenance is needed
            needs_maintenance = np.random.random() < maintenance_probability
            
            # If maintenance is needed, determine type and urgency
            if needs_maintenance:
                maintenance_type = self._determine_maintenance_type(
                    age_months, total_mileage, days_since_last_maintenance, model
                )
                urgency = self._determine_urgency(maintenance_probability)
                days_until_needed = max(1, int(np.random.exponential(7)))
            else:
                maintenance_type = 'none'
                urgency = 'low'
                days_until_needed = np.random.randint(30, 90)
            
            # Calculate estimated cost
            if maintenance_type != 'none':
                cost_range = self.maintenance_types[maintenance_type]['cost_range']
                estimated_cost = np.random.uniform(cost_range[0], cost_range[1])
                downtime_hours = self.maintenance_types[maintenance_type]['downtime_hours']
                downtime_hours += np.random.normal(0, 1)  # Add some variance
            else:
                estimated_cost = 0
                downtime_hours = 0
            
            data.append({
                'vehicle_id': vehicle_id,
                'model': model,
                'year': year,
                'age_months': age_months,
                'total_mileage': total_mileage,
                'daily_km': daily_km,
                'rides_per_day': rides_per_day,
                'days_since_last_maintenance': days_since_last_maintenance,
                'maintenance_frequency': maintenance_frequency,
                'fuel_efficiency': fuel_efficiency,
                'breakdown_count': breakdown_count,
                'driver_behavior_score': driver_behavior_score,
                'city_type': city_type,
                'traffic_exposure': traffic_exposure,
                'needs_maintenance': needs_maintenance,
                'maintenance_type': maintenance_type,
                'urgency': urgency,
                'days_until_needed': days_until_needed,
                'estimated_cost': estimated_cost,
                'estimated_downtime': max(0, downtime_hours)
            })
        
        return pd.DataFrame(data)
    
    def _calculate_maintenance_probability(self, age_months, mileage, days_since_last,
                                         fuel_efficiency, breakdowns, driver_score,
                                         daily_km, rides_per_day, model):
        """Calculate probability that vehicle needs maintenance"""
        probability = 0.1  # Base probability
        
        # Age factor
        if age_months > 36:
            probability += 0.3
        elif age_months > 24:
            probability += 0.2
        elif age_months > 12:
            probability += 0.1
        
        # Mileage factor
        if mileage > 50000:
            probability += 0.4
        elif mileage > 30000:
            probability += 0.25
        elif mileage > 15000:
            probability += 0.15
        
        # Time since last maintenance
        if days_since_last > 90:
            probability += 0.5
        elif days_since_last > 60:
            probability += 0.3
        elif days_since_last > 45:
            probability += 0.2
        
        # Performance indicators
        if fuel_efficiency < 35:  # Poor fuel efficiency
            probability += 0.2
        
        if breakdowns > 2:
            probability += 0.3
        elif breakdowns > 0:
            probability += 0.15
        
        # Usage intensity
        if daily_km > 150:
            probability += 0.15
        if rides_per_day > 20:
            probability += 0.1
        
        # Driver behavior
        if driver_score < 5:
            probability += 0.2
        elif driver_score < 7:
            probability += 0.1
        
        # Model-specific factors
        if model == 'Ather 450X':  # Electric vehicles
            probability *= 0.7  # Generally more reliable
        
        return min(0.95, probability)
    
    def _determine_maintenance_type(self, age_months, mileage, days_since_last, model):
        """Determine type of maintenance needed"""
        # Routine service is most common
        if days_since_last > 45:
            return 'routine_service'
        
        # Age-based maintenance
        if age_months > 36:
            types = ['engine_repair', 'transmission_service', 'electrical_repair']
            return np.random.choice(types)
        elif age_months > 24:
            types = ['brake_service', 'chain_sprocket', 'electrical_repair']
            return np.random.choice(types)
        
        # Mileage-based maintenance
        if mileage > 40000:
            types = ['engine_repair', 'transmission_service']
            return np.random.choice(types)
        elif mileage > 20000:
            types = ['brake_service', 'chain_sprocket', 'tire_replacement']
            return np.random.choice(types)
        
        # Default to routine service
        return 'routine_service'
    
    def _determine_urgency(self, maintenance_probability):
        """Determine urgency level based on maintenance probability"""
        if maintenance_probability > 0.8:
            return 'high'
        elif maintenance_probability > 0.6:
            return 'medium'
        else:
            return 'low'
    
    def _prepare_maintenance_features(self, data):
        """Prepare features for maintenance prediction models"""
        feature_data = data.copy()
        
        # Encode categorical variables
        categorical_columns = ['model', 'city_type', 'maintenance_type', 'urgency']
        for col in categorical_columns:
            if col not in self.label_encoders:
                self.label_encoders[col] = LabelEncoder()
                if col in feature_data.columns:
                    feature_data[col + '_encoded'] = self.label_encoders[col].fit_transform(feature_data[col])
        
        # Create derived features
        feature_data['mileage_per_month'] = feature_data['total_mileage'] / feature_data['age_months']
        feature_data['usage_intensity'] = feature_data['daily_km'] * feature_data['rides_per_day']
        feature_data['maintenance_overdue'] = (feature_data['days_since_last_maintenance'] > 60).astype(int)
        
        # Feature columns for prediction
        feature_columns = [
            'age_months', 'total_mileage', 'daily_km', 'rides_per_day',
            'days_since_last_maintenance', 'maintenance_frequency',
            'fuel_efficiency', 'breakdown_count', 'driver_behavior_score',
            'traffic_exposure', 'mileage_per_month', 'usage_intensity',
            'maintenance_overdue', 'model_encoded', 'city_type_encoded'
        ]
        
        X = feature_data[feature_columns]
        y_maintenance = feature_data['needs_maintenance'].astype(int)
        y_cost = feature_data['estimated_cost']
        
        return X, y_maintenance, y_cost
    
    def _train_maintenance_models(self, X, y_maintenance, y_cost):
        """Train maintenance prediction and cost estimation models"""
        # Split data
        X_train, X_test, y_maint_train, y_maint_test, y_cost_train, y_cost_test = train_test_split(
            X, y_maintenance, y_cost, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train maintenance need classifier
        self.maintenance_classifier = RandomForestClassifier(
            n_estimators=100, random_state=42, n_jobs=-1
        )
        self.maintenance_classifier.fit(X_train_scaled, y_maint_train)
        
        # Train cost predictor (only on samples that need maintenance)
        maintenance_mask = y_cost_train > 0
        if maintenance_mask.sum() > 10:  # Ensure we have enough samples
            self.cost_predictor = GradientBoostingRegressor(
                n_estimators=100, random_state=42
            )
            self.cost_predictor.fit(
                X_train_scaled[maintenance_mask], 
                y_cost_train[maintenance_mask]
            )
        
        # Evaluate models
        maint_score = self.maintenance_classifier.score(X_test_scaled, y_maint_test)
        logger.info(f"Maintenance classifier accuracy: {maint_score:.3f}")
        
        if self.cost_predictor:
            cost_mask = y_cost_test > 0
            if cost_mask.sum() > 0:
                cost_pred = self.cost_predictor.predict(X_test_scaled[cost_mask])
                cost_mae = mean_absolute_error(y_cost_test[cost_mask], cost_pred)
                logger.info(f"Cost predictor MAE: ₹{cost_mae:.0f}")
        
        # Save models
        self._save_maintenance_models()
    
    def _save_maintenance_models(self):
        """Save trained maintenance models"""
        joblib.dump(self.maintenance_classifier, "models/maintenance_classifier.joblib")
        if self.cost_predictor:
            joblib.dump(self.cost_predictor, "models/maintenance_cost_predictor.joblib")
        joblib.dump(self.scaler, "models/maintenance_scaler.joblib")
        joblib.dump(self.label_encoders, "models/maintenance_encoders.joblib")
        logger.info("Maintenance models saved successfully")
    
    async def predict_maintenance_needs(self, vehicle_ids: Optional[List[str]] = None):
        """Predict maintenance needs for vehicles"""
        try:
            # Get vehicle data
            vehicle_data = await self.db_manager.get_vehicle_maintenance_data(vehicle_ids)
            
            if not vehicle_data:
                return []
            
            predictions = []
            
            for vehicle in vehicle_data:
                # Prepare features for this vehicle
                features = self._prepare_vehicle_features(vehicle)
                
                # Make predictions
                maintenance_prediction = self._predict_vehicle_maintenance(features)
                
                predictions.append({
                    'vehicle_id': vehicle['vehicleId'],
                    'model': vehicle.get('model', 'Unknown'),
                    'maintenance_needed': maintenance_prediction['needs_maintenance'],
                    'maintenance_type': maintenance_prediction['maintenance_type'],
                    'urgency': maintenance_prediction['urgency'],
                    'estimated_days_until_needed': maintenance_prediction['days_until_needed'],
                    'estimated_cost': maintenance_prediction['estimated_cost'],
                    'estimated_downtime': maintenance_prediction['estimated_downtime'],
                    'confidence': maintenance_prediction['confidence'],
                    'factors': maintenance_prediction['factors'],
                    'recommendation': maintenance_prediction['recommendation']
                })
            
            # Cache results
            cache_key = f"maintenance_predictions:{datetime.now().strftime('%Y%m%d')}"
            await self.cache_manager.set(cache_key, predictions, expire=3600)
            
            return predictions
            
        except Exception as e:
            logger.error(f"Maintenance prediction error: {e}")
            return []
    
    def _prepare_vehicle_features(self, vehicle_data):
        """Prepare features for a single vehicle"""
        current_date = datetime.now()
        
        # Calculate age in months
        vehicle_year = vehicle_data.get('year', 2020)
        age_months = (current_date.year - vehicle_year) * 12 + current_date.month
        
        # Get last maintenance date
        last_maintenance = vehicle_data.get('lastMaintenanceDate')
        if isinstance(last_maintenance, str):
            last_maintenance = datetime.fromisoformat(last_maintenance.replace('Z', '+00:00'))
        elif not last_maintenance:
            last_maintenance = current_date - timedelta(days=60)  # Default
        
        days_since_last = (current_date - last_maintenance).days
        
        # Usage statistics
        usage_stats = vehicle_data.get('usageStats', {})
        daily_km = usage_stats.get('dailyKm', 100)
        rides_per_day = usage_stats.get('ridesPerDay', 12)
        
        # Performance metrics
        performance = vehicle_data.get('performanceMetrics', {})
        fuel_efficiency = performance.get('fuelEfficiency', 45)
        breakdown_count = performance.get('breakdownCount', 0)
        
        features = {
            'age_months': age_months,
            'total_mileage': vehicle_data.get('mileage', 20000),
            'daily_km': daily_km,
            'rides_per_day': rides_per_day,
            'days_since_last_maintenance': days_since_last,
            'maintenance_frequency': 45,  # Default frequency
            'fuel_efficiency': fuel_efficiency,
            'breakdown_count': breakdown_count,
            'driver_behavior_score': 7.0,  # Default score
            'traffic_exposure': 0.7,  # Default exposure
            'model': vehicle_data.get('model', 'Unknown'),
            'city_type': 'metro'  # Default
        }
        
        return features
    
    def _predict_vehicle_maintenance(self, features):
        """Predict maintenance needs for a single vehicle"""
        # Encode categorical features
        encoded_features = features.copy()
        
        for col in ['model', 'city_type']:
            if col in self.label_encoders:
                try:
                    encoded_features[col + '_encoded'] = self.label_encoders[col].transform([features[col]])[0]
                except ValueError:
                    encoded_features[col + '_encoded'] = 0
            else:
                encoded_features[col + '_encoded'] = 0
        
        # Create derived features
        encoded_features['mileage_per_month'] = encoded_features['total_mileage'] / max(1, encoded_features['age_months'])
        encoded_features['usage_intensity'] = encoded_features['daily_km'] * encoded_features['rides_per_day']
        encoded_features['maintenance_overdue'] = 1 if encoded_features['days_since_last_maintenance'] > 60 else 0
        
        # Prepare feature vector
        feature_columns = [
            'age_months', 'total_mileage', 'daily_km', 'rides_per_day',
            'days_since_last_maintenance', 'maintenance_frequency',
            'fuel_efficiency', 'breakdown_count', 'driver_behavior_score',
            'traffic_exposure', 'mileage_per_month', 'usage_intensity',
            'maintenance_overdue', 'model_encoded', 'city_type_encoded'
        ]
        
        feature_vector = np.array([[encoded_features[col] for col in feature_columns]])
        feature_vector_scaled = self.scaler.transform(feature_vector)
        
        # Predict maintenance need
        maintenance_prob = self.maintenance_classifier.predict_proba(feature_vector_scaled)[0][1]
        needs_maintenance = maintenance_prob > 0.5
        
        # Determine maintenance type and urgency
        if needs_maintenance:
            maintenance_type = self._determine_maintenance_type(
                features['age_months'], features['total_mileage'],
                features['days_since_last_maintenance'], features['model']
            )
            urgency = self._determine_urgency(maintenance_prob)
            days_until_needed = max(1, int(7 * (1 - maintenance_prob)))
        else:
            maintenance_type = 'none'
            urgency = 'low'
            days_until_needed = int(30 + np.random.randint(0, 30))
        
        # Predict cost
        if needs_maintenance and self.cost_predictor:
            estimated_cost = self.cost_predictor.predict(feature_vector_scaled)[0]
            estimated_cost = max(500, estimated_cost)  # Minimum cost
        elif needs_maintenance:
            # Fallback cost estimation
            cost_range = self.maintenance_types.get(maintenance_type, {'cost_range': (1500, 3000)})['cost_range']
            estimated_cost = np.mean(cost_range)
        else:
            estimated_cost = 0
        
        # Estimate downtime
        if needs_maintenance:
            base_downtime = self.maintenance_types.get(maintenance_type, {'downtime_hours': 4})['downtime_hours']
            estimated_downtime = base_downtime + np.random.normal(0, 1)
            estimated_downtime = max(1, estimated_downtime)
        else:
            estimated_downtime = 0
        
        # Calculate confidence
        confidence = min(0.95, max(0.6, maintenance_prob if needs_maintenance else 1 - maintenance_prob))
        
        # Analyze factors
        factors = self._analyze_maintenance_factors(features, maintenance_prob)
        
        # Generate recommendation
        recommendation = self._generate_maintenance_recommendation(
            needs_maintenance, maintenance_type, urgency, days_until_needed
        )
        
        return {
            'needs_maintenance': needs_maintenance,
            'maintenance_type': maintenance_type,
            'urgency': urgency,
            'days_until_needed': days_until_needed,
            'estimated_cost': round(estimated_cost),
            'estimated_downtime': round(estimated_downtime, 1),
            'confidence': round(confidence, 2),
            'factors': factors,
            'recommendation': recommendation
        }
    
    def _analyze_maintenance_factors(self, features, maintenance_prob):
        """Analyze factors contributing to maintenance prediction"""
        factors = {}
        
        # Age factor
        if features['age_months'] > 36:
            factors['vehicle_age'] = 0.3
        elif features['age_months'] > 24:
            factors['vehicle_age'] = 0.2
        
        # Mileage factor
        if features['total_mileage'] > 40000:
            factors['high_mileage'] = 0.35
        elif features['total_mileage'] > 20000:
            factors['moderate_mileage'] = 0.2
        
        # Maintenance overdue
        if features['days_since_last_maintenance'] > 60:
            factors['overdue_maintenance'] = 0.4
        elif features['days_since_last_maintenance'] > 45:
            factors['due_maintenance'] = 0.25
        
        # Performance issues
        if features['fuel_efficiency'] < 35:
            factors['poor_fuel_efficiency'] = 0.25
        
        if features['breakdown_count'] > 1:
            factors['frequent_breakdowns'] = 0.3
        elif features['breakdown_count'] > 0:
            factors['previous_breakdown'] = 0.15
        
        # Usage intensity
        if features['daily_km'] > 150:
            factors['high_usage'] = 0.2
        
        return factors
    
    def _generate_maintenance_recommendation(self, needs_maintenance, maintenance_type, urgency, days_until):
        """Generate maintenance recommendation"""
        if not needs_maintenance:
            return "Vehicle is in good condition. Continue regular monitoring."
        
        if urgency == 'high':
            return f"Immediate {maintenance_type.replace('_', ' ')} required. Schedule within {days_until} days to avoid breakdown."
        elif urgency == 'medium':
            return f"Schedule {maintenance_type.replace('_', ' ')} within {days_until} days to maintain optimal performance."
        else:
            return f"Plan {maintenance_type.replace('_', ' ')} in the next {days_until} days during low-demand periods."
    
    async def analyze_vehicle_health(self, vehicle_id: str):
        """Analyze overall health of a specific vehicle"""
        try:
            # Get vehicle data
            vehicle_data = await self.db_manager.get_vehicle_maintenance_data([vehicle_id])
            
            if not vehicle_data:
                return {'error': 'Vehicle not found'}
            
            vehicle = vehicle_data[0]
            features = self._prepare_vehicle_features(vehicle)
            
            # Get maintenance prediction
            maintenance_pred = self._predict_vehicle_maintenance(features)
            
            # Calculate health score
            health_score = self._calculate_health_score(features, maintenance_pred)
            
            # Generate health insights
            insights = self._generate_health_insights(features, maintenance_pred, health_score)
            
            return {
                'vehicle_id': vehicle_id,
                'health_score': health_score,
                'health_status': self._get_health_status(health_score),
                'maintenance_prediction': maintenance_pred,
                'insights': insights,
                'recommendations': self._get_health_recommendations(health_score, maintenance_pred),
                'analysis_timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Vehicle health analysis error: {e}")
            return {'error': str(e)}
    
    def _calculate_health_score(self, features, maintenance_pred):
        """Calculate overall vehicle health score (0-100)"""
        score = 100
        
        # Age penalty
        if features['age_months'] > 36:
            score -= 20
        elif features['age_months'] > 24:
            score -= 10
        
        # Mileage penalty
        mileage_per_month = features['total_mileage'] / max(1, features['age_months'])
        if mileage_per_month > 2000:
            score -= 15
        elif mileage_per_month > 1500:
            score -= 10
        
        # Maintenance penalty
        if features['days_since_last_maintenance'] > 90:
            score -= 25
        elif features['days_since_last_maintenance'] > 60:
            score -= 15
        
        # Performance penalty
        if features['fuel_efficiency'] < 35:
            score -= 20
        elif features['fuel_efficiency'] < 40:
            score -= 10
        
        # Breakdown penalty
        score -= features['breakdown_count'] * 15
        
        # Maintenance prediction penalty
        if maintenance_pred['needs_maintenance']:
            if maintenance_pred['urgency'] == 'high':
                score -= 30
            elif maintenance_pred['urgency'] == 'medium':
                score -= 20
            else:
                score -= 10
        
        return max(0, min(100, score))
    
    def _get_health_status(self, health_score):
        """Get health status based on score"""
        if health_score >= 80:
            return 'excellent'
        elif health_score >= 60:
            return 'good'
        elif health_score >= 40:
            return 'fair'
        elif health_score >= 20:
            return 'poor'
        else:
            return 'critical'
    
    def _generate_health_insights(self, features, maintenance_pred, health_score):
        """Generate health insights"""
        insights = []
        
        if health_score >= 80:
            insights.append("Vehicle is in excellent condition with minimal maintenance needs.")
        elif health_score >= 60:
            insights.append("Vehicle is performing well with routine maintenance requirements.")
        else:
            insights.append("Vehicle requires attention to maintain optimal performance.")
        
        if features['days_since_last_maintenance'] > 60:
            insights.append("Maintenance is overdue. Schedule service soon.")
        
        if features['fuel_efficiency'] < 40:
            insights.append("Fuel efficiency is below optimal. Consider engine service.")
        
        if features['breakdown_count'] > 1:
            insights.append("Multiple breakdowns indicate potential reliability issues.")
        
        return insights
    
    def _get_health_recommendations(self, health_score, maintenance_pred):
        """Get health-based recommendations"""
        recommendations = []
        
        if maintenance_pred['needs_maintenance']:
            recommendations.append(maintenance_pred['recommendation'])
        
        if health_score < 60:
            recommendations.append("Consider comprehensive vehicle inspection.")
        
        if health_score < 40:
            recommendations.append("Evaluate cost-effectiveness of continued operation vs replacement.")
        
        recommendations.append("Maintain regular service schedule to prevent issues.")
        
        return recommendations
    
    async def retrain_model(self):
        """Retrain maintenance prediction models with latest data"""
        try:
            # Get latest vehicle and maintenance data
            vehicle_data = await self.db_manager.get_vehicle_maintenance_data()
            
            if len(vehicle_data) > 50:
                logger.info("Retraining maintenance prediction models...")
                # Implementation would process real data and retrain models
            else:
                logger.warning("Insufficient data for maintenance model retraining")
                
        except Exception as e:
            logger.error(f"Maintenance model retraining error: {e}")
    
    async def get_model_status(self):
        """Get current maintenance model status"""
        return {
            "model_type": "RandomForest Classifier + GradientBoosting Regressor",
            "last_trained": datetime.now().isoformat(),
            "maintenance_types": len(self.maintenance_types),
            "status": "active"
        }
