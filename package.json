{"name": "two-wheeler-sharing-ai-automation", "version": "1.0.0", "description": "AI-powered two-wheeler sharing platform with workflow automation", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup": "chmod +x setup.sh && ./setup.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2", "@types/node": "^20.8.0", "@types/react": "^18.2.25", "@types/react-dom": "^18.2.10", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "lucide-react": "^0.288.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "mongoose": "^7.5.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "winston": "^3.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"eslint": "^8.51.0", "eslint-config-next": "14.0.0", "@tailwindcss/typography": "^0.5.10", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@types/bcryptjs": "^2.4.4", "@types/jsonwebtoken": "^9.0.3", "@types/jest": "^29.5.5"}, "keywords": ["two-wheeler", "sharing", "ai", "automation", "workflow", "n8n", "ollama", "mcp", "rewards", "nextjs"], "author": "Two Wheeler Sharing Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/two-wheeler-sharing-ai-automation.git"}, "bugs": {"url": "https://github.com/your-username/two-wheeler-sharing-ai-automation/issues"}, "homepage": "https://github.com/your-username/two-wheeler-sharing-ai-automation#readme"}