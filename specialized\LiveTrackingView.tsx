'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  MapPin, 
  Navigation, 
  Clock, 
  Phone, 
  MessageCircle, 
  Share2,
  AlertTriangle,
  CheckCircle,
  Car,
  User,
  Route,
  Zap,
  Shield
} from 'lucide-react';

interface LiveTrackingViewProps {
  tripId: string;
  userType: 'rider' | 'driver';
}

export default function LiveTrackingView({ tripId, userType }: LiveTrackingViewProps) {
  const [tripStatus, setTripStatus] = useState('en_route');
  const [eta, setEta] = useState(8);
  const [progress, setProgress] = useState(65);
  const [driverLocation, setDriverLocation] = useState({ lat: 12.9716, lng: 77.5946 });

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setEta(prev => Math.max(0, prev - 0.1));
      setProgress(prev => Math.min(100, prev + 1));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const tripData = {
    id: tripId,
    driver: {
      name: 'Rajesh Kumar',
      rating: 4.8,
      vehicle: 'KA 01 AB 1234',
      phone: '+91 98765 43210',
      photo: '/api/placeholder/40/40'
    },
    pickup: {
      address: '123 MG Road, Bangalore',
      time: '10:30 AM'
    },
    destination: {
      address: '456 Brigade Road, Bangalore',
      time: '10:45 AM (Est.)'
    },
    fare: 85,
    distance: 5.2,
    paymentMethod: 'UPI'
  };

  return (
    <div className="max-w-md mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-lg font-semibold">Live Tracking</h1>
            <p className="text-blue-100 text-sm">Trip #{tripId}</p>
          </div>
          <Badge className="bg-green-500 text-white">
            {tripStatus === 'en_route' ? 'On the way' : 'Arrived'}
          </Badge>
        </div>
      </div>

      {/* Map Area */}
      <div className="relative h-64 bg-gray-100">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-gray-500 text-center">
            <MapPin className="h-8 w-8 mx-auto mb-2" />
            <p>Live Map View</p>
          </div>
        </div>
        
        {/* Floating ETA Card */}
        <div className="absolute top-4 left-4 right-4">
          <Card className="shadow-lg">
            <CardContent className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium">ETA: {Math.ceil(eta)} min</span>
                </div>
                <div className="text-sm text-gray-600">
                  {progress.toFixed(0)}% complete
                </div>
              </div>
              <Progress value={progress} className="mt-2 h-2" />
            </CardContent>
          </Card>
        </div>

        {/* Driver Location Indicator */}
        <div className="absolute bottom-4 right-4">
          <div className="bg-blue-600 text-white p-2 rounded-full shadow-lg">
            <Car className="h-5 w-5" />
          </div>
        </div>
      </div>

      {/* Driver Info */}
      <Card className="m-4 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-gray-600" />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h3 className="font-medium">{tripData.driver.name}</h3>
                <div className="flex items-center">
                  <span className="text-yellow-500">★</span>
                  <span className="text-sm text-gray-600 ml-1">{tripData.driver.rating}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">{tripData.driver.vehicle}</p>
            </div>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" className="p-2">
                <Phone className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="outline" className="p-2">
                <MessageCircle className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trip Details */}
      <Card className="m-4 shadow-sm">
        <CardContent className="p-4 space-y-4">
          {/* Pickup */}
          <div className="flex items-start space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full mt-2"></div>
            <div className="flex-1">
              <p className="font-medium text-sm">Pickup</p>
              <p className="text-gray-600 text-sm">{tripData.pickup.address}</p>
              <p className="text-xs text-gray-500">{tripData.pickup.time}</p>
            </div>
            <CheckCircle className="h-5 w-5 text-green-500" />
          </div>

          {/* Route Line */}
          <div className="ml-1.5 border-l-2 border-dashed border-gray-300 h-6"></div>

          {/* Destination */}
          <div className="flex items-start space-x-3">
            <div className="w-3 h-3 bg-red-500 rounded-full mt-2"></div>
            <div className="flex-1">
              <p className="font-medium text-sm">Destination</p>
              <p className="text-gray-600 text-sm">{tripData.destination.address}</p>
              <p className="text-xs text-gray-500">{tripData.destination.time}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trip Stats */}
      <div className="grid grid-cols-3 gap-4 m-4">
        <Card className="text-center">
          <CardContent className="p-3">
            <Route className="h-5 w-5 text-blue-600 mx-auto mb-1" />
            <p className="text-xs text-gray-600">Distance</p>
            <p className="font-medium text-sm">{tripData.distance} km</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-3">
            <Clock className="h-5 w-5 text-green-600 mx-auto mb-1" />
            <p className="text-xs text-gray-600">Duration</p>
            <p className="font-medium text-sm">15 min</p>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-3">
            <span className="text-lg">₹</span>
            <p className="text-xs text-gray-600">Fare</p>
            <p className="font-medium text-sm">₹{tripData.fare}</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="m-4 space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <Button variant="outline" className="flex items-center justify-center space-x-2">
            <Share2 className="h-4 w-4" />
            <span>Share Trip</span>
          </Button>
          <Button variant="outline" className="flex items-center justify-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Safety</span>
          </Button>
        </div>
        
        <Button 
          variant="destructive" 
          className="w-full flex items-center justify-center space-x-2"
        >
          <AlertTriangle className="h-4 w-4" />
          <span>Emergency</span>
        </Button>
      </div>

      {/* Real-time Updates */}
      <Card className="m-4 shadow-sm">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center">
            <Zap className="h-4 w-4 mr-2 text-yellow-500" />
            Live Updates
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Driver is 2 minutes away</span>
              <span className="text-xs text-gray-400">now</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">Trip started from pickup location</span>
              <span className="text-xs text-gray-400">2 min ago</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span className="text-gray-600">Driver arrived at pickup</span>
              <span className="text-xs text-gray-400">5 min ago</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Info */}
      <Card className="m-4 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium">Payment Method</p>
              <p className="text-sm text-gray-600">{tripData.paymentMethod}</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">Total Fare</p>
              <p className="text-lg font-bold text-green-600">₹{tripData.fare}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bottom Padding */}
      <div className="h-6"></div>
    </div>
  );
}
