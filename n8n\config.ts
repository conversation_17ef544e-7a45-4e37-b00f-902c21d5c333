// n8n Workflow Automation Configuration
export const N8N_CONFIG = {
  // n8n instance URL (can be local or cloud)
  baseUrl: process.env.N8N_BASE_URL || 'http://localhost:5678',
  
  // API credentials for n8n
  apiKey: process.env.N8N_API_KEY || '',
  
  // Webhook endpoints for triggering workflows
  webhooks: {
    rideRequest: '/webhook/ride-request',
    driverAssignment: '/webhook/driver-assignment',
    statusUpdate: '/webhook/status-update',
    paymentProcessing: '/webhook/payment-processing',
    notification: '/webhook/notification',
    emergency: '/webhook/emergency',
  },
  
  // Workflow IDs (these would be created in n8n)
  workflows: {
    rideBookingFlow: 'ride-booking-workflow',
    driverNotificationFlow: 'driver-notification-workflow',
    paymentProcessingFlow: 'payment-processing-workflow',
    emergencyAlertFlow: 'emergency-alert-workflow',
    dailyReportsFlow: 'daily-reports-workflow',
  },
  
  // Automation settings
  settings: {
    retryAttempts: 3,
    retryDelay: 5000, // 5 seconds
    timeout: 30000, // 30 seconds
    enableLogging: true,
  }
};

// Workflow trigger types
export enum WorkflowTrigger {
  RIDE_REQUESTED = 'ride_requested',
  DRIVER_FOUND = 'driver_found',
  RIDE_ACCEPTED = 'ride_accepted',
  RIDE_STARTED = 'ride_started',
  RIDE_COMPLETED = 'ride_completed',
  RIDE_CANCELLED = 'ride_cancelled',
  PAYMENT_PROCESSED = 'payment_processed',
  EMERGENCY_ALERT = 'emergency_alert',
  DRIVER_OFFLINE = 'driver_offline',
  DAILY_REPORT = 'daily_report',
}

// Workflow data interfaces
export interface WorkflowData {
  trigger: WorkflowTrigger;
  timestamp: number;
  data: any;
  metadata?: {
    userId?: string;
    rideId?: string;
    driverId?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
  };
}

export interface NotificationWorkflowData extends WorkflowData {
  recipient: {
    userId: string;
    role: 'rider' | 'driver';
    phone?: string;
    email?: string;
    pushToken?: string;
  };
  message: {
    title: string;
    body: string;
    type: 'info' | 'success' | 'warning' | 'error';
    actionUrl?: string;
  };
  channels: ('push' | 'sms' | 'email' | 'websocket')[];
}

export interface RideWorkflowData extends WorkflowData {
  ride: {
    id: string;
    riderId: string;
    driverId?: string;
    status: string;
    pickupLocation: {
      address: string;
      coordinates: { latitude: number; longitude: number };
    };
    dropoffLocation: {
      address: string;
      coordinates: { latitude: number; longitude: number };
    };
    estimatedDistance: number;
    estimatedDuration: number;
    finalAmount: number;
  };
}

export interface PaymentWorkflowData extends WorkflowData {
  payment: {
    rideId: string;
    amount: number;
    currency: string;
    method: string;
    status: string;
    transactionId?: string;
  };
  rider: {
    id: string;
    email: string;
  };
  driver?: {
    id: string;
    earnings: number;
  };
}
