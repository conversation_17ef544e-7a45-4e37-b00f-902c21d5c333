'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Car, 
  DollarSign, 
  Clock,
  MapPin,
  Leaf,
  Download,
  Filter,
  Calendar,
  Building2,
  Target,
  Award,
  AlertCircle
} from 'lucide-react';

interface CorporateAnalyticsProps {
  companyId: string;
  timeRange: '7d' | '30d' | '90d' | '1y';
}

export default function CorporateAnalytics({ companyId, timeRange }: CorporateAnalyticsProps) {
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedMetric, setSelectedMetric] = useState('cost');

  const corporateData = {
    company: {
      name: 'TechCorp Solutions',
      employees: 450,
      activeUsers: 285,
      departments: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance']
    },
    overview: {
      totalSpend: 245000,
      totalTrips: 1250,
      avgCostPerTrip: 196,
      carbonSaved: 850,
      trends: {
        spend: 12.5,
        trips: 8.3,
        efficiency: 15.2,
        carbon: 22.1
      }
    },
    departmentBreakdown: [
      { name: 'Engineering', spend: 98000, trips: 520, employees: 180, efficiency: 188 },
      { name: 'Sales', spend: 75000, trips: 380, employees: 95, efficiency: 197 },
      { name: 'Marketing', spend: 42000, trips: 210, employees: 85, efficiency: 200 },
      { name: 'HR', spend: 18000, trips: 90, employees: 45, efficiency: 200 },
      { name: 'Finance', spend: 12000, trips: 50, employees: 45, efficiency: 240 }
    ],
    topRoutes: [
      { route: 'Office → Airport', trips: 145, avgCost: 450, carbonSaved: 65 },
      { route: 'Office → Client Site A', trips: 98, avgCost: 180, carbonSaved: 28 },
      { route: 'Office → Hotel District', trips: 87, avgCost: 220, carbonSaved: 31 },
      { route: 'Airport → Office', trips: 76, avgCost: 480, carbonSaved: 58 },
      { route: 'Office → Conference Center', trips: 65, avgCost: 160, carbonSaved: 22 }
    ],
    monthlyTrend: [
      { month: 'Jan', spend: 185000, trips: 980, carbon: 680 },
      { month: 'Feb', spend: 195000, trips: 1050, carbon: 720 },
      { month: 'Mar', spend: 210000, trips: 1120, carbon: 780 },
      { month: 'Apr', spend: 225000, trips: 1180, carbon: 820 },
      { month: 'May', spend: 245000, trips: 1250, carbon: 850 }
    ],
    costOptimization: {
      potentialSavings: 35000,
      recommendations: [
        'Switch 25% of car rides to bikes for short distances',
        'Implement ride-sharing for airport trips',
        'Use public transport for routine office commutes',
        'Schedule bulk bookings for recurring meetings'
      ]
    },
    compliance: {
      policyAdherence: 94,
      budgetUtilization: 78,
      approvalWorkflow: 96,
      expenseReporting: 99
    }
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Building2 className="h-8 w-8 mr-3 text-blue-600" />
            Corporate Analytics
          </h1>
          <p className="text-gray-600 mt-1">{corporateData.company.name} • {corporateData.company.employees} employees</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {corporateData.company.departments.map(dept => (
                <SelectItem key={dept} value={dept.toLowerCase()}>{dept}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Spend</p>
                <p className="text-3xl font-bold text-gray-900">₹{corporateData.overview.totalSpend.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600 ml-1">+{corporateData.overview.trends.spend}%</span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Trips</p>
                <p className="text-3xl font-bold text-gray-900">{corporateData.overview.totalTrips.toLocaleString()}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600 ml-1">+{corporateData.overview.trends.trips}%</span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Car className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-3xl font-bold text-gray-900">{corporateData.company.activeUsers}</p>
                <div className="flex items-center mt-2">
                  <span className="text-sm text-gray-600">
                    {Math.round((corporateData.company.activeUsers / corporateData.company.employees) * 100)}% adoption
                  </span>
                </div>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Carbon Saved</p>
                <p className="text-3xl font-bold text-gray-900">{corporateData.overview.carbonSaved} kg</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-green-600 ml-1">+{corporateData.overview.trends.carbon}%</span>
                  <span className="text-sm text-gray-500 ml-1">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Leaf className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="departments">Departments</TabsTrigger>
          <TabsTrigger value="routes">Routes</TabsTrigger>
          <TabsTrigger value="optimization">Optimization</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={corporateData.monthlyTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [
                      name === 'spend' ? `₹${value.toLocaleString()}` : value,
                      name === 'spend' ? 'Spend' : name === 'trips' ? 'Trips' : 'Carbon Saved (kg)'
                    ]} />
                    <Legend />
                    <Line type="monotone" dataKey="spend" stroke="#3B82F6" name="spend" />
                    <Line type="monotone" dataKey="trips" stroke="#10B981" name="trips" />
                    <Line type="monotone" dataKey="carbon" stroke="#F59E0B" name="carbon" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cost Efficiency</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">₹{corporateData.overview.avgCostPerTrip}</div>
                  <p className="text-sm text-gray-600">Average cost per trip</p>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">vs Industry Average</span>
                    <Badge className="bg-green-100 text-green-800">15% lower</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">vs Last Quarter</span>
                    <Badge className="bg-blue-100 text-blue-800">8% improved</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Efficiency Score</span>
                    <Badge className="bg-purple-100 text-purple-800">A+</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Departments Tab */}
        <TabsContent value="departments" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Department Spending</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={corporateData.departmentBreakdown}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="spend"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {corporateData.departmentBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `₹${value.toLocaleString()}`} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Department Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {corporateData.departmentBreakdown.map((dept, index) => (
                    <div key={dept.name} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{dept.name}</h4>
                        <Badge variant="outline">{dept.employees} employees</Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Total Spend</p>
                          <p className="font-medium">₹{dept.spend.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Trips</p>
                          <p className="font-medium">{dept.trips}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Avg/Trip</p>
                          <p className="font-medium">₹{dept.efficiency}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Routes Tab */}
        <TabsContent value="routes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Routes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {corporateData.topRoutes.map((route, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                      </div>
                      <div>
                        <p className="font-medium">{route.route}</p>
                        <p className="text-sm text-gray-600">{route.trips} trips</p>
                      </div>
                    </div>
                    <div className="text-right space-y-1">
                      <p className="font-medium">₹{route.avgCost} avg</p>
                      <div className="flex items-center text-sm text-green-600">
                        <Leaf className="h-3 w-3 mr-1" />
                        {route.carbonSaved}kg saved
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Optimization Tab */}
        <TabsContent value="optimization" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-green-600" />
                  Cost Optimization
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">₹{corporateData.costOptimization.potentialSavings.toLocaleString()}</div>
                  <p className="text-sm text-gray-600">Potential monthly savings</p>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-medium">Recommendations:</h4>
                  {corporateData.costOptimization.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <p className="text-sm text-gray-700">{rec}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-yellow-600" />
                  Sustainability Impact
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">{corporateData.overview.carbonSaved}kg</div>
                    <p className="text-xs text-gray-600">CO₂ Saved</p>
                  </div>
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">12</div>
                    <p className="text-xs text-gray-600">Trees Equivalent</p>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Sustainability Score</span>
                    <span className="font-medium">85/100</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Compliance Tab */}
        <TabsContent value="compliance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Object.entries(corporateData.compliance).map(([key, value]) => (
              <Card key={key}>
                <CardContent className="p-6 text-center">
                  <div className="text-2xl font-bold text-blue-600">{value}%</div>
                  <p className="text-sm text-gray-600 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </p>
                  <div className="mt-2">
                    {value >= 95 ? (
                      <Badge className="bg-green-100 text-green-800">Excellent</Badge>
                    ) : value >= 85 ? (
                      <Badge className="bg-yellow-100 text-yellow-800">Good</Badge>
                    ) : (
                      <Badge className="bg-red-100 text-red-800">Needs Attention</Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
