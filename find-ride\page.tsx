import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { RideSearch } from "@/components/ride-search"
import { MapPin, Clock, Calendar, BikeIcon as MotorbikeIcon, Star, ArrowRight } from "lucide-react"

export default function FindRidePage() {
  // Sample ride data
  const rides = [
    {
      id: 1,
      driver: {
        name: "<PERSON><PERSON>",
        avatar: "/placeholder-user.jpg",
        rating: 4.8,
        rides: 124,
      },
      vehicle: {
        type: "Motorcycle",
        model: "Hero Splendor Plus",
        year: 2021,
      },
      route: {
        start: "Andheri East",
        end: "Bandra Kurla Complex",
        distance: 12,
        departureTime: "08:00 AM",
        arrivalTime: "08:30 AM",
        date: "Today",
      },
      price: 60,
    },
    {
      id: 2,
      driver: {
        name: "<PERSON><PERSON>",
        avatar: "/placeholder-user.jpg",
        rating: 4.9,
        rides: 86,
      },
      vehicle: {
        type: "Electric Scooter",
        model: "Ather 450X",
        year: 2022,
      },
      route: {
        start: "Powai",
        end: "Hiranandani Gardens",
        distance: 8,
        departureTime: "09:15 AM",
        arrivalTime: "09:35 AM",
        date: "Today",
      },
      price: 40,
    },
    {
      id: 3,
      driver: {
        name: "Amit K.",
        avatar: "/placeholder-user.jpg",
        rating: 4.7,
        rides: 56,
      },
      vehicle: {
        type: "Scooter",
        model: "Honda Activa 6G",
        year: 2020,
      },
      route: {
        start: "Malad West",
        end: "Goregaon East",
        distance: 10,
        departureTime: "08:30 AM",
        arrivalTime: "09:00 AM",
        date: "Tomorrow",
      },
      price: 50,
    },
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Find a Ride</h1>

      <RideSearch />

      <div className="mt-12">
        <h2 className="text-2xl font-semibold mb-4">Available Rides</h2>

        <div className="grid gap-6">
          {rides.map((ride) => (
            <Card key={ride.id} className="overflow-hidden">
              <div className="flex flex-col md:flex-row">
                <CardHeader className="md:w-1/4 flex-shrink-0 border-r border-border">
                  <div className="flex flex-col items-center text-center">
                    <Avatar className="h-16 w-16 mb-2">
                      <AvatarImage src={ride.driver.avatar} alt={ride.driver.name} />
                      <AvatarFallback>
                        {ride.driver.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <h3 className="font-semibold">{ride.driver.name}</h3>
                    <div className="flex items-center mt-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-1" />
                      <span>{ride.driver.rating}</span>
                      <span className="text-muted-foreground ml-1">({ride.driver.rides} rides)</span>
                    </div>
                    <div className="mt-4">
                      <Badge variant="outline" className="flex items-center gap-1">
                        <MotorbikeIcon className="h-3 w-3" />
                        {ride.vehicle.type}
                      </Badge>
                      <p className="text-sm mt-1">
                        {ride.vehicle.model}, {ride.vehicle.year}
                      </p>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="md:w-2/4 flex-grow p-6">
                  <div className="flex items-center mb-4">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>{ride.route.date}</span>
                  </div>

                  <div className="relative pl-6 border-l border-dashed border-border">
                    <div className="absolute left-0 top-0 w-3 h-3 -ml-1.5 rounded-full bg-green-500"></div>
                    <div className="mb-6">
                      <p className="font-medium">{ride.route.departureTime}</p>
                      <p className="text-lg font-semibold">{ride.route.start}</p>
                    </div>

                    <div className="absolute left-0 bottom-0 w-3 h-3 -ml-1.5 rounded-full bg-primary"></div>
                    <div>
                      <p className="font-medium">{ride.route.arrivalTime}</p>
                      <p className="text-lg font-semibold">{ride.route.end}</p>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{ride.route.distance} km</span>
                    <Clock className="h-4 w-4 ml-4 mr-1" />
                    <span>~30 min</span>
                  </div>
                </CardContent>

                <CardFooter className="md:w-1/4 flex-shrink-0 border-l border-border p-6 flex flex-col items-center justify-center">
                  <div className="text-center mb-4">
                    <p className="text-sm text-muted-foreground">Price</p>
                    <p className="text-3xl font-bold">₹{ride.price}</p>
                    <p className="text-xs text-muted-foreground">₹{ride.price / ride.route.distance} per km</p>
                  </div>

                  <Button className="w-full" asChild>
                    <Link href={`/checkout/${ride.id}`}>
                      Book Ride
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

