# Pull Request Template - AI-Powered Two-Wheeler Sharing Platform

## 📋 PR Type
<!-- Check the type of change your PR introduces -->
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Configuration change (feature flags, environment variables)
- [ ] ♻️ Code refactoring (no functional changes)
- [ ] 🧪 Test coverage improvement
- [ ] 🚀 Performance improvement

## 📝 Description
<!-- Provide a brief description of the changes -->

### What does this PR do?
<!-- Describe the changes in detail -->

### Why is this change needed?
<!-- Explain the motivation for this change -->

### How was this tested?
<!-- Describe the testing approach -->

## 🎯 Related Issues
<!-- Link to related GitHub issues or Linear tickets -->
- Closes #[issue_number]
- Related to #[issue_number]
- Linear: [ticket_link]

## 🔄 Changes Made

### Core Changes
<!-- List the main changes -->
- [ ] 

### Configuration Changes
<!-- List any configuration or feature flag changes -->
- [ ] Feature flags updated: 
- [ ] Environment variables added/modified:
- [ ] Database schema changes:

### Dependencies
<!-- List any new dependencies or version updates -->
- [ ] New dependencies added:
- [ ] Dependencies updated:
- [ ] Breaking dependency changes:

## 🧪 Testing

### Test Coverage
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] End-to-end tests added/updated
- [ ] Performance tests added/updated

### Test Results
<!-- Include test results or link to CI/CD pipeline -->
- [ ] All tests passing
- [ ] Test coverage: ___%
- [ ] Performance benchmarks met

### Manual Testing
<!-- Describe manual testing performed -->
- [ ] Feature tested in development environment
- [ ] Feature tested in staging environment
- [ ] Cross-browser testing completed (if applicable)
- [ ] Mobile testing completed (if applicable)

## 📊 Performance Impact

### Metrics
<!-- Include performance metrics if applicable -->
- [ ] No performance impact
- [ ] Performance improvement: 
- [ ] Performance regression (justified):

### Resource Usage
- [ ] Memory usage impact:
- [ ] CPU usage impact:
- [ ] Database query impact:
- [ ] Network usage impact:

## 🔒 Security Considerations

### Security Review
- [ ] No security implications
- [ ] Security review completed
- [ ] Authentication/authorization changes reviewed
- [ ] Data privacy considerations addressed

### Compliance
- [ ] GDPR compliance maintained
- [ ] SOC 2 compliance maintained
- [ ] Industry-specific compliance addressed

## 🌍 Deployment Considerations

### Environment Impact
- [ ] Development environment ready
- [ ] Staging environment ready
- [ ] Production environment ready
- [ ] Multi-region deployment considerations

### Feature Flags
<!-- List feature flags that control this functionality -->
- [ ] Feature flag: `feature_name` set to `true`
- [ ] Rollout percentage: __%
- [ ] Rollback plan documented

### Database Migrations
- [ ] No database changes
- [ ] Database migration included
- [ ] Migration tested in staging
- [ ] Rollback migration available

## 📚 Documentation

### Documentation Updates
- [ ] README updated
- [ ] API documentation updated
- [ ] Architecture documentation updated
- [ ] User documentation updated
- [ ] Deployment documentation updated

### Code Documentation
- [ ] Code comments added/updated
- [ ] Function/method documentation complete
- [ ] Type annotations added (if applicable)

## ✅ Checklist

### Code Quality
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Code is self-documenting with clear variable/function names
- [ ] No debugging code left in
- [ ] Error handling implemented appropriately

### Review Requirements
- [ ] Ready for review
- [ ] Requires architecture review
- [ ] Requires security review
- [ ] Requires performance review

### Deployment Readiness
- [ ] Feature flag configuration ready
- [ ] Monitoring and alerting configured
- [ ] Rollback plan documented
- [ ] Communication plan for stakeholders

## 🔍 Review Notes
<!-- Add any specific notes for reviewers -->

### Areas of Focus
<!-- Highlight specific areas that need careful review -->
- 

### Known Issues
<!-- List any known issues or limitations -->
- 

### Future Improvements
<!-- List potential future improvements -->
- 

## 📸 Screenshots/Videos
<!-- Add screenshots or videos if applicable -->

## 🚀 Post-Deployment Tasks
<!-- List tasks to be completed after deployment -->
- [ ] Monitor feature flag rollout
- [ ] Verify metrics and alerts
- [ ] Update stakeholders
- [ ] Schedule follow-up review

---

**Reviewer Guidelines:**
1. Check that all tests pass and coverage is adequate
2. Verify that feature flags are properly configured
3. Ensure documentation is complete and accurate
4. Validate security and performance considerations
5. Confirm deployment readiness and rollback plans
