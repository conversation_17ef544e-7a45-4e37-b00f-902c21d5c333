'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { MapPin, Clock, CreditCard, Calendar, Navigation, CheckCircle, ArrowRight, Zap, Shield, Star, Calculator } from 'lucide-react';
import { LoadingSpinner, RideLoading, ProgressBar } from '@/components/ui/loading';

interface FareEstimate {
  baseFare: number;
  distanceFare: number;
  timeFare: number;
  totalFare: number;
  estimatedDistance: number;
  estimatedDuration: number;
}

interface LocationData {
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  landmark?: string;
}

export default function RideBookingForm() {
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(false);
  const [fareEstimate, setFareEstimate] = useState<FareEstimate | null>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [formData, setFormData] = useState({
    pickupAddress: '',
    pickupLandmark: '',
    dropoffAddress: '',
    dropoffLandmark: '',
    rideType: 'standard' as 'standard' | 'premium' | 'shared',
    paymentMethod: 'cash' as 'cash' | 'card' | 'wallet' | 'upi',
    specialInstructions: '',
    isScheduled: false,
    scheduledTime: '',
  });

  // Mock function to get coordinates from address (in real app, use Google Maps API)
  const getCoordinatesFromAddress = async (address: string): Promise<{ latitude: number; longitude: number } | null> => {
    // Mock coordinates for demo - in production, integrate with Google Maps Geocoding API
    const mockCoordinates = {
      'andheri': { latitude: 19.1136, longitude: 72.8697 },
      'bandra': { latitude: 19.0596, longitude: 72.8295 },
      'mumbai': { latitude: 19.0760, longitude: 72.8777 },
      'pune': { latitude: 18.5204, longitude: 73.8567 },
    };

    const key = Object.keys(mockCoordinates).find(k => 
      address.toLowerCase().includes(k)
    );

    return key ? mockCoordinates[key as keyof typeof mockCoordinates] : 
           { latitude: 19.0760 + Math.random() * 0.1, longitude: 72.8777 + Math.random() * 0.1 };
  };

  // Calculate fare estimate
  const calculateFareEstimate = async () => {
    if (!formData.pickupAddress || !formData.dropoffAddress) return;

    setLoading(true);
    try {
      const pickupCoords = await getCoordinatesFromAddress(formData.pickupAddress);
      const dropoffCoords = await getCoordinatesFromAddress(formData.dropoffAddress);

      if (!pickupCoords || !dropoffCoords) {
        setError('Unable to find location coordinates');
        return;
      }

      // Calculate distance (mock calculation - use Google Maps Distance Matrix API in production)
      const distance = Math.sqrt(
        Math.pow(dropoffCoords.latitude - pickupCoords.latitude, 2) +
        Math.pow(dropoffCoords.longitude - pickupCoords.longitude, 2)
      ) * 111; // Rough conversion to km

      const estimatedDistance = Math.max(1, Math.round(distance * 10) / 10);
      const estimatedDuration = Math.round(estimatedDistance * 3); // Rough estimate: 3 min per km

      // Calculate fare based on ride type
      const baseFare = formData.rideType === 'premium' ? 50 : formData.rideType === 'shared' ? 20 : 30;
      const distanceRate = formData.rideType === 'premium' ? 15 : formData.rideType === 'shared' ? 8 : 10;
      const timeRate = formData.rideType === 'premium' ? 2 : formData.rideType === 'shared' ? 1 : 1.5;

      const distanceFare = estimatedDistance * distanceRate;
      const timeFare = estimatedDuration * timeRate;
      const totalFare = baseFare + distanceFare + timeFare;

      setFareEstimate({
        baseFare,
        distanceFare,
        timeFare,
        totalFare: Math.round(totalFare),
        estimatedDistance,
        estimatedDuration,
      });
    } catch (error) {
      setError('Error calculating fare estimate');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!fareEstimate) {
      setError('Please calculate fare estimate first');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const pickupCoords = await getCoordinatesFromAddress(formData.pickupAddress);
      const dropoffCoords = await getCoordinatesFromAddress(formData.dropoffAddress);

      const rideData = {
        pickupLocation: {
          address: formData.pickupAddress,
          coordinates: pickupCoords,
          landmark: formData.pickupLandmark,
        },
        dropoffLocation: {
          address: formData.dropoffAddress,
          coordinates: dropoffCoords,
          landmark: formData.dropoffLandmark,
        },
        estimatedDistance: fareEstimate.estimatedDistance,
        estimatedDuration: fareEstimate.estimatedDuration,
        rideType: formData.rideType,
        paymentMethod: formData.paymentMethod,
        specialInstructions: formData.specialInstructions,
        isScheduled: formData.isScheduled,
        scheduledTime: formData.isScheduled ? formData.scheduledTime : undefined,
      };

      const response = await fetch('/api/rides/book', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(rideData),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Ride booked successfully! Looking for nearby drivers...');
        // Reset form
        setFormData({
          pickupAddress: '',
          pickupLandmark: '',
          dropoffAddress: '',
          dropoffLandmark: '',
          rideType: 'standard',
          paymentMethod: 'cash',
          specialInstructions: '',
          isScheduled: false,
          scheduledTime: '',
        });
        setFareEstimate(null);
      } else {
        setError(data.error || 'Failed to book ride');
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError('');
    setSuccess('');
  };

  // Auto-calculate fare when pickup/dropoff changes
  useEffect(() => {
    if (formData.pickupAddress && formData.dropoffAddress) {
      const timer = setTimeout(calculateFareEstimate, 1000);
      return () => clearTimeout(timer);
    }
  }, [formData.pickupAddress, formData.dropoffAddress, formData.rideType]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Navigation className="w-5 h-5 mr-2" />
          Book a Ride
        </CardTitle>
        <CardDescription>
          Enter your pickup and destination to book a ride
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50">
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}

          {/* Location Inputs */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pickup">Pickup Location</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-green-500" />
                <Input
                  id="pickup"
                  placeholder="Enter pickup address"
                  value={formData.pickupAddress}
                  onChange={(e) => handleInputChange('pickupAddress', e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
              <Input
                placeholder="Landmark (optional)"
                value={formData.pickupLandmark}
                onChange={(e) => handleInputChange('pickupLandmark', e.target.value)}
                className="text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="dropoff">Destination</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-4 w-4 text-red-500" />
                <Input
                  id="dropoff"
                  placeholder="Enter destination address"
                  value={formData.dropoffAddress}
                  onChange={(e) => handleInputChange('dropoffAddress', e.target.value)}
                  className="pl-10"
                  required
                />
              </div>
              <Input
                placeholder="Landmark (optional)"
                value={formData.dropoffLandmark}
                onChange={(e) => handleInputChange('dropoffLandmark', e.target.value)}
                className="text-sm"
              />
            </div>
          </div>

          {/* Ride Options */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Ride Type</Label>
              <Select value={formData.rideType} onValueChange={(value) => handleInputChange('rideType', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                  <SelectItem value="shared">Shared</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Payment Method</Label>
              <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange('paymentMethod', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="card">Card</SelectItem>
                  <SelectItem value="wallet">Wallet</SelectItem>
                  <SelectItem value="upi">UPI</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Fare Estimate */}
          {fareEstimate && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="pt-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Fare Estimate</span>
                  <Badge variant="outline" className="text-blue-700">
                    {fareEstimate.estimatedDistance} km • {fareEstimate.estimatedDuration} min
                  </Badge>
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Base fare:</span>
                    <span>₹{fareEstimate.baseFare}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Distance fare:</span>
                    <span>₹{fareEstimate.distanceFare}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Time fare:</span>
                    <span>₹{fareEstimate.timeFare}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t pt-1">
                    <span>Total:</span>
                    <span>₹{fareEstimate.totalFare}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Special Instructions */}
          <div className="space-y-2">
            <Label htmlFor="instructions">Special Instructions (Optional)</Label>
            <Textarea
              id="instructions"
              placeholder="Any special instructions for the driver..."
              value={formData.specialInstructions}
              onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
              rows={3}
            />
          </div>

          {/* Schedule Option */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="schedule"
                checked={formData.isScheduled}
                onChange={(e) => handleInputChange('isScheduled', e.target.checked)}
                className="rounded"
              />
              <Label htmlFor="schedule">Schedule for later</Label>
            </div>

            {formData.isScheduled && (
              <div className="space-y-2">
                <Label htmlFor="scheduledTime">Scheduled Time</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="scheduledTime"
                    type="datetime-local"
                    value={formData.scheduledTime}
                    onChange={(e) => handleInputChange('scheduledTime', e.target.value)}
                    className="pl-10"
                    min={new Date().toISOString().slice(0, 16)}
                    required={formData.isScheduled}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={loading || !fareEstimate}
          >
            {loading ? 'Booking Ride...' : `Book Ride - ₹${fareEstimate?.totalFare || 0}`}
          </Button>
        </CardContent>
      </form>
    </Card>
  );
}
