"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CreditCard, Wallet, CheckCircle2, ArrowRight, Shield } from "lucide-react"

export default function CheckoutPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [paymentMethod, setPaymentMethod] = useState("wallet")
  const [isProcessing, setIsProcessing] = useState(false)

  // Sample ride data (would come from API in real app)
  const ride = {
    id: params.id,
    route: {
      start: "Andheri East",
      end: "Bandra Kurla Complex",
      distance: 12,
    },
    price: 60,
    serviceFee: 5,
    total: 65,
    driver: {
      name: "<PERSON><PERSON>",
      rating: 4.8,
    },
  }

  const handlePayment = () => {
    setIsProcessing(true)

    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false)
      router.push(`/payment-success/${ride.id}`)
    }, 2000)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Checkout</h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Payment Method</CardTitle>
                <CardDescription>Choose how you want to pay</CardDescription>
              </CardHeader>
              <CardContent>
                <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="space-y-3">
                  <div className="flex items-center space-x-2 border rounded-md p-4">
                    <RadioGroupItem value="wallet" id="wallet" />
                    <Label htmlFor="wallet" className="flex items-center flex-1">
                      <Wallet className="h-4 w-4 mr-2" />
                      <div className="flex-1">
                        <p>Wallet Balance</p>
                        <p className="text-sm text-muted-foreground">Available: ₹1,250</p>
                      </div>
                      <span className="text-green-600 font-medium">5% Cashback</span>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2 border rounded-md p-4">
                    <RadioGroupItem value="card" id="card" />
                    <Label htmlFor="card" className="flex items-center flex-1">
                      <CreditCard className="h-4 w-4 mr-2" />
                      <div className="flex-1">
                        <p>HDFC Bank Credit Card</p>
                        <p className="text-sm text-muted-foreground">••••4582</p>
                      </div>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2 border rounded-md p-4">
                    <RadioGroupItem value="upi" id="upi" />
                    <Label htmlFor="upi" className="flex items-center flex-1">
                      <img src="/placeholder.svg?height=16&width=16" alt="UPI" className="h-4 w-4 mr-2" />
                      <div className="flex-1">
                        <p>UPI</p>
                        <p className="text-sm text-muted-foreground">user@okbank</p>
                      </div>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2 border rounded-md p-4 border-dashed">
                    <RadioGroupItem value="new" id="new" />
                    <Label htmlFor="new" className="flex items-center">
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Add New Payment Method
                    </Label>
                  </div>
                </RadioGroup>

                {paymentMethod === "wallet" && (
                  <Alert className="mt-4 bg-green-50 text-green-800 border-green-200">
                    <CheckCircle2 className="h-4 w-4" />
                    <AlertTitle>Wallet Payment Selected</AlertTitle>
                    <AlertDescription>
                      You&apos;ll earn ₹3 cashback on this ride when paid through wallet.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Ride Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Route</span>
                    <span className="font-medium">{ride.route.distance} km</span>
                  </div>
                  <div className="text-sm">
                    {ride.route.start} to {ride.route.end}
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Ride Fare</span>
                    <span>₹{ride.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Service Fee</span>
                    <span>₹{ride.serviceFee}</span>
                  </div>

                  {paymentMethod === "wallet" && (
                    <div className="flex justify-between text-green-600">
                      <span>Cashback (5%)</span>
                      <span>-₹3</span>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="flex justify-between font-bold">
                  <span>Total</span>
                  <span>₹{ride.total}</span>
                </div>

                <div className="flex items-center justify-center text-xs text-muted-foreground gap-1 mt-2">
                  <Shield className="h-3 w-3" />
                  <span>Secure Payment</span>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full" onClick={handlePayment} disabled={isProcessing}>
                  {isProcessing ? "Processing..." : "Pay Now"}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

