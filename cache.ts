// Cache utilities for performance optimization

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of items in cache
  storage?: 'memory' | 'localStorage' | 'sessionStorage';
}

class Cache<T> {
  private cache = new Map<string, CacheItem<T>>();
  private maxSize: number;
  private defaultTTL: number;
  private storage: 'memory' | 'localStorage' | 'sessionStorage';

  constructor(options: CacheOptions = {}) {
    this.maxSize = options.maxSize || 100;
    this.defaultTTL = options.ttl || 5 * 60 * 1000; // 5 minutes default
    this.storage = options.storage || 'memory';
    
    // Load from persistent storage if available
    if (this.storage !== 'memory' && typeof window !== 'undefined') {
      this.loadFromStorage();
    }
  }

  set(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    };

    // Remove oldest item if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, item);
    this.saveToStorage();
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.saveToStorage();
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    const result = this.cache.delete(key);
    this.saveToStorage();
    return result;
  }

  clear(): void {
    this.cache.clear();
    this.saveToStorage();
  }

  size(): number {
    return this.cache.size;
  }

  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  // Get or set with async function
  async getOrSet(
    key: string, 
    fetcher: () => Promise<T>, 
    ttl?: number
  ): Promise<T> {
    const cached = this.get(key);
    if (cached !== null) {
      return cached;
    }

    const data = await fetcher();
    this.set(key, data, ttl);
    return data;
  }

  private loadFromStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const storage = this.getStorageObject();
      const cached = storage.getItem(`cache_${this.constructor.name}`);
      
      if (cached) {
        const parsed = JSON.parse(cached);
        this.cache = new Map(parsed);
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined' || this.storage === 'memory') return;

    try {
      const storage = this.getStorageObject();
      const serialized = JSON.stringify(Array.from(this.cache.entries()));
      storage.setItem(`cache_${this.constructor.name}`, serialized);
    } catch (error) {
      console.warn('Failed to save cache to storage:', error);
    }
  }

  private getStorageObject(): Storage {
    if (this.storage === 'localStorage') {
      return localStorage;
    } else if (this.storage === 'sessionStorage') {
      return sessionStorage;
    }
    throw new Error('Invalid storage type');
  }
}

// Specific cache instances
export const apiCache = new Cache<any>({
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 50,
  storage: 'sessionStorage',
});

export const userCache = new Cache<any>({
  ttl: 30 * 60 * 1000, // 30 minutes
  maxSize: 10,
  storage: 'localStorage',
});

export const locationCache = new Cache<any>({
  ttl: 60 * 60 * 1000, // 1 hour
  maxSize: 100,
  storage: 'localStorage',
});

export const imageCache = new Cache<string>({
  ttl: 24 * 60 * 60 * 1000, // 24 hours
  maxSize: 200,
  storage: 'localStorage',
});

// React hook for caching
import { useState, useEffect, useCallback } from 'react';

interface UseCacheOptions<T> {
  key: string;
  fetcher: () => Promise<T>;
  cache?: Cache<T>;
  ttl?: number;
  enabled?: boolean;
}

export function useCache<T>({
  key,
  fetcher,
  cache = apiCache,
  ttl,
  enabled = true,
}: UseCacheOptions<T>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      const result = await cache.getOrSet(key, fetcher, ttl);
      setData(result);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [key, fetcher, cache, ttl, enabled]);

  const invalidate = useCallback(() => {
    cache.delete(key);
    fetchData();
  }, [key, cache, fetchData]);

  const mutate = useCallback((newData: T) => {
    cache.set(key, newData, ttl);
    setData(newData);
  }, [key, cache, ttl]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    invalidate,
    mutate,
    refetch: fetchData,
  };
}

// API response caching wrapper
export async function cachedFetch<T>(
  url: string,
  options: RequestInit = {},
  cacheOptions: { ttl?: number; cache?: Cache<T> } = {}
): Promise<T> {
  const { ttl, cache = apiCache } = cacheOptions;
  const cacheKey = `${url}_${JSON.stringify(options)}`;

  return cache.getOrSet(
    cacheKey,
    async () => {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    },
    ttl
  );
}

// Debounced cache setter
export function debouncedCacheSet<T>(
  cache: Cache<T>,
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout;

  return (key: string, data: T, ttl?: number) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      cache.set(key, data, ttl);
    }, delay);
  };
}

// Cache invalidation patterns
export class CacheInvalidator {
  private patterns = new Map<string, RegExp>();

  addPattern(name: string, pattern: RegExp): void {
    this.patterns.set(name, pattern);
  }

  invalidate(cache: Cache<any>, patternName: string): void {
    const pattern = this.patterns.get(patternName);
    if (!pattern) return;

    const keysToDelete = cache.keys().filter(key => pattern.test(key));
    keysToDelete.forEach(key => cache.delete(key));
  }

  invalidateAll(cache: Cache<any>): void {
    cache.clear();
  }
}

export const cacheInvalidator = new CacheInvalidator();

// Common invalidation patterns
cacheInvalidator.addPattern('user', /^user_/);
cacheInvalidator.addPattern('rides', /^rides_/);
cacheInvalidator.addPattern('vehicles', /^vehicles_/);
cacheInvalidator.addPattern('delivery', /^delivery_/);
cacheInvalidator.addPattern('corporate', /^corporate_/);

// Cache warming utilities
export async function warmCache<T>(
  cache: Cache<T>,
  entries: Array<{ key: string; fetcher: () => Promise<T>; ttl?: number }>
): Promise<void> {
  const promises = entries.map(async ({ key, fetcher, ttl }) => {
    try {
      if (!cache.has(key)) {
        const data = await fetcher();
        cache.set(key, data, ttl);
      }
    } catch (error) {
      console.warn(`Failed to warm cache for key ${key}:`, error);
    }
  });

  await Promise.allSettled(promises);
}

// Cache statistics
export function getCacheStats(cache: Cache<any>) {
  const keys = cache.keys();
  const now = Date.now();
  
  let expired = 0;
  let valid = 0;
  
  keys.forEach(key => {
    const item = (cache as any).cache.get(key);
    if (item && now - item.timestamp > item.ttl) {
      expired++;
    } else {
      valid++;
    }
  });

  return {
    total: keys.length,
    valid,
    expired,
    hitRate: valid / (valid + expired) || 0,
  };
}

// Preload critical resources
export function preloadCriticalData() {
  if (typeof window === 'undefined') return;

  // Preload user data if authenticated
  const token = localStorage.getItem('token');
  if (token) {
    warmCache(userCache, [
      {
        key: 'current_user',
        fetcher: () => cachedFetch('/api/auth/me', {
          headers: { Authorization: `Bearer ${token}` }
        }),
        ttl: 30 * 60 * 1000, // 30 minutes
      },
    ]);
  }

  // Preload location data
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        warmCache(locationCache, [
          {
            key: `nearby_vehicles_${latitude}_${longitude}`,
            fetcher: () => cachedFetch(`/api/vehicles/nearby?lat=${latitude}&lng=${longitude}`),
            ttl: 5 * 60 * 1000, // 5 minutes
          },
        ]);
      },
      (error) => {
        console.warn('Geolocation preload failed:', error);
      }
    );
  }
}
