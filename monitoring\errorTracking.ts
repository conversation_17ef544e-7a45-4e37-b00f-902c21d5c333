// Error Tracking and Alerting Service
import { apmService } from './apm';

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  userAgent?: string;
  url?: string;
  method?: string;
  statusCode?: number;
  timestamp: number;
  environment: string;
  service: string;
  version?: string;
}

export interface ErrorData {
  message: string;
  stack?: string;
  type: string;
  level: 'error' | 'warning' | 'info' | 'debug';
  context: ErrorContext;
  fingerprint?: string;
  tags?: Record<string, string>;
  extra?: Record<string, any>;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: {
    metric: string;
    operator: '>' | '<' | '=' | '>=' | '<=';
    threshold: number;
    timeWindow: number; // in minutes
  };
  channels: ('email' | 'slack' | 'webhook')[];
  enabled: boolean;
}

class ErrorTrackingService {
  private errors: ErrorData[] = [];
  private alertRules: AlertRule[] = [];
  private isEnabled: boolean = process.env.NODE_ENV === 'production';
  private errorCounts: Map<string, number> = new Map();

  constructor() {
    this.setupDefaultAlertRules();
    this.startErrorCountReset();
  }

  /**
   * Setup default alert rules
   */
  private setupDefaultAlertRules() {
    this.alertRules = [
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        condition: {
          metric: 'error_rate',
          operator: '>',
          threshold: 5, // 5% error rate
          timeWindow: 5, // 5 minutes
        },
        channels: ['email', 'slack'],
        enabled: true,
      },
      {
        id: 'payment_failures',
        name: 'Payment Failures',
        condition: {
          metric: 'payment_errors',
          operator: '>',
          threshold: 3, // 3 payment errors
          timeWindow: 10, // 10 minutes
        },
        channels: ['email', 'slack', 'webhook'],
        enabled: true,
      },
      {
        id: 'database_errors',
        name: 'Database Connection Errors',
        condition: {
          metric: 'database_errors',
          operator: '>',
          threshold: 1, // 1 database error
          timeWindow: 5, // 5 minutes
        },
        channels: ['email', 'slack'],
        enabled: true,
      },
      {
        id: 'api_response_time',
        name: 'High API Response Time',
        condition: {
          metric: 'avg_response_time',
          operator: '>',
          threshold: 5000, // 5 seconds
          timeWindow: 5, // 5 minutes
        },
        channels: ['slack'],
        enabled: true,
      },
    ];
  }

  /**
   * Reset error counts periodically
   */
  private startErrorCountReset() {
    setInterval(() => {
      this.errorCounts.clear();
    }, 5 * 60 * 1000); // Reset every 5 minutes
  }

  /**
   * Capture and track an error
   */
  captureError(
    error: Error | string,
    context: Partial<ErrorContext> = {},
    level: 'error' | 'warning' | 'info' | 'debug' = 'error'
  ) {
    if (!this.isEnabled) return;

    const errorMessage = typeof error === 'string' ? error : error.message;
    const stack = typeof error === 'object' && error.stack ? error.stack : undefined;
    
    const errorData: ErrorData = {
      message: errorMessage,
      stack,
      type: typeof error === 'object' ? error.constructor.name : 'Error',
      level,
      context: {
        timestamp: Date.now(),
        environment: process.env.NODE_ENV || 'development',
        service: 'rideshare-platform',
        version: process.env.APP_VERSION || '1.0.0',
        ...context,
      },
      fingerprint: this.generateFingerprint(errorMessage, stack),
      tags: this.extractTags(context),
      extra: this.extractExtra(context),
    };

    // Store error
    this.errors.push(errorData);
    
    // Keep only last 1000 errors in memory
    if (this.errors.length > 1000) {
      this.errors = this.errors.slice(-1000);
    }

    // Update error counts for alerting
    this.updateErrorCounts(errorData);

    // Send to external error tracking service
    this.sendToErrorService(errorData);

    // Record in APM
    apmService.recordError(errorData);

    // Check alert rules
    this.checkAlertRules(errorData);

    return errorData.fingerprint;
  }

  /**
   * Capture exception with automatic context
   */
  captureException(error: Error, additionalContext?: Record<string, any>) {
    const context: Partial<ErrorContext> = {
      ...additionalContext,
    };

    // Add browser context if available
    if (typeof window !== 'undefined') {
      context.userAgent = window.navigator.userAgent;
      context.url = window.location.href;
    }

    return this.captureError(error, context, 'error');
  }

  /**
   * Capture message with context
   */
  captureMessage(
    message: string,
    level: 'error' | 'warning' | 'info' | 'debug' = 'info',
    context?: Record<string, any>
  ) {
    return this.captureError(message, context, level);
  }

  /**
   * Set user context for subsequent errors
   */
  setUserContext(userId: string, email?: string, username?: string) {
    // This would typically be stored in a context that persists across requests
    if (typeof window !== 'undefined') {
      (window as any).__errorTrackingUserContext = {
        userId,
        email,
        username,
      };
    }
  }

  /**
   * Set request context
   */
  setRequestContext(requestId: string, method: string, url: string) {
    if (typeof window !== 'undefined') {
      (window as any).__errorTrackingRequestContext = {
        requestId,
        method,
        url,
      };
    }
  }

  /**
   * Generate error fingerprint for grouping
   */
  private generateFingerprint(message: string, stack?: string): string {
    // Simple fingerprinting based on error message and first stack frame
    const stackLine = stack?.split('\n')[1] || '';
    const combined = `${message}:${stackLine}`;
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Extract tags from context
   */
  private extractTags(context: Partial<ErrorContext>): Record<string, string> {
    const tags: Record<string, string> = {};
    
    if (context.userId) tags.userId = context.userId;
    if (context.method) tags.method = context.method;
    if (context.statusCode) tags.statusCode = context.statusCode.toString();
    if (context.environment) tags.environment = context.environment;
    
    return tags;
  }

  /**
   * Extract extra data from context
   */
  private extractExtra(context: Partial<ErrorContext>): Record<string, any> {
    const extra: Record<string, any> = {};
    
    if (context.userAgent) extra.userAgent = context.userAgent;
    if (context.url) extra.url = context.url;
    if (context.requestId) extra.requestId = context.requestId;
    if (context.sessionId) extra.sessionId = context.sessionId;
    
    return extra;
  }

  /**
   * Update error counts for alerting
   */
  private updateErrorCounts(errorData: ErrorData) {
    const key = `${errorData.type}_${errorData.level}`;
    const currentCount = this.errorCounts.get(key) || 0;
    this.errorCounts.set(key, currentCount + 1);

    // Update specific error type counts
    if (errorData.message.includes('payment')) {
      const paymentKey = 'payment_errors';
      const paymentCount = this.errorCounts.get(paymentKey) || 0;
      this.errorCounts.set(paymentKey, paymentCount + 1);
    }

    if (errorData.message.includes('database') || errorData.message.includes('mongo')) {
      const dbKey = 'database_errors';
      const dbCount = this.errorCounts.get(dbKey) || 0;
      this.errorCounts.set(dbKey, dbCount + 1);
    }
  }

  /**
   * Check alert rules and trigger alerts
   */
  private async checkAlertRules(errorData: ErrorData) {
    for (const rule of this.alertRules) {
      if (!rule.enabled) continue;

      const metricValue = this.getMetricValue(rule.condition.metric);
      const threshold = rule.condition.threshold;

      let shouldAlert = false;
      switch (rule.condition.operator) {
        case '>':
          shouldAlert = metricValue > threshold;
          break;
        case '<':
          shouldAlert = metricValue < threshold;
          break;
        case '>=':
          shouldAlert = metricValue >= threshold;
          break;
        case '<=':
          shouldAlert = metricValue <= threshold;
          break;
        case '=':
          shouldAlert = metricValue === threshold;
          break;
      }

      if (shouldAlert) {
        await this.triggerAlert(rule, metricValue, errorData);
      }
    }
  }

  /**
   * Get metric value for alert rules
   */
  private getMetricValue(metric: string): number {
    switch (metric) {
      case 'error_rate':
        // Calculate error rate as percentage
        const totalErrors = Array.from(this.errorCounts.values()).reduce((a, b) => a + b, 0);
        const totalRequests = 100; // This would come from request tracking
        return totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
      
      case 'payment_errors':
        return this.errorCounts.get('payment_errors') || 0;
      
      case 'database_errors':
        return this.errorCounts.get('database_errors') || 0;
      
      case 'avg_response_time':
        // This would come from performance metrics
        return 1000; // Placeholder
      
      default:
        return 0;
    }
  }

  /**
   * Trigger alert through configured channels
   */
  private async triggerAlert(rule: AlertRule, metricValue: number, errorData: ErrorData) {
    const alertMessage = `🚨 Alert: ${rule.name}\n` +
      `Metric: ${rule.condition.metric}\n` +
      `Current Value: ${metricValue}\n` +
      `Threshold: ${rule.condition.threshold}\n` +
      `Time: ${new Date().toISOString()}\n` +
      `Error: ${errorData.message}`;

    for (const channel of rule.channels) {
      try {
        switch (channel) {
          case 'email':
            await this.sendEmailAlert(alertMessage, rule);
            break;
          case 'slack':
            await this.sendSlackAlert(alertMessage, rule);
            break;
          case 'webhook':
            await this.sendWebhookAlert(alertMessage, rule, metricValue, errorData);
            break;
        }
      } catch (error) {
        console.error(`Failed to send alert via ${channel}:`, error);
      }
    }
  }

  /**
   * Send email alert
   */
  private async sendEmailAlert(message: string, rule: AlertRule) {
    if (process.env.EMAIL_ALERT_ENDPOINT) {
      await fetch(process.env.EMAIL_ALERT_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: process.env.ALERT_EMAIL,
          subject: `RideShare Alert: ${rule.name}`,
          body: message,
        }),
      });
    }
  }

  /**
   * Send Slack alert
   */
  private async sendSlackAlert(message: string, rule: AlertRule) {
    if (process.env.SLACK_WEBHOOK_URL) {
      await fetch(process.env.SLACK_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: message,
          channel: process.env.SLACK_ALERT_CHANNEL || '#alerts',
          username: 'RideShare Monitor',
          icon_emoji: ':warning:',
        }),
      });
    }
  }

  /**
   * Send webhook alert
   */
  private async sendWebhookAlert(
    message: string,
    rule: AlertRule,
    metricValue: number,
    errorData: ErrorData
  ) {
    if (process.env.ALERT_WEBHOOK_URL) {
      await fetch(process.env.ALERT_WEBHOOK_URL, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          alert: {
            rule: rule.name,
            metric: rule.condition.metric,
            value: metricValue,
            threshold: rule.condition.threshold,
            message,
            error: errorData,
            timestamp: Date.now(),
          },
        }),
      });
    }
  }

  /**
   * Send error to external service
   */
  private async sendToErrorService(errorData: ErrorData) {
    try {
      if (process.env.SENTRY_DSN) {
        // Send to Sentry or similar service
        await fetch(`${process.env.ERROR_TRACKING_ENDPOINT}/api/errors`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ERROR_TRACKING_API_KEY}`,
          },
          body: JSON.stringify(errorData),
        });
      }
    } catch (error) {
      console.error('Failed to send error to tracking service:', error);
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    const recentErrors = this.errors.filter(e => e.context.timestamp > oneHourAgo);
    const dailyErrors = this.errors.filter(e => e.context.timestamp > oneDayAgo);

    return {
      total: this.errors.length,
      lastHour: recentErrors.length,
      lastDay: dailyErrors.length,
      errorCounts: Object.fromEntries(this.errorCounts),
      recentErrors: recentErrors.slice(-10),
    };
  }

  /**
   * Enable or disable error tracking
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }
}

// Export singleton instance
export const errorTrackingService = new ErrorTrackingService();
export default errorTrackingService;
