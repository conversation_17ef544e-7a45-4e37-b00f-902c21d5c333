'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  ArrowRight, 
  ArrowLeft, 
  X, 
  MapPin, 
  Car, 
  MessageCircle, 
  Bell, 
  Star,
  Shield,
  Zap,
  CheckCircle
} from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  target?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: string;
}

interface OnboardingTourProps {
  userRole: 'rider' | 'driver';
  onComplete: () => void;
  onSkip: () => void;
}

const riderSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to RideShare! 🎉',
    description: 'Let\'s take a quick tour to help you get started with booking affordable rides.',
    icon: Star,
    action: 'Get Started'
  },
  {
    id: 'book-ride',
    title: 'Book Your First Ride',
    description: 'Tap "Book Ride" to start. Enter your pickup and destination to find nearby drivers.',
    icon: MapPin,
    target: '[data-tour="book-ride"]',
    position: 'bottom'
  },
  {
    id: 'real-time',
    title: 'Real-time Tracking',
    description: 'Track your driver in real-time, chat with them, and get live updates on your ride.',
    icon: Car,
    target: '[data-tour="tracking"]',
    position: 'left'
  },
  {
    id: 'notifications',
    title: 'Stay Updated',
    description: 'Get instant notifications about ride requests, driver arrivals, and trip updates.',
    icon: Bell,
    target: '[data-tour="notifications"]',
    position: 'bottom'
  },
  {
    id: 'safety',
    title: 'Your Safety First',
    description: 'All drivers are verified. Use emergency alerts and share your trip with contacts.',
    icon: Shield,
    action: 'Learn More'
  },
  {
    id: 'complete',
    title: 'You\'re All Set! ✨',
    description: 'Start booking rides and enjoy affordable, safe transportation. Need help? Check our support section.',
    icon: CheckCircle,
    action: 'Start Riding'
  }
];

const driverSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome, Driver! 🚗',
    description: 'Let\'s show you how to start earning with RideShare. You can make ₹1,800+ monthly!',
    icon: Star,
    action: 'Get Started'
  },
  {
    id: 'dashboard',
    title: 'Your Driver Dashboard',
    description: 'Monitor ride requests, track earnings, and manage your availability from here.',
    icon: Car,
    target: '[data-tour="dashboard"]',
    position: 'top'
  },
  {
    id: 'requests',
    title: 'Ride Requests',
    description: 'Accept or decline ride requests. You have 2 minutes to respond to each request.',
    icon: Zap,
    target: '[data-tour="requests"]',
    position: 'right'
  },
  {
    id: 'communication',
    title: 'Chat with Riders',
    description: 'Communicate with riders through our secure in-app chat system.',
    icon: MessageCircle,
    target: '[data-tour="chat"]',
    position: 'left'
  },
  {
    id: 'earnings',
    title: 'Track Your Earnings',
    description: 'Earn ₹3 per km. View daily, weekly, and monthly earnings in your dashboard.',
    icon: Star,
    action: 'View Earnings'
  },
  {
    id: 'complete',
    title: 'Ready to Drive! 🎯',
    description: 'Go online to start receiving ride requests. Drive safe and earn well!',
    icon: CheckCircle,
    action: 'Go Online'
  }
];

export default function OnboardingTour({ userRole, onComplete, onSkip }: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [highlightElement, setHighlightElement] = useState<HTMLElement | null>(null);

  const steps = userRole === 'rider' ? riderSteps : driverSteps;
  const step = steps[currentStep];

  useEffect(() => {
    if (step.target) {
      const element = document.querySelector(step.target) as HTMLElement;
      if (element) {
        setHighlightElement(element);
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Add highlight class
        element.classList.add('onboarding-highlight');
        
        return () => {
          element.classList.remove('onboarding-highlight');
        };
      }
    } else {
      setHighlightElement(null);
    }
  }, [currentStep, step.target]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    onComplete();
  };

  const handleSkip = () => {
    setIsVisible(false);
    onSkip();
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-40" />
      
      {/* Highlight overlay for targeted elements */}
      {highlightElement && (
        <div 
          className="fixed z-50 pointer-events-none"
          style={{
            top: highlightElement.offsetTop - 8,
            left: highlightElement.offsetLeft - 8,
            width: highlightElement.offsetWidth + 16,
            height: highlightElement.offsetHeight + 16,
            border: '3px solid hsl(var(--primary))',
            borderRadius: '12px',
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
          }}
        />
      )}

      {/* Tour Dialog */}
      <Dialog open={isVisible} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md z-50" hideCloseButton>
          <DialogHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="bg-gradient-primary p-2 rounded-lg">
                  <step.icon className="h-5 w-5 text-white" />
                </div>
                <Badge variant="outline">
                  {currentStep + 1} of {steps.length}
                </Badge>
              </div>
              <Button variant="ghost" size="sm" onClick={handleSkip}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <DialogTitle className="text-left text-xl">{step.title}</DialogTitle>
            <DialogDescription className="text-left text-base">
              {step.description}
            </DialogDescription>
          </DialogHeader>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              className="bg-gradient-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              {currentStep > 0 && (
                <Button variant="outline" onClick={handlePrevious}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}
            </div>
            
            <div className="flex space-x-2">
              <Button variant="ghost" onClick={handleSkip}>
                Skip Tour
              </Button>
              <Button onClick={handleNext} className="bg-gradient-primary">
                {step.action || (currentStep === steps.length - 1 ? 'Finish' : 'Next')}
                {currentStep < steps.length - 1 && <ArrowRight className="h-4 w-4 ml-2" />}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* CSS for highlighting */}
      <style jsx global>{`
        .onboarding-highlight {
          position: relative;
          z-index: 51;
          border-radius: 8px;
          animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
          0%, 100% {
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
          }
          50% {
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
          }
        }
      `}</style>
    </>
  );
}

// Hook to manage onboarding state
export function useOnboarding() {
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);

  useEffect(() => {
    const completed = localStorage.getItem('onboarding-completed');
    if (!completed) {
      setShowOnboarding(true);
    } else {
      setHasCompletedOnboarding(true);
    }
  }, []);

  const completeOnboarding = () => {
    localStorage.setItem('onboarding-completed', 'true');
    setShowOnboarding(false);
    setHasCompletedOnboarding(true);
  };

  const skipOnboarding = () => {
    localStorage.setItem('onboarding-skipped', 'true');
    setShowOnboarding(false);
  };

  const resetOnboarding = () => {
    localStorage.removeItem('onboarding-completed');
    localStorage.removeItem('onboarding-skipped');
    setShowOnboarding(true);
    setHasCompletedOnboarding(false);
  };

  return {
    showOnboarding,
    hasCompletedOnboarding,
    completeOnboarding,
    skipOnboarding,
    resetOnboarding,
  };
}
