import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { deliveryManagementService } from '@/lib/delivery/deliveryManagementService';

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      type,
      priority,
      pickupLocation,
      deliveryLocation,
      items,
      pickupTimeWindow,
      deliveryTimeWindow,
      requiresSignature,
      requiresID,
      cashOnDelivery,
      specialInstructions
    } = body;

    // Validate required fields
    if (!type || !pickupLocation || !deliveryLocation || !items || !Array.isArray(items)) {
      return NextResponse.json(
        { error: 'Missing required fields: type, pickupLocation, deliveryLocation, items' },
        { status: 400 }
      );
    }

    // Validate locations
    if (!pickupLocation.latitude || !pickupLocation.longitude || !pickupLocation.address) {
      return NextResponse.json(
        { error: 'Invalid pickup location. Latitude, longitude, and address are required' },
        { status: 400 }
      );
    }

    if (!deliveryLocation.latitude || !deliveryLocation.longitude || !deliveryLocation.address) {
      return NextResponse.json(
        { error: 'Invalid delivery location. Latitude, longitude, and address are required' },
        { status: 400 }
      );
    }

    // Validate items
    for (const item of items) {
      if (!item.name || !item.category || typeof item.weight !== 'number' || typeof item.value !== 'number') {
        return NextResponse.json(
          { error: 'Invalid item format. Each item must have name, category, weight, and value' },
          { status: 400 }
        );
      }
    }

    // Create delivery request
    const deliveryRequest = await deliveryManagementService.createDeliveryRequest({
      type,
      priority: priority || 'standard',
      senderId: decoded.userId,
      pickupLocation: {
        ...pickupLocation,
        contactName: pickupLocation.contactName || 'Sender',
        contactPhone: pickupLocation.contactPhone || '+91-0000000000',
      },
      deliveryLocation: {
        ...deliveryLocation,
        contactName: deliveryLocation.contactName || 'Recipient',
        contactPhone: deliveryLocation.contactPhone || '+91-0000000000',
      },
      items,
      pickupTimeWindow: pickupTimeWindow ? {
        start: new Date(pickupTimeWindow.start),
        end: new Date(pickupTimeWindow.end),
      } : undefined,
      deliveryTimeWindow: deliveryTimeWindow ? {
        start: new Date(deliveryTimeWindow.start),
        end: new Date(deliveryTimeWindow.end),
      } : undefined,
      requiresSignature: requiresSignature || false,
      requiresID: requiresID || false,
      cashOnDelivery,
    });

    // Find available drivers
    const availableDrivers = await deliveryManagementService.findAvailableDrivers(deliveryRequest.id);

    const response = {
      success: true,
      data: {
        deliveryRequest: {
          id: deliveryRequest.id,
          trackingCode: deliveryRequest.trackingCode,
          type: deliveryRequest.type,
          priority: deliveryRequest.priority,
          status: deliveryRequest.status,
          estimatedDeliveryTime: deliveryRequest.estimatedDeliveryTime,
          estimatedDuration: deliveryRequest.estimatedDuration,
          pricing: deliveryRequest.pricing,
          pickupLocation: deliveryRequest.pickupLocation,
          deliveryLocation: deliveryRequest.deliveryLocation,
          items: deliveryRequest.items,
          createdAt: deliveryRequest.createdAt,
        },
        availableDrivers: availableDrivers.map(driver => ({
          id: driver.id,
          vehicleType: driver.vehicleType,
          rating: driver.rating,
          completedDeliveries: driver.completedDeliveries,
          estimatedArrival: (driver as any).estimatedArrival,
          specializations: driver.specializations,
        })),
        nextSteps: [
          'Your delivery request has been created',
          `Tracking code: ${deliveryRequest.trackingCode}`,
          `${availableDrivers.length} drivers available in your area`,
          'A driver will be assigned shortly',
        ],
      },
      metadata: {
        requestId: deliveryRequest.id,
        userId: decoded.userId,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Create delivery request API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create delivery request',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization token required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    const token = authHeader.substring(7);
    let decoded: any;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const type = searchParams.get('type');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get delivery analytics (simplified for demo)
    const analytics = await deliveryManagementService.getDeliveryAnalytics();

    // Mock user's delivery requests (in real app, would filter by user)
    const mockDeliveries = [
      {
        id: 'del_001',
        trackingCode: 'TRK123456',
        type: 'package',
        priority: 'standard',
        status: 'in_transit',
        estimatedDeliveryTime: new Date(Date.now() + 45 * 60 * 1000),
        pricing: { total: 85 },
        pickupLocation: { address: '123 Pickup Street, Bangalore' },
        deliveryLocation: { address: '456 Delivery Avenue, Bangalore' },
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      },
      {
        id: 'del_002',
        trackingCode: 'TRK789012',
        type: 'food',
        priority: 'express',
        status: 'delivered',
        estimatedDeliveryTime: new Date(Date.now() - 30 * 60 * 1000),
        actualDeliveryTime: new Date(Date.now() - 25 * 60 * 1000),
        pricing: { total: 120 },
        pickupLocation: { address: 'Spice Garden Restaurant, MG Road' },
        deliveryLocation: { address: '789 Home Street, Bangalore' },
        createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
        rating: 5,
      },
    ];

    // Apply filters
    let filteredDeliveries = mockDeliveries;
    if (status) {
      filteredDeliveries = filteredDeliveries.filter(d => d.status === status);
    }
    if (type) {
      filteredDeliveries = filteredDeliveries.filter(d => d.type === type);
    }

    // Apply pagination
    const paginatedDeliveries = filteredDeliveries.slice(offset, offset + limit);

    const response = {
      success: true,
      data: {
        deliveries: paginatedDeliveries,
        pagination: {
          total: filteredDeliveries.length,
          limit,
          offset,
          hasMore: offset + limit < filteredDeliveries.length,
        },
        summary: {
          totalDeliveries: analytics.summary.totalDeliveries,
          activeDeliveries: analytics.summary.activeDeliveries,
          completedDeliveries: analytics.summary.completedDeliveries,
          completionRate: analytics.summary.completionRate,
        },
        filters: {
          availableStatuses: ['pending', 'confirmed', 'pickup_assigned', 'picked_up', 'in_transit', 'delivered', 'cancelled'],
          availableTypes: ['package', 'food', 'grocery', 'pharmacy', 'b2b', 'crowd_sourced'],
        },
      },
      metadata: {
        userId: decoded.userId,
        timestamp: Date.now(),
      },
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get delivery requests API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch delivery requests',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
