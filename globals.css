@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s infinite;
  }

  .animate-bounce-soft {
    animation: bounceSoft 1s ease-in-out;
  }

  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }

  .bg-gradient-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Custom shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulseSoft {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes bounceSoft {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

/* Accessibility Enhancements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High Contrast Mode */
.high-contrast {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 0 0% 10%;
  --card-foreground: 0 0% 100%;
  --primary: 60 100% 50%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 20%;
  --secondary-foreground: 0 0% 100%;
  --border: 0 0% 50%;
  --input: 0 0% 20%;
}

.high-contrast * {
  border-color: hsl(var(--border)) !important;
}

.high-contrast button:focus,
.high-contrast input:focus,
.high-contrast select:focus,
.high-contrast textarea:focus {
  outline: 3px solid hsl(var(--primary)) !important;
  outline-offset: 2px !important;
}

/* Large Text Mode */
.large-text {
  font-size: 120%;
}

.large-text h1 { font-size: 3rem; }
.large-text h2 { font-size: 2.5rem; }
.large-text h3 { font-size: 2rem; }
.large-text p, .large-text span { font-size: 1.25rem; }
.large-text button { font-size: 1.25rem; padding: 0.75rem 1.5rem; }

/* Reduced Motion Mode */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Focus Indicators */
*:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Keyboard Navigation Helpers */
.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation select:focus,
.keyboard-navigation textarea:focus {
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.3);
}

/* Touch Target Improvements */
@media (pointer: coarse) {
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }
}

@layer base {
  :root {
    /* Enhanced color palette for two-wheeler sharing */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Primary: Modern blue gradient */
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;

    /* Secondary: Soft gray */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 84% 4.9%;

    /* Muted: Light gray */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    /* Accent: Vibrant orange for CTAs */
    --accent: 24 95% 53%;
    --accent-foreground: 210 40% 98%;

    /* Success: Green for completed rides */
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    /* Warning: Yellow for pending states */
    --warning: 38 92% 50%;
    --warning-foreground: 222 84% 4.9%;

    /* Destructive: Red for errors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    /* Border and input */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;

    /* Chart colors for analytics */
    --chart-1: 221 83% 53%;
    --chart-2: 142 76% 36%;
    --chart-3: 24 95% 53%;
    --chart-4: 38 92% 50%;
    --chart-5: 0 84% 60%;

    /* Enhanced radius for modern look */
    --radius: 0.75rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 221 83% 53%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222 84% 4.9%;
    --sidebar-border: 214 32% 91%;
    --sidebar-ring: 221 83% 53%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
